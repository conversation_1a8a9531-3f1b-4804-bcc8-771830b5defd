---
type: "always_apply"
---

# PostgreSQL协议测试开发规则

本文档包含在开发PostgreSQL协议测试场景时必须遵循的最佳实践和规则，旨在确保生成的pcap文件格式正确，能够在Wireshark等工具中正确显示和解析。

## 1. 消息长度计算规则

### 1.1 动态长度计算（必须遵循）

**规则**：始终使用动态计算而非硬编码消息长度。

**错误示例**：
```python
# ❌ 错误：硬编码长度值
set_encoding_query = b"Q" + struct.pack("!I", 30) + b"SET client_encoding = 'UTF8';\0"
utf8_query = b"Q" + struct.pack("!I", 85) + b"SELECT 'text';\0"
```

**正确示例**：
```python
# ✅ 正确：动态计算长度
sql_text = b"SET client_encoding = 'UTF8';\0"
set_encoding_query = b"Q" + struct.pack("!I", len(sql_text) + 4) + sql_text

query_sql = b"SELECT 'text';\0"
query = b"Q" + struct.pack("!I", len(query_sql) + 4) + query_sql
```

**原理说明**：PostgreSQL协议中，消息长度字段包含长度字段本身的4字节，但不包含消息类型标识符。硬编码长度容易导致长度不匹配，使Wireshark无法正确解析消息边界。

### 1.2 CommandComplete消息长度计算

**规则**：CommandComplete消息的长度必须包含字符串内容加上长度字段本身。

**错误示例**：
```python
# ❌ 错误：长度计算不正确
cmd_complete = b"C" + struct.pack("!I", 7) + b"SET\0"  # "SET\0"实际长度是4，不是3
```

**正确示例**：
```python
# ✅ 正确：动态计算CommandComplete长度
cmd_complete_text = b"SET\0"
cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text

cmd_complete_text = b"SELECT 1\0"
cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
```

## 2. struct.pack格式一致性规则

### 2.1 RowDescription格式统一

**规则**：在同一个RowDescription消息中，所有字段必须使用相同的struct.pack格式字符串。

**错误示例**：
```python
# ❌ 错误：混合使用不同格式
row_desc_data += b"int4\0" + struct.pack("!IHIHIH", 0, 0, 23, 4, 4, 0)      # 使用H
row_desc_data += b"text\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)    # 使用h
```

**正确示例**：
```python
# ✅ 正确：统一使用有符号格式
row_desc_data += b"int4\0" + struct.pack("!IHIhih", 0, 0, 23, 4, 4, 0)
row_desc_data += b"text\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
row_desc_data += b"json\0" + struct.pack("!IHIhih", 0, 0, 114, -1, -1, 0)
```

**原理说明**：PostgreSQL协议中的RowDescription字段使用有符号整数，-1表示变长字段。格式不一致会导致字段值被错误解释。

### 2.2 有符号/无符号整数选择

**规则**：根据PostgreSQL协议规范选择正确的整数类型。

- 长度字段：使用无符号整数 (`!I`, `!H`)
- 数据类型OID：使用无符号整数 (`!I`)
- 字段长度（可能为-1）：使用有符号整数 (`!i`, `!h`)

```python
# ✅ 正确的格式选择
message_length = struct.pack("!I", length)      # 消息长度：无符号
field_count = struct.pack("!H", count)          # 字段数量：无符号
type_oid = struct.pack("!I", oid)               # 类型OID：无符号
field_length = struct.pack("!h", -1)            # 字段长度：有符号（-1表示变长）
```

## 3. 字符串处理规则

### 3.1 终止符处理

**规则**：PostgreSQL协议中的字符串必须以NULL终止符（\0）结尾，且终止符必须计算在长度内。

**正确示例**：
```python
# ✅ 正确：包含终止符的字符串处理
sql_string = b"SELECT * FROM users;\0"  # 包含终止符
query_msg = b"Q" + struct.pack("!I", len(sql_string) + 4) + sql_string

param_name = b"client_encoding\0"
param_value = b"UTF8\0"
param_data = param_name + param_value
param_status = b"S" + struct.pack("!I", len(param_data) + 4) + param_data
```

### 3.2 多字节字符处理

**规则**：处理UTF-8等多字节字符时，使用字节长度而非字符长度。

```python
# ✅ 正确：使用字节长度
utf8_text = b"SELECT '\xe4\xb8\xad\xe6\x96\x87' AS chinese;\0"  # UTF-8编码
query = b"Q" + struct.pack("!I", len(utf8_text) + 4) + utf8_text  # len()返回字节数
```

## 4. 消息序列和状态管理

### 4.1 ReadyForQuery状态

**规则**：正确设置ReadyForQuery消息中的事务状态标识符。

```python
# ✅ 正确的事务状态标识
ready_idle = b"Z" + struct.pack("!IB", 5, 73)      # 73 = 'I' (空闲)
ready_in_trans = b"Z" + struct.pack("!IB", 5, 84)  # 84 = 'T' (事务中)
ready_error = b"Z" + struct.pack("!IB", 5, 69)     # 69 = 'E' (错误状态)
```

### 4.2 扩展查询协议消息顺序

**规则**：扩展查询协议中的消息必须按照正确顺序发送。

```python
# ✅ 正确的扩展查询序列
# 1. Parse
# 2. Bind
# 3. Execute
# 4. Sync
# 服务器响应：
# 1. ParseComplete
# 2. BindComplete
# 3. RowDescription (如果有结果)
# 4. DataRow (零个或多个)
# 5. CommandComplete
# 6. ReadyForQuery
```

## 5. 验证和测试规则

### 5.1 自动验证

**规则**：生成pcap文件后必须进行格式验证。

```python
def validate_postgresql_message(data, offset=0):
    """验证PostgreSQL协议消息格式"""
    if len(data) < offset + 5:
        return False, "数据太短"

    msg_type = chr(data[offset])
    msg_length = struct.unpack("!I", data[offset+1:offset+5])[0]
    expected_total_length = offset + 1 + msg_length

    if len(data) < expected_total_length:
        return False, f"长度不匹配: 声明{msg_length}, 实际不足"

    return True, f"消息'{msg_type}'格式正确"
```

### 5.2 Wireshark兼容性测试

**规则**：生成的pcap文件必须能在Wireshark中正确显示。

**验证步骤**：
1. 在Wireshark中打开pcap文件
2. 检查是否有"Malformed Packet"错误
3. 验证PostgreSQL协议字段是否正确解析
4. 确认消息边界清晰，无重叠或截断

## 6. 错误处理规则

### 6.1 ErrorResponse格式

**规则**：ErrorResponse消息必须包含正确的字段标识符和终止符。

```python
# ✅ 正确的ErrorResponse格式
error_resp = b"E" + struct.pack("!I", total_length)
error_resp += b"SERROR\0"           # 严重性
error_resp += b"C42601\0"           # 错误代码
error_resp += b"Msyntax error\0"    # 错误消息
error_resp += b"Fparser.c\0"        # 文件名
error_resp += b"L123\0"             # 行号
error_resp += b"Rparser\0"          # 例程名
error_resp += b"\0"                 # 终止符
```

### 6.2 NoticeResponse格式

**规则**：NoticeResponse消息格式与ErrorResponse相同，但严重性不同。

```python
# ✅ 正确的NoticeResponse格式
notice_resp = b"N" + struct.pack("!I", total_length)
notice_resp += b"SNOTICE\0"         # 严重性：NOTICE
notice_resp += b"C42P07\0"          # 代码
notice_resp += b"Mtable exists\0"   # 消息
notice_resp += b"\0"                # 终止符
```

## 7. 性能和资源管理

### 7.1 大数据包处理

**规则**：生成大量数据时，注意内存使用和TCP分片。

```python
# ✅ 正确：分批处理大数据
def generate_large_result_set():
    # 分批发送DataRow消息，避免单个TCP包过大
    for batch in range(0, total_rows, batch_size):
        batch_data = b""
        for row in batch:
            row_data = create_data_row(row)
            batch_data += row_data

        # 发送批次数据
        send_tcp_packet(batch_data)
```

## 8. 文档和注释规则

### 8.1 函数文档

**规则**：每个测试场景函数必须包含详细的中文注释。

```python
def generate_auth_scenario():
    """生成认证场景的数据包

    测试目的：验证PostgreSQL MD5密码认证流程
    协议特性：StartupMessage -> AuthenticationMD5Password -> PasswordMessage -> AuthenticationOk
    预期结果：成功建立认证连接，返回ReadyForQuery状态
    """
```

### 8.2 协议引用

**规则**：复杂协议实现必须引用官方文档。

```python
# 参考：https://www.postgresql.org/docs/current/protocol-message-formats.html
# RowDescription消息格式：
# - 字段数量 (Int16)
# - 对每个字段：
#   - 字段名 (String)
#   - 表OID (Int32)
#   - 列属性号 (Int16)
#   - 数据类型OID (Int32)
#   - 数据类型大小 (Int16)
#   - 类型修饰符 (Int32)
#   - 格式代码 (Int16)
```

---

**重要提醒**：这些规则基于实际开发中遇到的问题总结而来，严格遵循这些规则可以避免Wireshark显示异常和协议格式错误，确保生成的pcap文件完全符合PostgreSQL协议规范。
