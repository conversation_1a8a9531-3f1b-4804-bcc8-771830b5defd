# HTTP Brotli压缩测试用例PCAP文件

本目录包含了全面的HTTP协议中使用Brotli (br) 压缩的测试用例，生成了对应的PCAP文件用于协议分析和测试。

## 项目结构

```
http_brotli/
├── generate_http_brotli_pcap.py    # PCAP生成脚本
├── http_brotli_pcaps/              # 生成的PCAP文件目录
│   ├── http_basic_brotli.pcap
│   ├── http_chunked_brotli.pcap
│   ├── ...
└── README.md                       # 本说明文件
```

## 测试场景详细说明

### 1. 基本场景

#### 1.1 基本Brotli压缩 (`http_basic_brotli.pcap`)
- **场景描述**: 标准的HTTP GET请求和响应，响应体使用Brotli压缩
- **特点**:
  - 客户端发送 `Accept-Encoding: br, gzip, deflate`
  - 服务器响应 `Content-Encoding: br`
  - JSON格式的响应内容
  - 使用 `Connection: keep-alive`

#### 1.2 Chunked编码+Brotli压缩 (`http_chunked_brotli.pcap`)
- **场景描述**: 使用Transfer-Encoding: chunked和Brotli压缩的组合
- **特点**:
  - 响应头包含 `Transfer-Encoding: chunked`
  - 响应头包含 `Content-Encoding: br`
  - HTML内容被压缩后分块传输
  - 包含完整的chunk结构（大小+数据+结束标记）

### 2. 文件传输场景

#### 2.1 文件上传Brotli压缩 (`http_file_upload_brotli.pcap`)
- **场景描述**: POST请求的请求体使用Brotli压缩
- **特点**:
  - HTTP POST方法
  - 请求头包含 `Content-Encoding: br`
  - 上传JSON格式的文件数据
  - 服务器响应也使用Brotli压缩

#### 2.2 文件下载Brotli压缩 (`http_file_download_brotli.pcap`)
- **场景描述**: GET请求下载文件，响应使用Brotli压缩
- **特点**:
  - 包含 `Content-Disposition: attachment` 头
  - XML文件内容的Brotli压缩
  - 包含缓存相关头部（Cache-Control, ETag）
  - 使用 `Range: bytes=0-` 请求头

### 3. 内容类型场景

#### 3.1 二进制内容Brotli压缩 (`http_binary_content_brotli.pcap`)
- **场景描述**: 二进制内容（PNG图片）的Brotli压缩
- **特点**:
  - `Content-Type: image/png`
  - 二进制数据的Brotli压缩
  - 短连接模式 (`Connection: close`)

### 4. 连接类型场景

#### 4.1 Keep-alive连接Brotli压缩 (`http_keep_alive_brotli.pcap`)
- **场景描述**: 在同一TCP连接上进行多个HTTP请求，都使用Brotli压缩
- **特点**:
  - 两个连续的HTTP请求/响应
  - 第一个响应：JSON内容 + `Connection: keep-alive`
  - 第二个响应：HTML内容 + `Connection: close`
  - 包含 `Keep-Alive: timeout=5, max=100` 头

### 5. 多重编码场景

#### 5.1 多重编码 (`http_multi_encoding_brotli.pcap`)
- **场景描述**: 使用多层压缩（gzip + brotli）
- **特点**:
  - `Content-Encoding: gzip, br`
  - 内容先用gzip压缩，再用brotli压缩
  - 演示多重编码的解压顺序

### 6. 错误和边界场景

#### 6.1 损坏的Brotli数据 (`http_corrupted_brotli.pcap`)
- **场景描述**: 服务器返回损坏的Brotli压缩数据
- **特点**:
  - 正确的 `Content-Encoding: br` 头
  - 故意损坏的压缩数据（修改了几个字节）
  - 用于测试解压错误处理

#### 6.2 编码头部不匹配 (`http_encoding_mismatch.pcap`)
- **场景描述**: Content-Encoding头部与实际数据编码不匹配
- **特点**:
  - 声明 `Content-Encoding: br`
  - 实际发送gzip压缩的数据
  - 用于测试编码检测和错误处理

#### 6.3 Content-Length不匹配 (`http_content_length_mismatch_brotli.pcap`)
- **场景描述**: Content-Length头部值与实际数据长度不匹配
- **特点**:
  - `Content-Length` 比实际数据长度大50字节
  - 正确的Brotli压缩数据
  - 用于测试长度验证逻辑

### 7. 大文件和性能场景

#### 7.1 大文件分片传输 (`http_large_file_chunked_brotli.pcap`)
- **场景描述**: 大文件使用chunked编码和Brotli压缩的组合传输
- **特点**:
  - 重复的JSON内容创建大文件
  - `Transfer-Encoding: chunked`
  - `Content-Encoding: br`
  - 多个chunk的完整传输过程

#### 7.2 高压缩级别 (`http_high_compression_brotli.pcap`)
- **场景描述**: 使用最高压缩级别的Brotli压缩
- **特点**:
  - 使用Brotli质量级别11（最高）
  - 包含 `Vary: Accept-Encoding` 头
  - 更高的压缩比但更多的CPU消耗

### 8. 混合场景

#### 8.1 混合压缩场景 (`http_mixed_compression.pcap`)
- **场景描述**: 同一连接中部分请求使用压缩，部分不使用
- **特点**:
  - 第一个请求：支持压缩，响应使用Brotli
  - 第二个请求：不支持压缩，响应为纯文本
  - 演示协商机制

## 技术特点

### 压缩算法
- 使用Python的 `brotli` 库进行实际压缩
- 支持不同的压缩质量级别（1-11）
- 包含多重编码的组合测试

### HTTP协议特性
- 完整的TCP三次握手和四次挥手
- 正确的HTTP/1.1协议格式
- 各种HTTP头部的组合使用
- 不同的连接管理模式

### 内容类型
- JSON数据
- HTML页面
- XML文档
- 二进制图片数据

### 错误处理
- 压缩数据损坏
- 头部信息不匹配
- 长度信息错误

## 使用方法

### 生成PCAP文件
```bash
cd /Users/<USER>/Documents/build-pcap
python3 http_brotli/generate_http_brotli_pcap.py
```

### 分析PCAP文件
可以使用以下工具分析生成的PCAP文件：
- Wireshark
- tcpdump
- tshark
- 自定义的协议分析工具

### 示例分析命令
```bash
# 使用tshark查看HTTP流
tshark -r http_basic_brotli.pcap -Y "http" -T fields -e http.request.method -e http.response.code -e http.content_encoding

# 使用tcpdump查看数据包
tcpdump -r http_chunked_brotli.pcap -A

# 在Wireshark中过滤HTTP流
http.content_encoding == "br"
```

## 测试验证

每个PCAP文件都包含：
1. 完整的TCP连接建立和断开
2. 正确的HTTP协议交互
3. 实际的Brotli压缩数据
4. 适当的HTTP头部设置

可以通过以下方式验证：
1. 在Wireshark中查看协议解析
2. 提取压缩数据并验证解压结果
3. 检查TCP序列号和确认号的正确性
4. 验证HTTP头部的完整性

## 扩展说明

这些测试用例覆盖了HTTP Brotli压缩的主要使用场景，包括：
- 正常的压缩/解压流程
- 各种传输编码的组合
- 错误和异常情况的处理
- 性能和优化相关的场景

可以根据具体需求添加更多的测试场景，如：
- HTTPS下的Brotli压缩
- HTTP/2协议的Brotli压缩
- 更复杂的多重编码组合
- 流式压缩的实时场景
