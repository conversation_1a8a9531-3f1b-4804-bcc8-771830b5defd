# HTTP Brotli协议测试新功能

本文档介绍新添加到HTTP Brotli协议测试用例中的两个测试场景。

## 新增功能概述

### 1. 表单上传测试场景 (multipart/form-data + Brotli压缩)

**文件**: `http_multipart_form_upload_brotli.pcap`

**功能特点**:
- 实现HTTP表单上传(multipart/form-data)的测试用例
- 服务器响应使用Brotli(br)压缩算法压缩返回的内容
- 确保测试用例能够正确处理表单数据的上传和压缩响应的接收

**测试场景内容**:
- TCP三次握手建立连接
- HTTP POST请求包含multipart/form-data表单数据
  - 用户名字段: `testuser123`
  - 邮箱字段: `<EMAIL>`
  - 文件上传: `test_document.json` (包含JSON数据)
- HTTP响应使用Brotli压缩
  - `Content-Encoding: br`
  - 包含上传状态和处理信息
- TCP四次挥手关闭连接

**生成方法**:
```python
from generate_http_brotli_pcap import generate_multipart_form_upload_brotli_scenario
generate_multipart_form_upload_brotli_scenario()
```

### 2. Brotli解压性能压测场景

**文件**: `generate_brotli_performance_test.py`

**功能特点**:
- 参考PostgreSQL性能测试场景的实现方式(`/pgsql/generate_performance_test.py`)
- 创建专门用于测试Brotli解压缩性能的测试用例
- 包含不同大小的压缩数据，以评估解压缩性能
- 实现性能指标收集和报告功能，如解压时间、CPU使用率等

**性能测试特性**:
- 支持自定义请求数量(默认50,000个请求)
- 多种数据大小测试: 1KB, 4KB, 16KB, 64KB
- 多IP多端口负载分布，模拟真实环境
- 详细的压缩统计信息和性能报告
- tcpreplay使用建议

**生成方法**:
```bash
# 交互式生成
python3 generate_brotli_performance_test.py

# 或者在代码中调用
from generate_brotli_performance_test import generate_brotli_performance_pcap
generate_brotli_performance_pcap(target_requests=50000)
```

## 压缩性能数据示例

基于测试结果，不同大小数据的Brotli压缩效果：

| 原始大小 | 压缩大小 | 压缩率 | 节省空间 |
|---------|---------|--------|----------|
| 1,024 字节 | 199 字节 | 19.43% | 825 字节 |
| 4,096 字节 | 322 字节 | 7.86% | 3,774 字节 |
| 16,384 字节 | 742 字节 | 4.53% | 15,642 字节 |
| 65,536 字节 | 2,482 字节 | 3.79% | 63,054 字节 |

## 使用方法

### 生成所有测试场景(包括新功能)
```bash
python3 generate_http_brotli_pcap.py
```

### 单独生成表单上传测试
```python
from generate_http_brotli_pcap import generate_multipart_form_upload_brotli_scenario
generate_multipart_form_upload_brotli_scenario()
```

### 生成性能测试PCAP
```bash
python3 generate_brotli_performance_test.py
# 按提示输入目标请求数，或直接回车使用默认值50000
```

### 演示新功能
```bash
python3 demo_new_features.py
```

## 文件结构

```
http_brotli/
├── generate_http_brotli_pcap.py          # 主要的HTTP Brotli测试生成器(已更新)
├── generate_brotli_performance_test.py   # 新增：专门的性能测试生成器
├── demo_new_features.py                  # 新增：新功能演示脚本
├── NEW_FEATURES.md                       # 新增：本文档
└── http_brotli_pcaps/
    ├── http_multipart_form_upload_brotli.pcap  # 新增：表单上传测试
    └── http_brotli_performance_*.pcap          # 新增：性能测试文件
```

## 测试建议

### 表单上传测试
- 使用tcpreplay回放PCAP文件
- 验证multipart/form-data解析功能
- 测试Brotli解压缩响应处理
- 检查表单字段和文件上传的正确性

### 性能测试
- 使用tcpreplay进行高负载测试
- 监控CPU使用率和内存占用
- 测试不同数据大小的解压缩性能
- 评估并发处理能力

### tcpreplay使用示例
```bash
# 表单上传测试
tcpreplay -i eth0 http_brotli_pcaps/http_multipart_form_upload_brotli.pcap

# 性能测试(根据文件大小调整带宽)
tcpreplay -i eth0 -M 100 http_brotli_pcaps/http_brotli_performance_50k_requests.pcap
```

## 与现有框架的兼容性

新添加的测试场景与现有的HTTP测试框架保持完全一致：

1. **代码结构**: 遵循现有的函数命名和组织方式
2. **数据包格式**: 使用相同的TCP/IP/HTTP封装方法
3. **文件输出**: 统一输出到`http_brotli_pcaps`目录
4. **错误处理**: 采用相同的异常处理机制
5. **配置参数**: 使用一致的IP地址和端口配置

## 技术实现细节

### 表单上传场景
- 使用`uuid`生成唯一的multipart边界
- 包含文本字段和文件上传字段
- 服务器响应包含详细的处理信息
- 支持Brotli压缩的JSON响应

### 性能测试场景
- 参考PostgreSQL性能测试的会话管理方式
- 使用多个客户端IP和服务器IP增加负载分布
- 预生成压缩数据以提高测试效率
- 提供详细的统计信息和建议

这些新功能扩展了HTTP Brotli协议测试的覆盖范围，特别是在表单处理和性能评估方面，为协议测试提供了更全面的测试场景。
