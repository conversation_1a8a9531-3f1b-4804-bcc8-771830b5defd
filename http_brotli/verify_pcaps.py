#!/usr/bin/env python3
"""
验证生成的HTTP Brotli PCAP文件的脚本
检查每个PCAP文件的基本结构和内容
"""

import os
import subprocess
import sys

def check_pcap_file(pcap_path):
    """检查单个PCAP文件的基本信息"""
    try:
        # 使用tcpdump检查文件是否可读
        result = subprocess.run([
            'tcpdump', '-r', pcap_path, '-c', '1', '-q'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode != 0:
            return False, f"无法读取PCAP文件: {result.stderr}"
        
        # 检查是否包含HTTP流量
        result = subprocess.run([
            'tcpdump', '-r', pcap_path, '-c', '10', 'port 80'
        ], capture_output=True, text=True, timeout=10)
        
        if not result.stdout:
            return False, "未找到HTTP流量"
        
        return True, "文件格式正确"
        
    except subprocess.TimeoutExpired:
        return False, "检查超时"
    except Exception as e:
        return False, f"检查出错: {str(e)}"

def get_pcap_stats(pcap_path):
    """获取PCAP文件的统计信息"""
    try:
        # 获取数据包总数
        result = subprocess.run([
            'tcpdump', '-r', pcap_path, '-q'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            packet_count = len(result.stdout.strip().split('\n')) if result.stdout.strip() else 0
            return packet_count
        else:
            return 0
            
    except Exception:
        return 0

def main():
    """主函数"""
    pcap_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "http_brotli_pcaps")
    
    if not os.path.exists(pcap_dir):
        print(f"错误: PCAP目录不存在: {pcap_dir}")
        sys.exit(1)
    
    print("HTTP Brotli PCAP文件验证报告")
    print("=" * 50)
    
    pcap_files = [f for f in os.listdir(pcap_dir) if f.endswith('.pcap')]
    pcap_files.sort()
    
    if not pcap_files:
        print("未找到PCAP文件")
        sys.exit(1)
    
    total_files = len(pcap_files)
    valid_files = 0
    total_packets = 0
    
    for pcap_file in pcap_files:
        pcap_path = os.path.join(pcap_dir, pcap_file)
        file_size = os.path.getsize(pcap_path)
        
        print(f"\n文件: {pcap_file}")
        print(f"大小: {file_size:,} 字节")
        
        # 检查文件有效性
        is_valid, message = check_pcap_file(pcap_path)
        print(f"状态: {'✓ 有效' if is_valid else '✗ 无效'}")
        if not is_valid:
            print(f"错误: {message}")
        else:
            valid_files += 1
            # 获取统计信息
            packet_count = get_pcap_stats(pcap_path)
            total_packets += packet_count
            print(f"数据包数量: {packet_count}")
    
    print("\n" + "=" * 50)
    print("验证总结:")
    print(f"总文件数: {total_files}")
    print(f"有效文件数: {valid_files}")
    print(f"总数据包数: {total_packets:,}")
    print(f"成功率: {(valid_files/total_files)*100:.1f}%")
    
    if valid_files == total_files:
        print("\n✓ 所有PCAP文件验证通过！")
        return 0
    else:
        print(f"\n✗ {total_files - valid_files} 个文件验证失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
