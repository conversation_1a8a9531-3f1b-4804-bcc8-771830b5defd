#!/usr/bin/env python3
"""
测试多重编码实现的正确性
验证gzip + brotli的压缩和解压缩过程
"""

import gzip
import brotli
import json

def test_multi_encoding():
    """测试多重编码的压缩和解压缩"""
    
    # 原始内容（与PCAP生成器中相同）
    original_content = {
        "users": [
            {"id": 1, "name": "<PERSON>", "email": "<EMAIL>", "active": True},
            {"id": 2, "name": "<PERSON>", "email": "<EMAIL>", "active": False},
            {"id": 3, "name": "<PERSON>", "email": "<EMAIL>", "active": True}
        ],
        "metadata": {
            "total": 3,
            "page": 1,
            "compression": "brotli"
        }
    }
    
    original_text = json.dumps(original_content, indent=2)
    original_bytes = original_text.encode('utf-8')
    
    print("=== 多重编码测试 ===")
    print(f"原始内容长度: {len(original_bytes)} 字节")
    print(f"原始内容前100字符: {original_text[:100]}...")
    
    # 第一层：gzip压缩
    print("\n--- 第一层：gzip压缩 ---")
    gzip_compressed = gzip.compress(original_bytes)
    print(f"gzip压缩后长度: {len(gzip_compressed)} 字节")
    print(f"gzip压缩率: {(1 - len(gzip_compressed)/len(original_bytes))*100:.1f}%")
    
    # 验证gzip解压缩
    gzip_decompressed = gzip.decompress(gzip_compressed)
    print(f"gzip解压缩验证: {'✓ 成功' if gzip_decompressed == original_bytes else '✗ 失败'}")
    
    # 第二层：brotli压缩
    print("\n--- 第二层：brotli压缩 ---")
    final_compressed = brotli.compress(gzip_compressed)
    print(f"最终压缩后长度: {len(final_compressed)} 字节")
    print(f"总压缩率: {(1 - len(final_compressed)/len(original_bytes))*100:.1f}%")
    
    # 验证完整的解压缩过程（按相反顺序）
    print("\n--- 完整解压缩验证 ---")
    try:
        # 第一步：brotli解压缩
        step1_decompressed = brotli.decompress(final_compressed)
        print(f"步骤1 - brotli解压缩: {'✓ 成功' if step1_decompressed == gzip_compressed else '✗ 失败'}")
        
        # 第二步：gzip解压缩
        step2_decompressed = gzip.decompress(step1_decompressed)
        print(f"步骤2 - gzip解压缩: {'✓ 成功' if step2_decompressed == original_bytes else '✗ 失败'}")
        
        # 验证最终结果
        final_text = step2_decompressed.decode('utf-8')
        final_json = json.loads(final_text)
        print(f"最终结果验证: {'✓ 成功' if final_json == original_content else '✗ 失败'}")
        
        return True, final_compressed
        
    except Exception as e:
        print(f"解压缩失败: {e}")
        return False, final_compressed

def test_single_encodings():
    """测试单一编码作为对比"""
    
    original_content = {
        "users": [
            {"id": 1, "name": "Alice", "email": "<EMAIL>", "active": True},
            {"id": 2, "name": "Bob", "email": "<EMAIL>", "active": False},
            {"id": 3, "name": "Charlie", "email": "<EMAIL>", "active": True}
        ],
        "metadata": {
            "total": 3,
            "page": 1,
            "compression": "brotli"
        }
    }
    
    original_text = json.dumps(original_content, indent=2)
    original_bytes = original_text.encode('utf-8')
    
    print("\n=== 单一编码对比测试 ===")
    
    # 仅gzip压缩
    gzip_only = gzip.compress(original_bytes)
    print(f"仅gzip压缩: {len(gzip_only)} 字节 (压缩率: {(1-len(gzip_only)/len(original_bytes))*100:.1f}%)")
    
    # 仅brotli压缩
    brotli_only = brotli.compress(original_bytes)
    print(f"仅brotli压缩: {len(brotli_only)} 字节 (压缩率: {(1-len(brotli_only)/len(original_bytes))*100:.1f}%)")
    
    # 多重编码
    gzip_first = gzip.compress(original_bytes)
    multi_encoded = brotli.compress(gzip_first)
    print(f"多重编码(gzip+br): {len(multi_encoded)} 字节 (压缩率: {(1-len(multi_encoded)/len(original_bytes))*100:.1f}%)")
    
    return gzip_only, brotli_only, multi_encoded

def analyze_http_headers():
    """分析HTTP头部的正确性"""
    
    print("\n=== HTTP头部分析 ===")
    
    # RFC 7231 规定的Content-Encoding格式
    print("RFC 7231 Content-Encoding 规范:")
    print("- 多个编码值用逗号分隔")
    print("- 编码顺序表示应用顺序")
    print("- 解码时应按相反顺序进行")
    
    print("\n当前实现:")
    print("Content-Encoding: gzip, br")
    print("含义: 先应用gzip压缩，再应用brotli压缩")
    print("解码: 先brotli解压缩，再gzip解压缩")
    
    # 检查Wireshark支持情况
    print("\nWireshark支持情况:")
    print("- Wireshark 3.0+ 支持brotli解压缩")
    print("- Wireshark 支持gzip解压缩")
    print("- 多重编码的自动解压缩支持有限")
    print("- 可能需要手动解压缩或使用插件")

def extract_compressed_data_from_pcap():
    """从PCAP文件中提取压缩数据进行验证"""
    
    print("\n=== PCAP文件数据提取验证 ===")
    
    try:
        import subprocess
        
        # 使用tshark提取HTTP响应体
        cmd = [
            'tshark', 
            '-r', 'http_brotli/http_brotli_pcaps/http_multi_encoding_brotli.pcap',
            '-Y', 'http.response',
            '-T', 'fields',
            '-e', 'http.file_data'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and result.stdout.strip():
            hex_data = result.stdout.strip().replace(':', '')
            compressed_data = bytes.fromhex(hex_data)
            
            print(f"从PCAP提取的压缩数据长度: {len(compressed_data)} 字节")
            
            # 尝试解压缩
            try:
                step1 = brotli.decompress(compressed_data)
                step2 = gzip.decompress(step1)
                final_text = step2.decode('utf-8')
                final_json = json.loads(final_text)
                
                print("✓ PCAP中的压缩数据验证成功")
                print(f"解压缩后内容: {final_text[:100]}...")
                return True
                
            except Exception as e:
                print(f"✗ PCAP数据解压缩失败: {e}")
                return False
        else:
            print("无法从PCAP文件提取数据")
            return False
            
    except Exception as e:
        print(f"PCAP分析失败: {e}")
        return False

def main():
    """主函数"""
    
    # 测试多重编码实现
    success, compressed_data = test_multi_encoding()
    
    # 测试单一编码对比
    gzip_only, brotli_only, multi_encoded = test_single_encodings()
    
    # 分析HTTP头部
    analyze_http_headers()
    
    # 验证PCAP文件数据
    pcap_valid = extract_compressed_data_from_pcap()
    
    print("\n=== 总结 ===")
    print(f"多重编码实现: {'✓ 正确' if success else '✗ 错误'}")
    print(f"PCAP数据验证: {'✓ 正确' if pcap_valid else '✗ 错误'}")
    
    if success:
        print("\n建议:")
        print("1. 多重编码实现符合RFC标准")
        print("2. Wireshark可能不支持自动多重解压缩")
        print("3. 可以手动提取数据进行验证")
        print("4. 考虑添加单独的验证工具")
    
    return success and pcap_valid

if __name__ == "__main__":
    main()
