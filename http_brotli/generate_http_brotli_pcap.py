#!/usr/bin/env python3
"""
HTTP Brotli压缩测试用例PCAP生成器
基于MongoDB PCAP生成器的框架，生成各种HTTP Brotli压缩场景的测试数据
"""

from scapy.all import *
from scapy.layers.inet import IP, TCP
import struct
import random
import os
import brotli
import json
import gzip
import time
import uuid
import base64

# HTTP 默认端口
HTTP_PORT = 80
HTTPS_PORT = 443

# 客户端和服务器IP地址
CLIENT_IP = "************"
SERVER_IP = "************"

# 随机端口和序列号
client_port = random.randint(40000, 65000)
seq_num = random.randint(1000000, 9000000)
ack_num = random.randint(1000000, 9000000)

# 输出目录
output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "http_brotli_pcaps")

def create_http_packet(src, dst, sport, dport, seq, ack, payload, flags="PA"):
    """创建一个HTTP数据包"""
    eth = Ether(type=0x0800)  # IPv4类型
    ip = IP(src=src, dst=dst)
    tcp = TCP(sport=sport, dport=dport, seq=seq, ack=ack, flags=flags)
    return eth/ip/tcp/payload

def create_tcp_handshake(src, dst, sport, dport):
    """创建TCP三次握手数据包"""
    packets = []
    # 初始序列号
    client_isn = random.randint(1000000, 9000000)
    server_isn = random.randint(1000000, 9000000)
    
    # SYN
    syn = create_http_packet(src, dst, sport, dport, client_isn, 0, b"", flags="S")
    packets.append(syn)
    
    # SYN-ACK
    syn_ack = create_http_packet(dst, src, dport, sport, server_isn, client_isn + 1, b"", flags="SA")
    packets.append(syn_ack)
    
    # ACK
    ack = create_http_packet(src, dst, sport, dport, client_isn + 1, server_isn + 1, b"", flags="A")
    packets.append(ack)
    
    return packets, client_isn + 1, server_isn + 1

def create_tcp_teardown(src, dst, sport, dport, seq, ack):
    """创建TCP四次挥手数据包"""
    packets = []
    
    # FIN-ACK (client)
    fin_ack1 = create_http_packet(src, dst, sport, dport, seq, ack, b"", flags="FA")
    packets.append(fin_ack1)
    
    # ACK (server)
    ack1 = create_http_packet(dst, src, dport, sport, ack, seq + 1, b"", flags="A")
    packets.append(ack1)
    
    # FIN-ACK (server)
    fin_ack2 = create_http_packet(dst, src, dport, sport, ack, seq + 1, b"", flags="FA")
    packets.append(fin_ack2)
    
    # ACK (client)
    ack2 = create_http_packet(src, dst, sport, dport, seq + 1, ack + 1, b"", flags="A")
    packets.append(ack2)
    
    return packets

def write_pcap(packets, filename):
    """将数据包写入PCAP文件"""
    wrpcap(os.path.join(output_dir, filename), packets)
    print(f"已生成 {filename}")

def create_http_request(method, path, headers=None, body=b""):
    """创建HTTP请求"""
    if headers is None:
        headers = {}
    
    # 构建请求行
    request_line = f"{method} {path} HTTP/1.1\r\n"
    
    # 构建头部
    header_lines = []
    for key, value in headers.items():
        header_lines.append(f"{key}: {value}\r\n")
    
    # 组装完整请求
    request = request_line.encode() + b"".join(h.encode() for h in header_lines) + b"\r\n" + body
    return request

def create_http_response(status_code, status_text, headers=None, body=b""):
    """创建HTTP响应"""
    if headers is None:
        headers = {}
    
    # 构建状态行
    status_line = f"HTTP/1.1 {status_code} {status_text}\r\n"
    
    # 构建头部
    header_lines = []
    for key, value in headers.items():
        header_lines.append(f"{key}: {value}\r\n")
    
    # 组装完整响应
    response = status_line.encode() + b"".join(h.encode() for h in header_lines) + b"\r\n" + body
    return response

def generate_sample_pdf_document():
    """生成一个真实的小型PDF文档数据"""
    # 这是一个最小的有效PDF文档，包含一页简单文本
    pdf_content = """%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Brotli Test PDF) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f
0000000010 00000 n
0000000053 00000 n
0000000100 00000 n
0000000249 00000 n
0000000343 00000 n
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
421
%%EOF"""
    return pdf_content.encode('utf-8')

def generate_sample_content():
    """生成各种类型的示例内容"""
    contents = {
        'html': '''<!DOCTYPE html>
<html>
<head>
    <title>Brotli Compression Test</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>HTTP Brotli Compression Test Page</h1>
    <p>This is a test page for HTTP Brotli compression. The content should be compressed using the br algorithm.</p>
    <div>
        <ul>
            <li>Item 1: Lorem ipsum dolor sit amet</li>
            <li>Item 2: Consectetur adipiscing elit</li>
            <li>Item 3: Sed do eiusmod tempor incididunt</li>
        </ul>
    </div>
</body>
</html>''',
        'json': json.dumps({
            "users": [
                {"id": 1, "name": "Alice", "email": "<EMAIL>", "active": True},
                {"id": 2, "name": "Bob", "email": "<EMAIL>", "active": False},
                {"id": 3, "name": "Charlie", "email": "<EMAIL>", "active": True}
            ],
            "metadata": {
                "total": 3,
                "page": 1,
                "compression": "brotli"
            }
        }, indent=2),
        'xml': '''<?xml version="1.0" encoding="UTF-8"?>
<catalog>
    <book id="1">
        <title>The Great Gatsby</title>
        <author>F. Scott Fitzgerald</author>
        <price>12.99</price>
    </book>
    <book id="2">
        <title>To Kill a Mockingbird</title>
        <author>Harper Lee</author>
        <price>14.99</price>
    </book>
</catalog>''',
        'binary': b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x10\x00\x00\x00\x10\x08\x02\x00\x00\x00\x90\x91h6\x00\x00\x00\x19tEXtSoftware\x00Adobe ImageReadyq\xc9e<\x00\x00\x00\x0eIDATx\xdab\xf8\x0f\x00\x01\x01\x01\x00\x18\xdd\x8d\xb4\x00\x00\x00\x00IEND\xaeB`\x82',
        'pdf': generate_sample_pdf_document()
    }
    return contents

def generate_basic_brotli_scenario():
    """生成基本的HTTP Brotli压缩场景"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT)
    packets.extend(handshake_packets)
    
    # 客户端发送HTTP GET请求，支持Brotli压缩
    request_headers = {
        "Host": "example.com",
        "User-Agent": "Mozilla/5.0 (Test Client)",
        "Accept": "text/html,application/json,*/*",
        "Accept-Encoding": "br, gzip, deflate",
        "Connection": "keep-alive"
    }
    
    request = create_http_request("GET", "/api/data", request_headers)
    
    # 客户端发送请求
    c_packet = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request)
    packets.append(c_packet)
    seq_num += len(request)
    
    # 服务器响应，使用Brotli压缩
    content = generate_sample_content()['json']
    compressed_content = brotli.compress(content.encode('utf-8'))
    
    response_headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Content-Encoding": "br",
        "Content-Length": str(len(compressed_content)),
        "Server": "nginx/1.18.0",
        "Connection": "keep-alive"
    }
    
    response = create_http_response(200, "OK", response_headers, compressed_content)
    
    # 服务器发送响应
    s_packet = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response)
    packets.append(s_packet)
    ack_num += len(response)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "http_basic_brotli.pcap")

def generate_chunked_brotli_scenario():
    """生成chunked编码 + Brotli压缩场景"""
    packets = []
    global seq_num, ack_num
    
    # 重置端口和序列号
    global client_port
    client_port = random.randint(40000, 65000)
    seq_num = random.randint(1000000, 9000000)
    ack_num = random.randint(1000000, 9000000)
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT)
    packets.extend(handshake_packets)
    
    # 客户端发送HTTP GET请求
    request_headers = {
        "Host": "example.com",
        "User-Agent": "Mozilla/5.0 (Test Client)",
        "Accept": "text/html,*/*",
        "Accept-Encoding": "br, gzip",
        "Connection": "keep-alive"
    }
    
    request = create_http_request("GET", "/large-content", request_headers)
    
    # 客户端发送请求
    c_packet = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request)
    packets.append(c_packet)
    seq_num += len(request)
    
    # 服务器响应，使用chunked + Brotli压缩
    content = generate_sample_content()['html']
    compressed_content = brotli.compress(content.encode('utf-8'))
    
    # 将压缩内容分成多个chunk
    chunk_size = 100
    chunks = []
    for i in range(0, len(compressed_content), chunk_size):
        chunk_data = compressed_content[i:i+chunk_size]
        chunk = f"{len(chunk_data):x}\r\n".encode() + chunk_data + b"\r\n"
        chunks.append(chunk)
    
    # 添加结束chunk
    chunks.append(b"0\r\n\r\n")
    
    chunked_body = b"".join(chunks)
    
    response_headers = {
        "Content-Type": "text/html; charset=utf-8",
        "Content-Encoding": "br",
        "Transfer-Encoding": "chunked",
        "Server": "Apache/2.4.41",
        "Connection": "keep-alive"
    }
    
    response = create_http_response(200, "OK", response_headers, chunked_body)
    
    # 服务器发送响应
    s_packet = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response)
    packets.append(s_packet)
    ack_num += len(response)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "http_chunked_brotli.pcap")

def generate_file_upload_brotli_scenario():
    """生成文件上传请求的Brotli压缩场景"""
    packets = []
    global seq_num, ack_num

    # 重置端口和序列号
    global client_port
    client_port = random.randint(40000, 65000)
    seq_num = random.randint(1000000, 9000000)
    ack_num = random.randint(1000000, 9000000)

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT)
    packets.extend(handshake_packets)

    # 准备上传的文件内容（JSON数据）
    upload_data = {
        "filename": "data.json",
        "content": generate_sample_content()['json'],
        "metadata": {
            "size": 1024,
            "type": "application/json",
            "uploaded_at": "2024-01-01T12:00:00Z"
        }
    }

    # 压缩上传数据
    upload_content = json.dumps(upload_data, indent=2).encode('utf-8')
    compressed_upload = brotli.compress(upload_content)

    # 客户端发送HTTP POST请求，请求体使用Brotli压缩
    request_headers = {
        "Host": "api.example.com",
        "User-Agent": "FileUploader/1.0",
        "Content-Type": "application/json",
        "Content-Encoding": "br",
        "Content-Length": str(len(compressed_upload)),
        "Accept": "application/json",
        "Accept-Encoding": "br, gzip",
        "Connection": "close"
    }

    request = create_http_request("POST", "/api/upload", request_headers, compressed_upload)

    # 客户端发送请求
    c_packet = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request)
    packets.append(c_packet)
    seq_num += len(request)

    # 服务器响应
    response_data = {"status": "success", "message": "File uploaded successfully", "id": "12345"}
    response_content = json.dumps(response_data).encode('utf-8')
    compressed_response = brotli.compress(response_content)

    response_headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Content-Encoding": "br",
        "Content-Length": str(len(compressed_response)),
        "Server": "FileServer/2.0",
        "Connection": "close"
    }

    response = create_http_response(201, "Created", response_headers, compressed_response)

    # 服务器发送响应
    s_packet = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response)
    packets.append(s_packet)
    ack_num += len(response)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "http_file_upload_brotli.pcap")

def generate_file_download_brotli_scenario():
    """生成文件下载响应的Brotli压缩场景 - PDF文档文件"""
    packets = []
    global seq_num, ack_num

    # 重置端口和序列号
    global client_port
    client_port = random.randint(40000, 65000)
    seq_num = random.randint(1000000, 9000000)
    ack_num = random.randint(1000000, 9000000)

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT)
    packets.extend(handshake_packets)

    # 客户端发送HTTP GET请求下载PDF文档文件
    request_headers = {
        "Host": "cdn.example.com",
        "User-Agent": "DownloadManager/3.0",
        "Accept": "application/pdf,*/*",
        "Accept-Encoding": "br, gzip, deflate",
        "Range": "bytes=0-",
        "Connection": "keep-alive"
    }

    request = create_http_request("GET", "/documents/report.pdf", request_headers)

    # 客户端发送请求
    c_packet = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request)
    packets.append(c_packet)
    seq_num += len(request)

    # 服务器响应，返回压缩的PDF文档文件
    pdf_content = generate_sample_content()['pdf']
    compressed_pdf = brotli.compress(pdf_content)

    response_headers = {
        "Content-Type": "application/pdf",
        "Content-Encoding": "br",
        "Content-Length": str(len(compressed_pdf)),
        "Content-Disposition": "attachment; filename=\"report.pdf\"",
        "Server": "CDN-Server/1.5",
        "Cache-Control": "public, max-age=3600",
        "ETag": "\"pdf789-br\"",
        "Connection": "keep-alive"
    }

    response = create_http_response(200, "OK", response_headers, compressed_pdf)

    # 服务器发送响应
    s_packet = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response)
    packets.append(s_packet)
    ack_num += len(response)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "http_file_download_brotli.pcap")

def generate_binary_content_brotli_scenario():
    """生成二进制内容Brotli压缩场景"""
    packets = []
    global seq_num, ack_num

    # 重置端口和序列号
    global client_port
    client_port = random.randint(40000, 65000)
    seq_num = random.randint(1000000, 9000000)
    ack_num = random.randint(1000000, 9000000)

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT)
    packets.extend(handshake_packets)

    # 客户端发送HTTP GET请求
    request_headers = {
        "Host": "images.example.com",
        "User-Agent": "ImageViewer/2.0",
        "Accept": "image/*",
        "Accept-Encoding": "br, gzip",
        "Connection": "close"
    }

    request = create_http_request("GET", "/images/logo.png", request_headers)

    # 客户端发送请求
    c_packet = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request)
    packets.append(c_packet)
    seq_num += len(request)

    # 服务器响应，返回压缩的二进制图片数据
    binary_content = generate_sample_content()['binary']
    compressed_binary = brotli.compress(binary_content)

    response_headers = {
        "Content-Type": "image/png",
        "Content-Encoding": "br",
        "Content-Length": str(len(compressed_binary)),
        "Server": "ImageServer/1.0",
        "Connection": "close"
    }

    response = create_http_response(200, "OK", response_headers, compressed_binary)

    # 服务器发送响应
    s_packet = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response)
    packets.append(s_packet)
    ack_num += len(response)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "http_binary_content_brotli.pcap")

def generate_keep_alive_brotli_scenario():
    """生成keep-alive连接下的多个Brotli压缩请求场景"""
    packets = []
    global seq_num, ack_num

    # 重置端口和序列号
    global client_port
    client_port = random.randint(40000, 65000)
    seq_num = random.randint(1000000, 9000000)
    ack_num = random.randint(1000000, 9000000)

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT)
    packets.extend(handshake_packets)

    # 第一个请求
    request1_headers = {
        "Host": "api.example.com",
        "User-Agent": "KeepAliveClient/1.0",
        "Accept": "application/json",
        "Accept-Encoding": "br, gzip",
        "Connection": "keep-alive"
    }

    request1 = create_http_request("GET", "/api/users", request1_headers)

    # 客户端发送第一个请求
    c_packet1 = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request1)
    packets.append(c_packet1)
    seq_num += len(request1)

    # 服务器第一个响应
    content1 = generate_sample_content()['json']
    compressed_content1 = brotli.compress(content1.encode('utf-8'))

    response1_headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Content-Encoding": "br",
        "Content-Length": str(len(compressed_content1)),
        "Server": "API-Server/1.0",
        "Connection": "keep-alive",
        "Keep-Alive": "timeout=5, max=100"
    }

    response1 = create_http_response(200, "OK", response1_headers, compressed_content1)

    # 服务器发送第一个响应
    s_packet1 = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response1)
    packets.append(s_packet1)
    ack_num += len(response1)

    # 第二个请求（在同一连接上）
    request2_headers = {
        "Host": "api.example.com",
        "User-Agent": "KeepAliveClient/1.0",
        "Accept": "text/html",
        "Accept-Encoding": "br",
        "Connection": "keep-alive"
    }

    request2 = create_http_request("GET", "/api/status", request2_headers)

    # 客户端发送第二个请求
    c_packet2 = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request2)
    packets.append(c_packet2)
    seq_num += len(request2)

    # 服务器第二个响应
    content2 = generate_sample_content()['html']
    compressed_content2 = brotli.compress(content2.encode('utf-8'))

    response2_headers = {
        "Content-Type": "text/html; charset=utf-8",
        "Content-Encoding": "br",
        "Content-Length": str(len(compressed_content2)),
        "Server": "API-Server/1.0",
        "Connection": "close"  # 关闭连接
    }

    response2 = create_http_response(200, "OK", response2_headers, compressed_content2)

    # 服务器发送第二个响应
    s_packet2 = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response2)
    packets.append(s_packet2)
    ack_num += len(response2)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "http_keep_alive_brotli.pcap")

def generate_multi_encoding_brotli_scenario():
    """生成多重编码场景（gzip + br）"""
    packets = []
    global seq_num, ack_num

    # 重置端口和序列号
    global client_port
    client_port = random.randint(40000, 65000)
    seq_num = random.randint(1000000, 9000000)
    ack_num = random.randint(1000000, 9000000)

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT)
    packets.extend(handshake_packets)

    # 客户端发送HTTP GET请求
    request_headers = {
        "Host": "compress.example.com",
        "User-Agent": "MultiEncodingClient/1.0",
        "Accept": "application/json",
        "Accept-Encoding": "br, gzip, deflate",
        "Connection": "close"
    }

    request = create_http_request("GET", "/api/large-data", request_headers)

    # 客户端发送请求
    c_packet = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request)
    packets.append(c_packet)
    seq_num += len(request)

    # 服务器响应，使用多重编码（先gzip再br）
    content = generate_sample_content()['json']

    # 第一层：gzip压缩
    gzip_compressed = gzip.compress(content.encode('utf-8'))

    # 第二层：brotli压缩
    final_compressed = brotli.compress(gzip_compressed)

    response_headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Content-Encoding": "gzip, br",  # 多重编码
        "Content-Length": str(len(final_compressed)),
        "Server": "MultiCompressServer/1.0",
        "Connection": "close"
    }

    response = create_http_response(200, "OK", response_headers, final_compressed)

    # 服务器发送响应
    s_packet = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response)
    packets.append(s_packet)
    ack_num += len(response)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "http_multi_encoding_brotli.pcap")

def generate_corrupted_brotli_scenario():
    """生成损坏的Brotli压缩数据场景"""
    packets = []
    global seq_num, ack_num

    # 重置端口和序列号
    global client_port
    client_port = random.randint(40000, 65000)
    seq_num = random.randint(1000000, 9000000)
    ack_num = random.randint(1000000, 9000000)

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT)
    packets.extend(handshake_packets)

    # 客户端发送HTTP GET请求
    request_headers = {
        "Host": "error.example.com",
        "User-Agent": "ErrorTestClient/1.0",
        "Accept": "application/json",
        "Accept-Encoding": "br, gzip",
        "Connection": "close"
    }

    request = create_http_request("GET", "/api/corrupted-data", request_headers)

    # 客户端发送请求
    c_packet = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request)
    packets.append(c_packet)
    seq_num += len(request)

    # 服务器响应，返回损坏的Brotli数据
    content = generate_sample_content()['json']
    compressed_content = brotli.compress(content.encode('utf-8'))

    # 故意损坏压缩数据（修改几个字节）
    corrupted_content = bytearray(compressed_content)
    if len(corrupted_content) > 10:
        corrupted_content[5] = (corrupted_content[5] + 1) % 256
        corrupted_content[10] = (corrupted_content[10] + 1) % 256
    corrupted_content = bytes(corrupted_content)

    response_headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Content-Encoding": "br",
        "Content-Length": str(len(corrupted_content)),
        "Server": "ErrorServer/1.0",
        "Connection": "close"
    }

    response = create_http_response(200, "OK", response_headers, corrupted_content)

    # 服务器发送响应
    s_packet = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response)
    packets.append(s_packet)
    ack_num += len(response)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "http_corrupted_brotli.pcap")

def generate_encoding_mismatch_scenario():
    """生成Content-Encoding头部不匹配的场景"""
    packets = []
    global seq_num, ack_num

    # 重置端口和序列号
    global client_port
    client_port = random.randint(40000, 65000)
    seq_num = random.randint(1000000, 9000000)
    ack_num = random.randint(1000000, 9000000)

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT)
    packets.extend(handshake_packets)

    # 客户端发送HTTP GET请求
    request_headers = {
        "Host": "mismatch.example.com",
        "User-Agent": "MismatchTestClient/1.0",
        "Accept": "text/plain",
        "Accept-Encoding": "br, gzip",
        "Connection": "close"
    }

    request = create_http_request("GET", "/api/mismatch-test", request_headers)

    # 客户端发送请求
    c_packet = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request)
    packets.append(c_packet)
    seq_num += len(request)

    # 服务器响应，声明br压缩但实际发送gzip压缩数据
    content = "This is a test content for encoding mismatch scenario."
    gzip_compressed = gzip.compress(content.encode('utf-8'))  # 实际使用gzip压缩

    response_headers = {
        "Content-Type": "text/plain; charset=utf-8",
        "Content-Encoding": "br",  # 声明使用br压缩，但实际是gzip
        "Content-Length": str(len(gzip_compressed)),
        "Server": "MismatchServer/1.0",
        "Connection": "close"
    }

    response = create_http_response(200, "OK", response_headers, gzip_compressed)

    # 服务器发送响应
    s_packet = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response)
    packets.append(s_packet)
    ack_num += len(response)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "http_encoding_mismatch.pcap")

def generate_large_file_chunked_brotli_scenario():
    """生成大文件分片传输的chunked + Brotli压缩场景"""
    packets = []
    global seq_num, ack_num

    # 重置端口和序列号
    global client_port
    client_port = random.randint(40000, 65000)
    seq_num = random.randint(1000000, 9000000)
    ack_num = random.randint(1000000, 9000000)

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT)
    packets.extend(handshake_packets)

    # 客户端发送HTTP GET请求
    request_headers = {
        "Host": "bigfiles.example.com",
        "User-Agent": "BigFileClient/1.0",
        "Accept": "*/*",
        "Accept-Encoding": "br, gzip",
        "Connection": "keep-alive"
    }

    request = create_http_request("GET", "/downloads/large-dataset.json", request_headers)

    # 客户端发送请求
    c_packet = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request)
    packets.append(c_packet)
    seq_num += len(request)

    # 生成大文件内容（重复JSON数据）
    base_content = generate_sample_content()['json']
    large_content = ""
    for i in range(10):  # 重复10次创建大文件
        large_content += f"// Section {i+1}\n" + base_content + "\n\n"

    # 压缩大文件内容
    compressed_large = brotli.compress(large_content.encode('utf-8'))

    # 将压缩内容分成多个chunk（模拟流式传输）
    chunk_size = 200
    chunks = []
    for i in range(0, len(compressed_large), chunk_size):
        chunk_data = compressed_large[i:i+chunk_size]
        chunk = f"{len(chunk_data):x}\r\n".encode() + chunk_data + b"\r\n"
        chunks.append(chunk)

    # 添加结束chunk
    chunks.append(b"0\r\n\r\n")

    chunked_body = b"".join(chunks)

    response_headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Content-Encoding": "br",
        "Transfer-Encoding": "chunked",
        "Server": "BigFileServer/2.0",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive"
    }

    response = create_http_response(200, "OK", response_headers, chunked_body)

    # 服务器发送响应
    s_packet = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response)
    packets.append(s_packet)
    ack_num += len(response)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "http_large_file_chunked_brotli.pcap")

def generate_content_length_mismatch_scenario():
    """生成Content-Length与实际数据长度不匹配的场景"""
    packets = []
    global seq_num, ack_num

    # 重置端口和序列号
    global client_port
    client_port = random.randint(40000, 65000)
    seq_num = random.randint(1000000, 9000000)
    ack_num = random.randint(1000000, 9000000)

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT)
    packets.extend(handshake_packets)

    # 客户端发送HTTP GET请求
    request_headers = {
        "Host": "length-test.example.com",
        "User-Agent": "LengthTestClient/1.0",
        "Accept": "application/json",
        "Accept-Encoding": "br",
        "Connection": "close"
    }

    request = create_http_request("GET", "/api/length-mismatch", request_headers)

    # 客户端发送请求
    c_packet = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request)
    packets.append(c_packet)
    seq_num += len(request)

    # 服务器响应，Content-Length与实际数据长度不匹配
    content = generate_sample_content()['json']
    compressed_content = brotli.compress(content.encode('utf-8'))

    # 故意设置错误的Content-Length（比实际长度大）
    wrong_length = len(compressed_content) + 50

    response_headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Content-Encoding": "br",
        "Content-Length": str(wrong_length),  # 错误的长度
        "Server": "LengthMismatchServer/1.0",
        "Connection": "close"
    }

    response = create_http_response(200, "OK", response_headers, compressed_content)

    # 服务器发送响应
    s_packet = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response)
    packets.append(s_packet)
    ack_num += len(response)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "http_content_length_mismatch_brotli.pcap")

def generate_mixed_compression_scenario():
    """生成混合场景：同一连接中部分使用br压缩，部分不使用"""
    packets = []
    global seq_num, ack_num

    # 重置端口和序列号
    global client_port
    client_port = random.randint(40000, 65000)
    seq_num = random.randint(1000000, 9000000)
    ack_num = random.randint(1000000, 9000000)

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT)
    packets.extend(handshake_packets)

    # 第一个请求 - 支持压缩
    request1_headers = {
        "Host": "mixed.example.com",
        "User-Agent": "MixedClient/1.0",
        "Accept": "application/json",
        "Accept-Encoding": "br, gzip",
        "Connection": "keep-alive"
    }

    request1 = create_http_request("GET", "/api/compressed-data", request1_headers)

    # 客户端发送第一个请求
    c_packet1 = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request1)
    packets.append(c_packet1)
    seq_num += len(request1)

    # 服务器第一个响应 - 使用br压缩
    content1 = generate_sample_content()['json']
    compressed_content1 = brotli.compress(content1.encode('utf-8'))

    response1_headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Content-Encoding": "br",
        "Content-Length": str(len(compressed_content1)),
        "Server": "MixedServer/1.0",
        "Connection": "keep-alive"
    }

    response1 = create_http_response(200, "OK", response1_headers, compressed_content1)

    # 服务器发送第一个响应
    s_packet1 = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response1)
    packets.append(s_packet1)
    ack_num += len(response1)

    # 第二个请求 - 不支持压缩
    request2_headers = {
        "Host": "mixed.example.com",
        "User-Agent": "MixedClient/1.0",
        "Accept": "text/plain",
        "Connection": "keep-alive"
    }

    request2 = create_http_request("GET", "/api/uncompressed-data", request2_headers)

    # 客户端发送第二个请求
    c_packet2 = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request2)
    packets.append(c_packet2)
    seq_num += len(request2)

    # 服务器第二个响应 - 不使用压缩
    content2 = "This is plain text content without compression."

    response2_headers = {
        "Content-Type": "text/plain; charset=utf-8",
        "Content-Length": str(len(content2.encode('utf-8'))),
        "Server": "MixedServer/1.0",
        "Connection": "close"
    }

    response2 = create_http_response(200, "OK", response2_headers, content2.encode('utf-8'))

    # 服务器发送第二个响应
    s_packet2 = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response2)
    packets.append(s_packet2)
    ack_num += len(response2)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "http_mixed_compression.pcap")

def generate_compression_levels_scenario():
    """生成不同压缩级别的Brotli压缩场景"""
    packets = []
    global seq_num, ack_num

    # 重置端口和序列号
    global client_port
    client_port = random.randint(40000, 65000)
    seq_num = random.randint(1000000, 9000000)
    ack_num = random.randint(1000000, 9000000)

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT)
    packets.extend(handshake_packets)

    # 客户端发送HTTP GET请求
    request_headers = {
        "Host": "levels.example.com",
        "User-Agent": "CompressionLevelClient/1.0",
        "Accept": "application/json",
        "Accept-Encoding": "br",
        "Connection": "close"
    }

    request = create_http_request("GET", "/api/high-compression", request_headers)

    # 客户端发送请求
    c_packet = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request)
    packets.append(c_packet)
    seq_num += len(request)

    # 服务器响应，使用高压缩级别的Brotli
    content = generate_sample_content()['json']

    # 使用最高压缩级别（11）
    high_compressed = brotli.compress(content.encode('utf-8'), quality=11)

    response_headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Content-Encoding": "br",
        "Content-Length": str(len(high_compressed)),
        "Server": "HighCompressionServer/1.0",
        "Vary": "Accept-Encoding",
        "Connection": "close"
    }

    response = create_http_response(200, "OK", response_headers, high_compressed)

    # 服务器发送响应
    s_packet = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response)
    packets.append(s_packet)
    ack_num += len(response)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "http_high_compression_brotli.pcap")

def generate_nested_brotli_scenario():
    """生成嵌套Brotli压缩场景（三层压缩）"""
    packets = []
    global seq_num, ack_num

    # 重置端口和序列号
    global client_port
    client_port = random.randint(40000, 65000)
    seq_num = random.randint(1000000, 9000000)
    ack_num = random.randint(1000000, 9000000)

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT)
    packets.extend(handshake_packets)

    # 客户端发送HTTP GET请求
    request_headers = {
        "Host": "nested.example.com",
        "User-Agent": "NestedCompressionClient/1.0",
        "Accept": "application/json",
        "Accept-Encoding": "br, gzip, deflate",
        "Connection": "close"
    }

    request = create_http_request("GET", "/api/nested-compression", request_headers)

    # 客户端发送请求
    c_packet = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request)
    packets.append(c_packet)
    seq_num += len(request)

    # 服务器响应，使用三层嵌套Brotli压缩
    content = generate_sample_content()['json']

    # 第一层：对原始内容进行Brotli压缩
    first_layer = brotli.compress(content.encode('utf-8'))
    print(f"第一层压缩: {len(content.encode('utf-8'))} -> {len(first_layer)} 字节")

    # 第二层：对第一层结果再进行Brotli压缩
    second_layer = brotli.compress(first_layer)
    print(f"第二层压缩: {len(first_layer)} -> {len(second_layer)} 字节")

    # 第三层：对第二层结果再进行Brotli压缩
    third_layer = brotli.compress(second_layer)
    print(f"第三层压缩: {len(second_layer)} -> {len(third_layer)} 字节")

    final_compressed = third_layer

    response_headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Content-Encoding": "br, br, br",  # 三层嵌套压缩
        "Content-Length": str(len(final_compressed)),
        "Server": "NestedCompressionServer/1.0",
        "Vary": "Accept-Encoding",
        "X-Compression-Layers": "3",
        "Connection": "close"
    }

    response = create_http_response(200, "OK", response_headers, final_compressed)

    # 服务器发送响应
    s_packet = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response)
    packets.append(s_packet)
    ack_num += len(response)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "http_nested_brotli.pcap")

def generate_multipart_form_upload_brotli_scenario():
    """生成表单上传测试场景（multipart/form-data + Brotli压缩响应）"""
    packets = []
    global seq_num, ack_num

    # 重置端口和序列号
    global client_port
    client_port = random.randint(40000, 65000)
    seq_num = random.randint(1000000, 9000000)
    ack_num = random.randint(1000000, 9000000)

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT)
    packets.extend(handshake_packets)

    # 生成multipart/form-data内容
    boundary = f"----WebKitFormBoundary{uuid.uuid4().hex[:16]}"

    # 表单字段1：文本字段
    form_data = f"--{boundary}\r\n"
    form_data += 'Content-Disposition: form-data; name="username"\r\n\r\n'
    form_data += "testuser123\r\n"

    # 表单字段2：邮箱字段
    form_data += f"--{boundary}\r\n"
    form_data += 'Content-Disposition: form-data; name="email"\r\n\r\n'
    form_data += "<EMAIL>\r\n"

    # 表单字段3：文件上传
    form_data += f"--{boundary}\r\n"
    form_data += 'Content-Disposition: form-data; name="file"; filename="test_document.json"\r\n'
    form_data += 'Content-Type: application/json\r\n\r\n'

    # 文件内容（JSON数据）
    file_content = {
        "document_id": "doc_12345",
        "title": "Test Document for Form Upload",
        "content": "This is a test document content for multipart form upload testing with Brotli compression.",
        "metadata": {
            "author": "Test User",
            "created_at": "2024-01-01T12:00:00Z",
            "tags": ["test", "upload", "brotli", "compression"],
            "size": 1024
        },
        "data": [
            {"id": i, "value": f"test_value_{i}", "description": f"Test data item {i}"}
            for i in range(1, 21)  # 20个测试数据项
        ]
    }

    form_data += json.dumps(file_content, indent=2)
    form_data += f"\r\n--{boundary}--\r\n"

    form_data_bytes = form_data.encode('utf-8')

    # 客户端发送HTTP POST请求（表单上传）
    request_headers = {
        "Host": "upload.example.com",
        "User-Agent": "FormUploader/2.0 (Brotli-Enabled)",
        "Content-Type": f"multipart/form-data; boundary={boundary}",
        "Content-Length": str(len(form_data_bytes)),
        "Accept": "application/json",
        "Accept-Encoding": "br, gzip, deflate",
        "Connection": "close"
    }

    request = create_http_request("POST", "/api/upload/form", request_headers, form_data_bytes)

    # 客户端发送请求
    c_packet = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request)
    packets.append(c_packet)
    seq_num += len(request)

    # 服务器响应，使用Brotli压缩
    response_data = {
        "status": "success",
        "message": "Form data uploaded successfully",
        "upload_id": f"upload_{uuid.uuid4().hex[:12]}",
        "received_fields": {
            "username": "testuser123",
            "email": "<EMAIL>",
            "file": {
                "filename": "test_document.json",
                "size": len(json.dumps(file_content)),
                "content_type": "application/json",
                "processed": True
            }
        },
        "processing_info": {
            "compression_used": "brotli",
            "compression_ratio": 0.65,
            "processing_time_ms": 45,
            "server_timestamp": "2024-01-01T12:00:01Z"
        }
    }

    response_content = json.dumps(response_data, indent=2).encode('utf-8')
    compressed_response = brotli.compress(response_content)

    response_headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Content-Encoding": "br",
        "Content-Length": str(len(compressed_response)),
        "Server": "FormUploadServer/2.0",
        "X-Upload-Status": "completed",
        "X-Compression-Ratio": "0.65",
        "Connection": "close"
    }

    response = create_http_response(201, "Created", response_headers, compressed_response)

    # 服务器发送响应
    s_packet = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response)
    packets.append(s_packet)
    ack_num += len(response)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "http_multipart_form_upload_brotli.pcap")

def generate_brotli_performance_test_scenario(target_requests=10000, data_sizes=[1024, 4096, 16384, 65536]):
    """生成Brotli解压性能压测场景

    Args:
        target_requests: 目标请求数量
        data_sizes: 不同大小的测试数据（字节）
    """
    print("=" * 60)
    print("HTTP Brotli 解压性能测试 PCAP 生成器")
    print("=" * 60)
    print(f"目标请求数: {target_requests:,}")
    print(f"测试数据大小: {data_sizes}")

    # 计算预估信息
    estimated_packets = target_requests * 2  # 请求+响应
    avg_compressed_size = sum(data_sizes) // len(data_sizes) * 0.3  # 假设30%压缩率
    estimated_size_mb = estimated_packets * avg_compressed_size / 1024 / 1024

    print(f"预计生成报文数: {estimated_packets:,}")
    print(f"预计文件大小: {estimated_size_mb:.1f} MB")
    print("-" * 60)

    start_time = time.time()
    packets = []

    # 创建多个客户端IP以增加负载分布
    client_ips = [f"192.168.1.{10+i}" for i in range(min(16, target_requests // 100))]
    server_ips = [f"192.168.2.{10+i}" for i in range(min(8, len(client_ips) // 2))]

    print(f"使用客户端IP数量: {len(client_ips)}")
    print(f"使用服务器IP数量: {len(server_ips)}")

    # 生成不同大小的测试数据
    test_data_sets = {}
    for size in data_sizes:
        # 生成指定大小的JSON测试数据
        base_data = {
            "test_id": f"perf_test_{size}",
            "data_size": size,
            "timestamp": "2024-01-01T12:00:00Z",
            "performance_test": True
        }

        # 填充数据到指定大小
        padding_needed = size - len(json.dumps(base_data))
        if padding_needed > 0:
            # 创建填充数据
            padding_items = []
            item_size = 50  # 每个填充项约50字节
            num_items = max(1, padding_needed // item_size)

            for i in range(num_items):
                padding_items.append({
                    "item_id": i,
                    "data": f"padding_data_{i}_" + "x" * (item_size - 30)
                })

            base_data["padding"] = padding_items

        # 压缩数据
        json_data = json.dumps(base_data).encode('utf-8')
        # 调整到精确大小
        if len(json_data) > size:
            json_data = json_data[:size]
        elif len(json_data) < size:
            json_data += b' ' * (size - len(json_data))

        compressed_data = brotli.compress(json_data, quality=6)  # 中等压缩级别，平衡速度和压缩率

        test_data_sets[size] = {
            'original': json_data,
            'compressed': compressed_data,
            'compression_ratio': len(compressed_data) / len(json_data)
        }

        print(f"数据大小 {size:,} 字节: 压缩后 {len(compressed_data):,} 字节 (压缩率: {test_data_sets[size]['compression_ratio']:.2%})")

    print("开始生成性能测试数据包...")

    # 生成请求-响应对
    requests_per_size = target_requests // len(data_sizes)
    current_request = 0

    for size in data_sizes:
        print(f"生成 {size:,} 字节数据的测试请求...")

        for i in range(requests_per_size):
            current_request += 1

            # 选择客户端和服务器IP
            client_ip = client_ips[current_request % len(client_ips)]
            server_ip = server_ips[current_request % len(server_ips)]
            client_port = 40000 + (current_request % 20000)

            # 生成序列号
            seq_num = random.randint(1000000, 9000000)
            ack_num = random.randint(1000000, 9000000)

            # TCP三次握手
            handshake_packets, seq_num, ack_num = create_tcp_handshake(client_ip, server_ip, client_port, HTTP_PORT)
            packets.extend(handshake_packets)

            # 客户端请求
            request_headers = {
                "Host": f"perf-test-{size}.example.com",
                "User-Agent": f"BrotliPerfTest/1.0 (Request-{current_request})",
                "Accept": "application/json",
                "Accept-Encoding": "br",
                "X-Test-Size": str(size),
                "X-Test-Request-Id": str(current_request),
                "Connection": "close"
            }

            request = create_http_request("GET", f"/api/perf-test/{size}/{current_request}", request_headers)

            # 发送请求
            c_packet = create_http_packet(client_ip, server_ip, client_port, HTTP_PORT, seq_num, ack_num, request)
            packets.append(c_packet)
            seq_num += len(request)

            # 服务器响应（使用预压缩的数据）
            compressed_data = test_data_sets[size]['compressed']

            response_headers = {
                "Content-Type": "application/json; charset=utf-8",
                "Content-Encoding": "br",
                "Content-Length": str(len(compressed_data)),
                "Server": "BrotliPerfServer/1.0",
                "X-Original-Size": str(size),
                "X-Compressed-Size": str(len(compressed_data)),
                "X-Compression-Ratio": f"{test_data_sets[size]['compression_ratio']:.3f}",
                "X-Test-Request-Id": str(current_request),
                "Connection": "close"
            }

            response = create_http_response(200, "OK", response_headers, compressed_data)

            # 发送响应
            s_packet = create_http_packet(server_ip, client_ip, HTTP_PORT, client_port, ack_num, seq_num, response)
            packets.append(s_packet)
            ack_num += len(response)

            # TCP四次挥手
            teardown_packets = create_tcp_teardown(client_ip, server_ip, client_port, HTTP_PORT, seq_num, ack_num)
            packets.extend(teardown_packets)

            # 进度显示
            if current_request % 1000 == 0:
                progress = (current_request / target_requests) * 100
                elapsed = time.time() - start_time
                print(f"进度: {progress:.1f}% ({current_request}/{target_requests}) - 已用时: {elapsed:.1f}s")

    generation_time = time.time() - start_time
    print(f"数据包生成完成! 用时: {generation_time:.1f}s")
    print(f"实际生成报文数: {len(packets):,}")

    # 写入PCAP文件
    filename = f"http_brotli_performance_{target_requests//1000}k_requests.pcap"
    filepath = os.path.join(output_dir, filename)

    print(f"正在写入文件: {filepath}")
    write_start = time.time()
    write_pcap(packets, filename)
    write_time = time.time() - write_start

    # 获取文件大小
    file_size = os.path.getsize(filepath)
    file_size_mb = file_size / 1024 / 1024

    print("=" * 60)
    print("Brotli性能测试PCAP生成完成!")
    print(f"文件路径: {filepath}")
    print(f"文件大小: {file_size_mb:.1f} MB")
    print(f"报文数量: {len(packets):,}")
    print(f"请求数量: {current_request:,}")
    print(f"客户端IP数量: {len(client_ips)}")
    print(f"服务器IP数量: {len(server_ips)}")
    print(f"生成用时: {generation_time:.1f}s")
    print(f"写入用时: {write_time:.1f}s")
    print(f"总用时: {generation_time + write_time:.1f}s")
    print("=" * 60)

    # 性能测试统计
    print("性能测试数据统计:")
    for size in data_sizes:
        data_info = test_data_sets[size]
        print(f"  {size:,} 字节数据:")
        print(f"    压缩后大小: {len(data_info['compressed']):,} 字节")
        print(f"    压缩率: {data_info['compression_ratio']:.2%}")
        print(f"    请求数量: {requests_per_size:,}")

    print("-" * 60)
    print("tcpreplay 使用建议:")
    recommended_mbps = int(file_size_mb * 8 * 1.1)  # 加10%余量
    print(f"tcpreplay -i eth0 -M {recommended_mbps} {filepath}")
    print("性能测试重点: Brotli解压缩性能、内存使用、CPU占用率")

def generate_mixed_gzip_scenario():
    """生成混合gzip压缩场景（同一连接中混合压缩和非压缩数据）"""
    packets = []
    global seq_num, ack_num

    # 重置端口和序列号
    global client_port
    client_port = random.randint(40000, 65000)
    seq_num = random.randint(1000000, 9000000)
    ack_num = random.randint(1000000, 9000000)

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT)
    packets.extend(handshake_packets)

    # 第一个请求 - 请求gzip压缩的JSON数据
    request1_headers = {
        "Host": "mixed-gzip.example.com",
        "User-Agent": "MixedGzipClient/1.0",
        "Accept": "application/json",
        "Accept-Encoding": "gzip, deflate, br",
        "Connection": "keep-alive"
    }

    request1 = create_http_request("GET", "/api/compressed-data", request1_headers)

    # 客户端发送第一个请求
    c_packet1 = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request1)
    packets.append(c_packet1)
    seq_num += len(request1)

    # 服务器第一个响应 - 使用gzip压缩的JSON数据
    content1 = generate_sample_content()['json']
    compressed_content1 = gzip.compress(content1.encode('utf-8'))

    response1_headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Content-Encoding": "gzip",
        "Content-Length": str(len(compressed_content1)),
        "Server": "MixedGzipServer/1.0",
        "Connection": "keep-alive",
        "Keep-Alive": "timeout=5, max=100"
    }

    response1 = create_http_response(200, "OK", response1_headers, compressed_content1)

    # 服务器发送第一个响应
    s_packet1 = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response1)
    packets.append(s_packet1)
    ack_num += len(response1)

    # 第二个请求 - 请求未压缩的纯文本数据
    request2_headers = {
        "Host": "mixed-gzip.example.com",
        "User-Agent": "MixedGzipClient/1.0",
        "Accept": "text/plain",
        "Accept-Encoding": "gzip, deflate, br",
        "Connection": "keep-alive"
    }

    request2 = create_http_request("GET", "/api/plain-text", request2_headers)

    # 客户端发送第二个请求
    c_packet2 = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request2)
    packets.append(c_packet2)
    seq_num += len(request2)

    # 服务器第二个响应 - 未压缩的纯文本数据
    content2 = "This is uncompressed plain text data."

    response2_headers = {
        "Content-Type": "text/plain; charset=utf-8",
        # 注意：没有 Content-Encoding 头部，表示未压缩
        "Content-Length": str(len(content2.encode('utf-8'))),
        "Server": "MixedGzipServer/1.0",
        "Connection": "keep-alive",
        "Keep-Alive": "timeout=5, max=99"
    }

    response2 = create_http_response(200, "OK", response2_headers, content2.encode('utf-8'))

    # 服务器发送第二个响应
    s_packet2 = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response2)
    packets.append(s_packet2)
    ack_num += len(response2)

    # 第三个请求 - 请求gzip压缩的HTML数据
    request3_headers = {
        "Host": "mixed-gzip.example.com",
        "User-Agent": "MixedGzipClient/1.0",
        "Accept": "text/html",
        "Accept-Encoding": "gzip, deflate, br",
        "Connection": "close"  # 最后一个请求关闭连接
    }

    request3 = create_http_request("GET", "/api/final-data", request3_headers)

    # 客户端发送第三个请求
    c_packet3 = create_http_packet(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num, request3)
    packets.append(c_packet3)
    seq_num += len(request3)

    # 服务器第三个响应 - 使用gzip压缩的HTML数据
    content3 = generate_sample_content()['html']
    compressed_content3 = gzip.compress(content3.encode('utf-8'))

    response3_headers = {
        "Content-Type": "text/html; charset=utf-8",
        "Content-Encoding": "gzip",
        "Content-Length": str(len(compressed_content3)),
        "Server": "MixedGzipServer/1.0",
        "Connection": "close"
    }

    response3 = create_http_response(200, "OK", response3_headers, compressed_content3)

    # 服务器发送第三个响应
    s_packet3 = create_http_packet(SERVER_IP, CLIENT_IP, HTTP_PORT, client_port, ack_num, seq_num, response3)
    packets.append(s_packet3)
    ack_num += len(response3)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, HTTP_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "http_mixed_gzip.pcap")

def main():
    """主函数，生成所有场景的PCAP文件"""
    print("开始生成HTTP Brotli压缩协议PCAP文件...")

    # 生成基本场景
    print("生成基本场景...")
    generate_basic_brotli_scenario()
    generate_chunked_brotli_scenario()
    generate_file_upload_brotli_scenario()
    generate_file_download_brotli_scenario()
    generate_binary_content_brotli_scenario()

    # 生成新增的表单上传场景
    print("生成表单上传场景...")
    generate_multipart_form_upload_brotli_scenario()

    # 生成连接和编码场景
    print("生成连接和编码场景...")
    generate_keep_alive_brotli_scenario()
    generate_multi_encoding_brotli_scenario()
    generate_mixed_gzip_scenario()

    # 生成错误和边界场景
    print("生成错误和边界场景...")
    generate_corrupted_brotli_scenario()
    generate_encoding_mismatch_scenario()
    generate_large_file_chunked_brotli_scenario()
    generate_content_length_mismatch_scenario()
    generate_mixed_compression_scenario()
    generate_compression_levels_scenario()
    generate_nested_brotli_scenario()

    # 生成性能测试场景
    print("生成Brotli解压性能测试场景...")
    generate_brotli_performance_test_scenario(target_requests=5000)  # 生成5000个请求的性能测试

    print(f"\n所有PCAP文件已生成到 {output_dir} 目录")
    print("\n生成的测试场景包括：")
    print("1. 基本Brotli压缩 (http_basic_brotli.pcap)")
    print("2. Chunked编码+Brotli压缩 (http_chunked_brotli.pcap)")
    print("3. 文件上传Brotli压缩 (http_file_upload_brotli.pcap)")
    print("4. PDF文档文件下载Brotli压缩 (http_file_download_brotli.pcap)")
    print("5. 二进制内容Brotli压缩 (http_binary_content_brotli.pcap)")
    print("6. 表单上传测试场景 (http_multipart_form_upload_brotli.pcap) [新增]")
    print("7. Keep-alive连接Brotli压缩 (http_keep_alive_brotli.pcap)")
    print("8. 多重编码(gzip+br) (http_multi_encoding_brotli.pcap)")
    print("9. 混合gzip压缩场景 (http_mixed_gzip.pcap)")
    print("10. 损坏的Brotli数据 (http_corrupted_brotli.pcap)")
    print("11. 编码头部不匹配 (http_encoding_mismatch.pcap)")
    print("12. 大文件分片传输 (http_large_file_chunked_brotli.pcap)")
    print("13. Content-Length不匹配 (http_content_length_mismatch_brotli.pcap)")
    print("14. 混合压缩场景 (http_mixed_compression.pcap)")
    print("15. 高压缩级别 (http_high_compression_brotli.pcap)")
    print("16. 嵌套Brotli压缩(三层) (http_nested_brotli.pcap)")
    print("17. Brotli解压性能测试 (http_brotli_performance_5k_requests.pcap) [新增]")
    print("\n新增测试场景说明：")
    print("- 表单上传测试场景：实现multipart/form-data表单上传，服务器响应使用Brotli压缩")
    print("- Brotli解压性能测试：生成不同大小数据的压缩测试，用于评估解压缩性能")

if __name__ == "__main__":
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    main()
