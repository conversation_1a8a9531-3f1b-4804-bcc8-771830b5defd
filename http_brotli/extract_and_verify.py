#!/usr/bin/env python3
"""
从PCAP文件中提取HTTP响应数据并验证多重编码
"""

from scapy.all import *
import gzip
import brotli
import json
import re

def extract_http_response_from_pcap(pcap_file):
    """从PCAP文件中提取HTTP响应数据"""
    
    try:
        packets = rdpcap(pcap_file)
        
        for packet in packets:
            if packet.haslayer(TCP) and packet.haslayer(Raw):
                payload = packet[Raw].load
                
                # 查找HTTP响应
                if b'HTTP/1.1 200 OK' in payload:
                    payload_str = payload.decode('utf-8', errors='ignore')
                    
                    # 查找Content-Encoding头部
                    if 'Content-Encoding: gzip, br' in payload_str:
                        print("找到多重编码的HTTP响应")
                        
                        # 分离头部和主体
                        parts = payload_str.split('\r\n\r\n', 1)
                        if len(parts) == 2:
                            headers, body_str = parts
                            
                            print("HTTP头部:")
                            for line in headers.split('\r\n'):
                                if line.startswith('Content-'):
                                    print(f"  {line}")
                            
                            # 提取二进制数据
                            header_end = payload.find(b'\r\n\r\n') + 4
                            compressed_data = payload[header_end:]
                            
                            print(f"\n压缩数据长度: {len(compressed_data)} 字节")
                            print(f"压缩数据前20字节: {compressed_data[:20].hex()}")
                            
                            return compressed_data, headers
                            
        print("未找到多重编码的HTTP响应")
        return None, None
        
    except Exception as e:
        print(f"提取PCAP数据失败: {e}")
        return None, None

def verify_multi_encoding_data(compressed_data):
    """验证多重编码数据的正确性"""
    
    if not compressed_data:
        return False
    
    try:
        print("\n=== 多重编码数据验证 ===")
        
        # 第一步：brotli解压缩
        print("步骤1: brotli解压缩...")
        step1_data = brotli.decompress(compressed_data)
        print(f"  解压缩后长度: {len(step1_data)} 字节")
        print(f"  数据前20字节: {step1_data[:20].hex()}")
        
        # 检查是否是gzip格式
        if step1_data.startswith(b'\x1f\x8b'):
            print("  ✓ 检测到gzip格式")
        else:
            print("  ✗ 不是gzip格式")
            return False
        
        # 第二步：gzip解压缩
        print("步骤2: gzip解压缩...")
        step2_data = gzip.decompress(step1_data)
        print(f"  解压缩后长度: {len(step2_data)} 字节")
        
        # 第三步：验证JSON格式
        print("步骤3: JSON格式验证...")
        final_text = step2_data.decode('utf-8')
        final_json = json.loads(final_text)
        
        print("  ✓ JSON格式正确")
        print(f"  用户数量: {len(final_json.get('users', []))}")
        print(f"  元数据: {final_json.get('metadata', {})}")
        
        return True
        
    except brotli.error as e:
        print(f"  ✗ brotli解压缩失败: {e}")
        return False
    except gzip.BadGzipFile as e:
        print(f"  ✗ gzip解压缩失败: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"  ✗ JSON解析失败: {e}")
        return False
    except Exception as e:
        print(f"  ✗ 验证失败: {e}")
        return False

def compare_with_reference():
    """与参考实现进行对比"""
    
    print("\n=== 参考实现对比 ===")
    
    # 生成参考数据
    original_content = {
        "users": [
            {"id": 1, "name": "Alice", "email": "<EMAIL>", "active": True},
            {"id": 2, "name": "Bob", "email": "<EMAIL>", "active": False},
            {"id": 3, "name": "Charlie", "email": "<EMAIL>", "active": True}
        ],
        "metadata": {
            "total": 3,
            "page": 1,
            "compression": "brotli"
        }
    }
    
    original_text = json.dumps(original_content, indent=2)
    original_bytes = original_text.encode('utf-8')
    
    # 生成参考压缩数据
    gzip_compressed = gzip.compress(original_bytes)
    reference_compressed = brotli.compress(gzip_compressed)
    
    print(f"参考实现压缩数据长度: {len(reference_compressed)} 字节")
    print(f"参考数据前20字节: {reference_compressed[:20].hex()}")
    
    return reference_compressed

def analyze_wireshark_compatibility():
    """分析Wireshark兼容性问题"""
    
    print("\n=== Wireshark兼容性分析 ===")
    
    print("问题分析:")
    print("1. Wireshark的HTTP解析器支持单一Content-Encoding")
    print("2. 多重编码(gzip, br)需要按顺序解压缩")
    print("3. Wireshark可能只尝试最后一个编码(br)")
    print("4. 当brotli解压缩后得到gzip数据时，Wireshark不会继续解压缩")
    
    print("\n解决方案:")
    print("1. 使用手动提取和验证工具")
    print("2. 创建Wireshark插件支持多重编码")
    print("3. 修改实现使用单一编码")
    print("4. 提供详细的验证文档")

def create_fixed_version():
    """创建修复版本的建议"""
    
    print("\n=== 修复建议 ===")
    
    print("选项1: 修改为单一编码")
    print("  - 只使用brotli压缩")
    print("  - 确保Wireshark完全兼容")
    print("  - 简化实现逻辑")
    
    print("\n选项2: 保持多重编码但添加验证")
    print("  - 保持当前实现（符合RFC标准）")
    print("  - 添加专门的验证工具")
    print("  - 在文档中说明Wireshark限制")
    
    print("\n选项3: 创建两个版本")
    print("  - 一个单一编码版本（Wireshark友好）")
    print("  - 一个多重编码版本（RFC标准）")
    print("  - 分别用于不同的测试场景")

def main():
    """主函数"""
    
    pcap_file = "http_brotli/http_brotli_pcaps/http_multi_encoding_brotli.pcap"
    
    print("HTTP多重编码PCAP文件验证工具")
    print("=" * 50)
    
    # 提取PCAP数据
    compressed_data, headers = extract_http_response_from_pcap(pcap_file)
    
    if compressed_data:
        # 验证多重编码数据
        is_valid = verify_multi_encoding_data(compressed_data)
        
        # 生成参考数据进行对比
        reference_data = compare_with_reference()
        
        # 对比数据
        if len(compressed_data) == len(reference_data):
            print(f"\n✓ 数据长度匹配: {len(compressed_data)} 字节")
        else:
            print(f"\n✗ 数据长度不匹配: PCAP={len(compressed_data)}, 参考={len(reference_data)}")
        
        if compressed_data == reference_data:
            print("✓ 数据内容完全匹配")
        else:
            print("✗ 数据内容不匹配")
            
        # 分析兼容性问题
        analyze_wireshark_compatibility()
        
        # 提供修复建议
        create_fixed_version()
        
        print(f"\n=== 最终结论 ===")
        if is_valid:
            print("✓ 多重编码实现正确，符合RFC标准")
            print("✓ 压缩数据可以正确解压缩")
            print("⚠ Wireshark不支持自动多重解压缩（这是Wireshark的限制，不是代码问题）")
        else:
            print("✗ 多重编码实现有问题")
            
    else:
        print("✗ 无法从PCAP文件提取数据")

if __name__ == "__main__":
    main()
