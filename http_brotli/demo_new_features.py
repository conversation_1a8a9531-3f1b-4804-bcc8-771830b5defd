#!/usr/bin/env python3
"""
HTTP Brotli新功能演示脚本
演示新添加的两个测试场景：
1. 表单上传测试场景（multipart/form-data + Brotli压缩响应）
2. Brotli解压性能压测场景
"""

import os
import sys
import time

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from generate_http_brotli_pcap import generate_multipart_form_upload_brotli_scenario
from generate_brotli_performance_test import generate_brotli_performance_pcap, generate_test_data

def demo_multipart_form_upload():
    """演示表单上传测试场景"""
    print("=" * 60)
    print("演示1: 表单上传测试场景 (multipart/form-data + Brotli压缩)")
    print("=" * 60)
    print("功能特点:")
    print("- 实现HTTP表单上传(multipart/form-data)的测试用例")
    print("- 服务器响应使用Brotli(br)压缩算法压缩返回的内容")
    print("- 包含文本字段和文件上传字段")
    print("- 测试表单数据的上传和压缩响应的接收")
    print()
    
    print("正在生成表单上传测试场景...")
    start_time = time.time()
    
    try:
        generate_multipart_form_upload_brotli_scenario()
        generation_time = time.time() - start_time
        
        # 检查生成的文件
        pcap_file = "http_brotli_pcaps/http_multipart_form_upload_brotli.pcap"
        if os.path.exists(pcap_file):
            file_size = os.path.getsize(pcap_file)
            print(f"✓ 生成成功!")
            print(f"  文件: {pcap_file}")
            print(f"  大小: {file_size:,} 字节")
            print(f"  用时: {generation_time:.2f} 秒")
            
            # 显示文件内容概要
            print("\n测试场景内容:")
            print("- TCP三次握手建立连接")
            print("- HTTP POST请求包含multipart/form-data表单数据")
            print("  * 用户名字段: testuser123")
            print("  * 邮箱字段: <EMAIL>")
            print("  * 文件上传: test_document.json (包含JSON数据)")
            print("- HTTP响应使用Brotli压缩")
            print("  * Content-Encoding: br")
            print("  * 包含上传状态和处理信息")
            print("- TCP四次挥手关闭连接")
        else:
            print("✗ 文件生成失败")
            
    except Exception as e:
        print(f"✗ 生成失败: {e}")
    
    print()

def demo_performance_test_data():
    """演示性能测试数据生成"""
    print("=" * 60)
    print("演示2: Brotli解压性能测试数据生成")
    print("=" * 60)
    print("功能特点:")
    print("- 生成不同大小的测试数据用于性能评估")
    print("- 支持多种数据大小: 1KB, 4KB, 16KB, 64KB")
    print("- 使用中等压缩级别平衡速度和压缩率")
    print("- 提供详细的压缩统计信息")
    print()
    
    # 测试不同大小的数据生成
    test_sizes = [1024, 4096, 16384, 65536]  # 1KB, 4KB, 16KB, 64KB
    
    print("正在生成不同大小的测试数据...")
    print()
    
    for size in test_sizes:
        print(f"生成 {size:,} 字节测试数据...")
        start_time = time.time()
        
        try:
            data_info = generate_test_data(size, compression_quality=6)
            generation_time = time.time() - start_time
            
            print(f"✓ 生成成功!")
            print(f"  原始大小: {data_info['original_size']:,} 字节")
            print(f"  压缩大小: {data_info['compressed_size']:,} 字节")
            print(f"  压缩率: {data_info['compression_ratio']:.2%}")
            print(f"  节省空间: {data_info['original_size'] - data_info['compressed_size']:,} 字节")
            print(f"  生成用时: {generation_time:.3f} 秒")
            print()
            
        except Exception as e:
            print(f"✗ 生成失败: {e}")
            print()

def demo_small_performance_test():
    """演示小规模性能测试PCAP生成"""
    print("=" * 60)
    print("演示3: 小规模Brotli性能测试PCAP生成")
    print("=" * 60)
    print("功能特点:")
    print("- 参考PostgreSQL性能测试的实现方式")
    print("- 生成多个会话和四元组以模拟真实负载")
    print("- 包含不同大小数据的压缩测试")
    print("- 提供性能指标收集和报告功能")
    print()
    
    # 生成小规模测试（100个请求用于演示）
    target_requests = 100
    data_sizes = [1024, 4096]  # 简化的数据大小用于演示
    
    print(f"正在生成 {target_requests} 个请求的性能测试PCAP...")
    print(f"数据大小: {data_sizes}")
    print()
    
    # 模拟用户确认
    print("模拟用户确认: y")
    
    # 临时重定向input函数
    import builtins
    original_input = builtins.input
    builtins.input = lambda prompt: 'y'
    
    try:
        start_time = time.time()
        generate_brotli_performance_pcap(target_requests, data_sizes)
        total_time = time.time() - start_time
        
        # 检查生成的文件
        pcap_file = f"http_brotli_pcaps/http_brotli_performance_{target_requests//1000}k_requests.pcap"
        if target_requests < 1000:
            pcap_file = f"http_brotli_pcaps/http_brotli_performance_0k_requests.pcap"
            
        # 查找实际生成的文件
        import glob
        perf_files = glob.glob("http_brotli_pcaps/http_brotli_performance_*.pcap")
        if perf_files:
            pcap_file = perf_files[-1]  # 获取最新生成的文件
            
        if os.path.exists(pcap_file):
            file_size = os.path.getsize(pcap_file)
            print(f"\n✓ 性能测试PCAP生成成功!")
            print(f"  文件: {pcap_file}")
            print(f"  大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
            print(f"  总用时: {total_time:.2f} 秒")
        else:
            print(f"\n✗ 未找到生成的文件: {pcap_file}")
            
    except Exception as e:
        print(f"✗ 生成失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 恢复原始input函数
        builtins.input = original_input

def main():
    """主演示函数"""
    print("HTTP Brotli协议测试新功能演示")
    print("本演示展示新添加的两个测试场景")
    print()
    
    # 确保输出目录存在
    output_dir = "http_brotli_pcaps"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")
        print()
    
    # 演示1: 表单上传测试场景
    demo_multipart_form_upload()
    
    # 演示2: 性能测试数据生成
    demo_performance_test_data()
    
    # 演示3: 小规模性能测试PCAP生成
    demo_small_performance_test()
    
    print("=" * 60)
    print("演示完成!")
    print("=" * 60)
    print("新增功能总结:")
    print()
    print("1. 表单上传测试场景 (http_multipart_form_upload_brotli.pcap)")
    print("   - 实现multipart/form-data表单上传")
    print("   - 服务器响应使用Brotli压缩")
    print("   - 包含文本字段和文件上传测试")
    print()
    print("2. Brotli解压性能压测场景")
    print("   - 参考PostgreSQL性能测试实现方式")
    print("   - 支持自定义请求数量和数据大小")
    print("   - 多IP多端口负载分布")
    print("   - 详细的性能统计和tcpreplay建议")
    print()
    print("使用方法:")
    print("- 运行 generate_http_brotli_pcap.py 生成所有测试场景")
    print("- 运行 generate_brotli_performance_test.py 生成专门的性能测试")
    print("- 使用tcpreplay回放生成的PCAP文件进行测试")
    print()
    print("这些新功能与现有的HTTP测试框架保持一致，")
    print("可以有效测试Brotli压缩的各种场景和性能特性。")

if __name__ == "__main__":
    main()
