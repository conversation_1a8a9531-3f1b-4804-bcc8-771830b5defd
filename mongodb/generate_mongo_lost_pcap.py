#!/usr/bin/env python3
"""
MongoDB协议TCP网络丢包异常测试场景生成器

用于生成各种TCP网络丢包情况下的MongoDB协议测试数据包，
模拟混沌工程场景，验证协议解析器的鲁棒性。
"""

import sys
import os
import random
import struct
from scapy.all import *
from scapy.layers.inet import IP, TCP
import zlib
from bson import BSON
from bson.objectid import ObjectId
from collections import OrderedDict

# MongoDB协议常量
MONGODB_PORT = 27017
CLIENT_IP = "************"
SERVER_IP = "************"

# TCP分段常量（遵循网络协议处理规范）
MTU = 1500
TCP_HEADER_SIZE = 20
IP_HEADER_SIZE = 20
ETH_HEADER_SIZE = 14
MAX_PAYLOAD_SIZE = MTU - ETH_HEADER_SIZE - IP_HEADER_SIZE - TCP_HEADER_SIZE  # 1446字节

# 输出目录
LOST_OUTPUT_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "mongo_lost_pcaps")

# 全局变量
client_port = random.randint(40000, 65000)
seq_num = random.randint(1000000, 9000000)
ack_num = random.randint(1000000, 9000000)

def create_tcp_segments_with_loss(src, dst, sport, dport, seq_start, ack, payload, lost_segments, flags="PA"):
    """将大型payload分割成多个TCP段，并模拟指定分段的丢失"""
    segments = []
    total_length = len(payload)
    lost_set = set(lost_segments)
    
    if total_length <= MAX_PAYLOAD_SIZE:
        if 1 not in lost_set:
            segment = create_mongodb_packet(src, dst, sport, dport, seq_start, ack, payload, flags)
            segments.append(segment)
        else:
            print(f"[丢包模拟] 丢失第1个分段，大小: {len(payload)} 字节")
        return segments, seq_start + total_length
    
    # 需要分段
    offset = 0
    seq_num = seq_start
    segment_number = 1
    
    while offset < total_length:
        remaining = total_length - offset
        segment_size = min(MAX_PAYLOAD_SIZE, remaining)
        segment_payload = payload[offset:offset + segment_size]
        
        # 确定TCP标志
        if offset == 0:
            segment_flags = "P" if remaining > MAX_PAYLOAD_SIZE else flags
        elif offset + segment_size >= total_length:
            segment_flags = flags
        else:
            segment_flags = "A"
        
        # 检查是否要丢失此分段
        if segment_number not in lost_set:
            segment = create_mongodb_packet(src, dst, sport, dport, seq_num, ack, segment_payload, segment_flags)
            segments.append(segment)
        else:
            print(f"[丢包模拟] 丢失第{segment_number}个分段，大小: {len(segment_payload)} 字节")
        
        offset += segment_size
        seq_num += segment_size
        segment_number += 1
    
    return segments, seq_num

def create_mongodb_packet(src, dst, sport, dport, seq, ack, payload, flags="PA"):
    """创建MongoDB数据包"""
    eth = Ether(type=0x0800)
    ip = IP(src=src, dst=dst)
    tcp = TCP(sport=sport, dport=dport, seq=seq, ack=ack, flags=flags)
    return eth/ip/tcp/payload

def create_tcp_handshake(src, dst, sport, dport):
    """创建TCP三次握手数据包（不丢失）"""
    packets = []
    client_isn = random.randint(1000000, 9000000)
    server_isn = random.randint(1000000, 9000000)
    
    syn = create_mongodb_packet(src, dst, sport, dport, client_isn, 0, b"", flags="S")
    packets.append(syn)
    syn_ack = create_mongodb_packet(dst, src, dport, sport, server_isn, client_isn + 1, b"", flags="SA")
    packets.append(syn_ack)
    ack = create_mongodb_packet(src, dst, sport, dport, client_isn + 1, server_isn + 1, b"", flags="A")
    packets.append(ack)
    
    return packets, client_isn + 1, server_isn + 1

def create_tcp_teardown(src, dst, sport, dport, seq, ack):
    """创建TCP四次挥手数据包（不丢失）"""
    packets = []
    fin_ack1 = create_mongodb_packet(src, dst, sport, dport, seq, ack, b"", flags="FA")
    packets.append(fin_ack1)
    ack1 = create_mongodb_packet(dst, src, dport, sport, ack, seq + 1, b"", flags="A")
    packets.append(ack1)
    fin_ack2 = create_mongodb_packet(dst, src, dport, sport, ack, seq + 1, b"", flags="FA")
    packets.append(fin_ack2)
    ack2 = create_mongodb_packet(src, dst, sport, dport, seq + 1, ack + 1, b"", flags="A")
    packets.append(ack2)
    return packets

def write_lost_pcap(packets, filename):
    """将数据包写入PCAP文件"""
    if not os.path.exists(LOST_OUTPUT_DIR):
        os.makedirs(LOST_OUTPUT_DIR)
    filepath = os.path.join(LOST_OUTPUT_DIR, filename)
    wrpcap(filepath, packets)
    print(f"已生成丢包测试文件: {filepath}")

def reset_global_counters():
    """重置全局计数器"""
    global client_port, seq_num, ack_num
    client_port = random.randint(40000, 65000)
    seq_num = random.randint(1000000, 9000000)
    ack_num = random.randint(1000000, 9000000)

def generate_op_query_loss_scenario(lost_segments):
    """生成OP_QUERY消息丢包场景"""
    lost_segments_str = "_".join(map(str, sorted(lost_segments)))
    print(f"\n=== 生成OP_QUERY丢包场景 (丢失第{lost_segments_str}段) ===")
    
    packets = []
    global seq_num, ack_num
    reset_global_counters()
    
    # TCP三次握手（不丢失）
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造OP_QUERY消息
    request_id = 1
    op_code = 2004
    flags = 0
    full_collection_name = b"test.inventory\x00"
    
    bson_query = BSON.encode({'status': 'A'})
    bson_return_fields_selector = BSON.encode(OrderedDict([("item", 1), ("_id", 0)]))
    
    query_body = (
        struct.pack("<i", flags) + full_collection_name +
        struct.pack("<i", 0) + struct.pack("<i", 2) +
        bson_query + bson_return_fields_selector
    )
    
    msg_length = 16 + len(query_body)
    op_query_msg = (
        struct.pack("<iiii", msg_length, request_id, 0, op_code) + query_body
    )
    
    # 发送查询消息（带丢包）
    query_segments, new_seq_num = create_tcp_segments_with_loss(
        CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_query_msg, lost_segments)
    packets.extend(query_segments)
    seq_num = new_seq_num
    
    # 服务器响应（正常，不丢包）
    if len(lost_segments) < 3:
        doc1 = BSON.encode(OrderedDict([("_id", ObjectId(b'\x5f\x1e\x7d\x9b\x1e\x9f\x1a\x2b\x3c\x4d\x5e\x6f')), ("status", "A"), ("item", "canvas")]))
        doc2 = BSON.encode(OrderedDict([("_id", ObjectId(b'\x5f\x1e\x7d\x9b\x1e\x9f\x1a\x2b\x3c\x4d\x5e\x70')), ("status", "A"), ("item", "brush")]))
        
        reply_body = (
            struct.pack("<i", 0) + struct.pack("<q", 0) + struct.pack("<i", 0) + 
            struct.pack("<i", 2) + doc1 + doc2
        )
        
        op_reply_msg = struct.pack("<iiii", 16 + len(reply_body), 2, request_id, 1) + reply_body
        s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_reply_msg)
        packets.append(s_packet)
        ack_num += len(op_reply_msg)
    
    # TCP四次挥手（不丢失）
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    filename = f"mongodb_op_query_segments_{lost_segments_str}_lost.pcap"
    write_lost_pcap(packets, filename)

def generate_op_msg_loss_scenario(lost_segments):
    """生成OP_MSG消息丢包场景"""
    lost_segments_str = "_".join(map(str, sorted(lost_segments)))
    print(f"\n=== 生成OP_MSG丢包场景 (丢失第{lost_segments_str}段) ===")
    
    packets = []
    global seq_num, ack_num
    reset_global_counters()
    
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造OP_MSG消息
    command_document = BSON.encode(OrderedDict([
        ("find", "inventory"), ("filter", {"status": "A"}),
        ("projection", OrderedDict([("item", 1), ("_id", 0)])),
        ("batchSize", 2), ("$db", "test")
    ]))
    
    msg_body = struct.pack("<I", 0) + struct.pack("<B", 0) + command_document
    op_msg_request = struct.pack("<iiii", 16 + len(msg_body), 11, 0, 2013) + msg_body
    
    # 发送OP_MSG消息（带丢包）
    query_segments, new_seq_num = create_tcp_segments_with_loss(
        CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_msg_request, lost_segments)
    packets.extend(query_segments)
    seq_num = new_seq_num
    
    # 服务器响应
    if len(lost_segments) < 3:
        reply_document = BSON.encode(OrderedDict([
            ("cursor", OrderedDict([("id", 0), ("ns", "test.inventory"), ("firstBatch", [{"item": "canvas"}, {"item": "brush"}])])),
            ("ok", 1)
        ]))
        
        reply_msg_body = struct.pack("<I", 0) + struct.pack("<B", 0) + reply_document
        op_msg_reply = struct.pack("<iiii", 16 + len(reply_msg_body), 12, 11, 2013) + reply_msg_body
        
        s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_msg_reply)
        packets.append(s_packet)
        ack_num += len(op_msg_reply)
    
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    filename = f"mongodb_op_msg_segments_{lost_segments_str}_lost.pcap"
    write_lost_pcap(packets, filename)

def generate_op_compressed_loss_scenario(lost_segments):
    """生成OP_COMPRESSED消息丢包场景"""
    lost_segments_str = "_".join(map(str, sorted(lost_segments)))
    print(f"\n=== 生成OP_COMPRESSED丢包场景 (丢失第{lost_segments_str}段) ===")
    
    packets = []
    global seq_num, ack_num
    reset_global_counters()
    
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造压缩消息
    original_query_body = (
        struct.pack("<i", 0) + b"test.inventory\x00" + struct.pack("<i", 0) + struct.pack("<i", 2) +
        BSON.encode({"status": "A"}) + BSON.encode(OrderedDict([("item", 1), ("_id", 0)]))
    )
    
    compressed_body = zlib.compress(original_query_body)
    op_compressed_msg = (
        struct.pack("<iiii", 16 + 4 + 4 + 1 + len(compressed_body), 1, 0, 2012) +
        struct.pack("<i", 2004) + struct.pack("<i", len(original_query_body)) +
        struct.pack("<B", 2) + compressed_body
    )
    
    # 发送压缩消息（带丢包）
    query_segments, new_seq_num = create_tcp_segments_with_loss(
        CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_compressed_msg, lost_segments)
    packets.extend(query_segments)
    seq_num = new_seq_num
    
    # 服务器响应
    if len(lost_segments) < 3:
        doc1 = BSON.encode(OrderedDict([("_id", ObjectId(b'\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c')), ("status", "A"), ("item", "canvas")]))
        doc2 = BSON.encode(OrderedDict([("_id", ObjectId(b'\x0d\x0e\x0f\x10\x11\x12\x13\x14\x15\x16\x17\x18')), ("status", "A"), ("item", "brush")]))
        
        reply = (
            struct.pack("<iiii", 16 + 4 + 8 + 4 + 4 + len(doc1 + doc2), 2, 1, 1) +
            struct.pack("<i", 0) + struct.pack("<q", 0) + struct.pack("<i", 0) + 
            struct.pack("<i", 2) + doc1 + doc2
        )
        
        s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, reply)
        packets.append(s_packet)
        ack_num += len(reply)
    
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    filename = f"mongodb_op_compressed_segments_{lost_segments_str}_lost.pcap"
    write_lost_pcap(packets, filename)

def generate_enhanced_mongodb_scenarios():
    """生成增强的MongoDB丢包场景（所有分段组合）"""
    print("\n=== 生成增强的MongoDB丢包场景 ===")
    
    loss_combinations = [
        ([1], "first_segment_lost"),
        ([2], "second_segment_lost"),
        ([3], "third_segment_lost"),
        ([1, 2], "first_second_segments_lost"),
        ([2, 3], "second_third_segments_lost"),
        ([1, 3], "first_third_segments_lost"),
        ([1, 2, 3], "all_segments_lost")
    ]
    
    scenarios = [
        ("op_query", generate_op_query_loss_scenario),
        ("op_msg", generate_op_msg_loss_scenario),
        ("op_compressed", generate_op_compressed_loss_scenario)
    ]
    
    for scenario_name, scenario_func in scenarios:
        print(f"\n--- 生成{scenario_name}场景的所有分段丢失变体 ---")
        for lost_segments, description in loss_combinations:
            try:
                scenario_func(lost_segments)
                print(f"✓ {scenario_name} - {description} 完成")
            except Exception as e:
                print(f"✗ {scenario_name} - {description} 失败: {e}")

def print_usage():
    """打印使用说明"""
    print("MongoDB协议TCP丢包测试场景生成器")
    print("用法:")
    print("  python3 generate_mongo_lost_pcap.py --all              # 生成所有丢包场景")
    print("  python3 generate_mongo_lost_pcap.py --enhanced         # 生成增强丢包场景")
    print("  python3 generate_mongo_lost_pcap.py --list             # 列出可用场景")

def main():
    """主函数"""
    if len(sys.argv) == 1 or "--help" in sys.argv:
        print_usage()
    elif "--all" in sys.argv or "--enhanced" in sys.argv:
        generate_enhanced_mongodb_scenarios()
        print(f"\n所有丢包测试文件已生成到 {LOST_OUTPUT_DIR} 目录")
        if os.path.exists(LOST_OUTPUT_DIR):
            files = [f for f in os.listdir(LOST_OUTPUT_DIR) if f.endswith('.pcap')]
            print(f"共生成 {len(files)} 个PCAP文件:")
            for i, file in enumerate(sorted(files), 1):
                print(f"  {i}. {file}")
    elif "--list" in sys.argv:
        print("可用的丢包测试场景:")
        scenarios = ["op_query", "op_msg", "op_compressed"]
        combinations = ["first_segment", "second_segment", "third_segment", 
                       "first_second_segments", "second_third_segments", 
                       "first_third_segments", "all_segments"]
        for scenario in scenarios:
            for combo in combinations:
                print(f"  - {scenario}_{combo}_lost")
    else:
        print("错误: 无效的命令行参数")
        print_usage()

if __name__ == "__main__":
    main()