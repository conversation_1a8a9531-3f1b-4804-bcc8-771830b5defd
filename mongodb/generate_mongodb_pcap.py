#!/usr/bin/env python3
from scapy.all import *
from scapy.layers.inet import IP, TCP
import struct
import random
import os
import zlib

# MongoDB 默认端口
MONGODB_PORT = 27017

# 客户端和服务器IP地址
CLIENT_IP = "************"
SERVER_IP = "************"

# 随机端口和序列号
client_port = random.randint(40000, 65000)
seq_num = random.randint(1000000, 9000000)
ack_num = random.randint(1000000, 9000000)

# 输出目录
output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "mongodb_pcaps")

def create_mongodb_packet(src, dst, sport, dport, seq, ack, payload, flags="PA"):
    """创建一个MongoDB数据包"""
    eth = Ether(type=0x0800)  # IPv4类型
    ip = IP(src=src, dst=dst)
    tcp = TCP(sport=sport, dport=dport, seq=seq, ack=ack, flags=flags)
    return eth/ip/tcp/payload

def create_tcp_handshake(src, dst, sport, dport):
    """创建TCP三次握手数据包"""
    packets = []
    # 初始序列号
    client_isn = random.randint(1000000, 9000000)
    server_isn = random.randint(1000000, 9000000)
    
    # SYN
    syn = create_mongodb_packet(src, dst, sport, dport, client_isn, 0, b"", flags="S")
    packets.append(syn)
    
    # SYN-ACK
    syn_ack = create_mongodb_packet(dst, src, dport, sport, server_isn, client_isn + 1, b"", flags="SA")
    packets.append(syn_ack)
    
    # ACK
    ack = create_mongodb_packet(src, dst, sport, dport, client_isn + 1, server_isn + 1, b"", flags="A")
    packets.append(ack)
    
    return packets, client_isn + 1, server_isn + 1

def create_tcp_teardown(src, dst, sport, dport, seq, ack):
    """创建TCP四次挥手数据包"""
    packets = []
    
    # FIN-ACK (client)
    fin_ack1 = create_mongodb_packet(src, dst, sport, dport, seq, ack, b"", flags="FA")
    packets.append(fin_ack1)
    
    # ACK (server)
    ack1 = create_mongodb_packet(dst, src, dport, sport, ack, seq + 1, b"", flags="A")
    packets.append(ack1)
    
    # FIN-ACK (server)
    fin_ack2 = create_mongodb_packet(dst, src, dport, sport, ack, seq + 1, b"", flags="FA")
    packets.append(fin_ack2)
    
    # ACK (client)
    ack2 = create_mongodb_packet(src, dst, sport, dport, seq + 1, ack + 1, b"", flags="A")
    packets.append(ack2)
    
    return packets

def write_pcap(packets, filename):
    """将数据包写入PCAP文件"""
    wrpcap(os.path.join(output_dir, filename), packets)
    print(f"已生成 {filename}")

def generate_op_compressed_scenario():
    """生成OP_COMPRESSED消息场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造原始OP_QUERY消息体（不含MsgHeader），参考 refer.md
    op_query_flags = 0
    op_query_full_collection_name = b"test.inventory\x00"
    op_query_number_to_skip = 0
    op_query_number_to_return = 2
    # BSON query: {"status": "A"}
    op_query_bson_query = b"\x13\x00\x00\x00\x02status\x00\x02\x00\x00\x00A\x00\x00"
    # BSON returnFieldsSelector: {"item": 1, "_id": 0}
    op_query_bson_return_fields_selector = b"\x18\x00\x00\x00\x10item\x00\x01\x00\x00\x00\x10_id\x00\x00\x00\x00\x00\x00"

    original_query_body = (
        struct.pack("<i", op_query_flags) +
        op_query_full_collection_name +
        struct.pack("<i", op_query_number_to_skip) +
        struct.pack("<i", op_query_number_to_return) +
        op_query_bson_query +
        op_query_bson_return_fields_selector
    )

    # 计算原始消息的总长度（包括MsgHeader）
    original_msg_length = 16 + len(original_query_body)  # 16字节的MsgHeader + 消息体

    # 压缩原始消息体
    compressed_body = zlib.compress(original_query_body)

    # 构造OP_COMPRESSED消息
    # MsgHeader (16字节)
    # OP_COMPRESSED header: originalOpcode (4) + uncompressedSize (4) + compressorId (1) = 9 bytes
    # Total msg_length = 16 (MsgHeader) + 9 (OP_COMPRESSED specific header) + len(compressed_body)
    msg_length = 16 + 4 + 4 + 1 + len(compressed_body)
    request_id = 1 # 对应 refer.md 示例
    response_to = 0
    op_code = 2012  # OP_COMPRESSED

    # OP_COMPRESSED消息体
    original_opcode = 2004  # OP_QUERY
    uncompressed_size = len(original_query_body)
    compressor_id = 2  # 0: noop, 1: snappy, 2: zlib, 3: zstd

    # 组装完整的OP_COMPRESSED消息
    op_compressed_msg = (
        struct.pack("<iiii", msg_length, request_id, response_to, op_code) +  # MsgHeader
        struct.pack("<i", original_opcode) +  # 原始消息类型
        struct.pack("<i", uncompressed_size) +  # 解压前的消息长度
        struct.pack("<B", compressor_id) +  # 压缩算法标识 (uint8_t)
        compressed_body  # 压缩后的数据
    )

    # 客户端发送OP_COMPRESSED消息
    c_packet = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_compressed_msg)
    packets.append(c_packet)
    seq_num += len(op_compressed_msg)

    # 服务器响应 OP_REPLY，参考 refer.md
    reply_response_flags = 0
    reply_cursor_id = 0
    reply_starting_from = 0
    reply_number_returned = 2
    # BSON documents (示例)
    # 第一个文档: {"_id": ObjectId("0102030405060708090a0b0c"), "status": "A", "item": "canvas"}
    doc1 = (
        b'\x35\x00\x00\x00'  # BSON size (53 bytes)
        b'\x07_id\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c' # ObjectId
        b'\x02status\x00\x02\x00\x00\x00A\x00' # status: "A"
        b'\x02item\x00\x07\x00\x00\x00canvas\x00' # item: "canvas"
        b'\x00' # BSON EOO
    )
    # 第二个文档: {"_id": ObjectId("0d0e0f101112131415161718"), "status": "A", "item": "brush"}
    doc2 = (
        b'\x34\x00\x00\x00'  # BSON size (52 bytes)
        b'\x07_id\x00\x0d\x0e\x0f\x10\x11\x12\x13\x14\x15\x16\x17\x18' # ObjectId
        b'\x02status\x00\x02\x00\x00\x00A\x00' # status: "A"
        b'\x02item\x00\x06\x00\x00\x00brush\x00' # item: "brush"
        b'\x00' # BSON EOO
    )
    reply_documents = doc1 + doc2

    reply_body_len = 4 + 8 + 4 + 4 + len(reply_documents) # responseFlags, cursorID, startingFrom, numberReturned, documents
    reply_msg_len = 16 + reply_body_len # MsgHeader + body
    reply_request_id = 2 # 服务器生成的新ID
    reply_response_to = request_id # 对应客户端的requestID
    reply_op_code = 1 # OP_REPLY

    reply_header = struct.pack("<iiii", reply_msg_len, reply_request_id, reply_response_to, reply_op_code)
    reply_body_packed = (
        struct.pack("<i", reply_response_flags) +
        struct.pack("<q", reply_cursor_id) +
        struct.pack("<i", reply_starting_from) +
        struct.pack("<i", reply_number_returned) +
        reply_documents
    )
    reply = reply_header + reply_body_packed
    
    s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, reply)
    packets.append(s_packet)
    ack_num += len(reply)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_op_compressed.pcap")

def generate_op_compressed_with_example_scenario():
    """生成与示例完全匹配的OP_COMPRESSED消息场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 使用示例中的压缩数据
    compressed_data = bytes([
        0x78, 0x9c, 0x4b, 0xcf, 0x4f, 0x4d, 0x4d, 0x2d,
        0x2a, 0xca, 0x2f, 0x49, 0x2d, 0x2e, 0x51, 0xb0,
        0xd1, 0x51, 0x48, 0x2c, 0xc9, 0x48, 0xcd, 0xc9,
        0xc9, 0x4f, 0x51, 0xd0, 0x4b, 0x2a, 0xca, 0x2f,
        0x49, 0x2d, 0x2e, 0x01, 0x00, 0x1c, 0x0b, 0x04, 0x5d
    ])
    
    # 构造OP_COMPRESSED消息
    # MsgHeader (16字节)
    # OP_COMPRESSED header: originalOpcode (4) + uncompressedSize (4) + compressorId (1) = 9 bytes
    # Total msg_length = 16 (MsgHeader) + 9 (OP_COMPRESSED specific header) + len(compressed_data)
    msg_length = 16 + 4 + 4 + 1 + len(compressed_data)
    request_id = 1234
    response_to = 0
    op_code = 2012  # OP_COMPRESSED
    
    # OP_COMPRESSED消息体
    original_opcode = 2004  # OP_QUERY
    uncompressed_size = 96  # 示例中提到的原始长度
    compressor_id = 2  # 0: noop, 1: snappy, 2: zlib, 3: zstd
    
    # 组装完整的OP_COMPRESSED消息
    op_compressed_msg = (
        struct.pack("<iiii", msg_length, request_id, response_to, op_code) +  # MsgHeader
        struct.pack("<i", original_opcode) +  # 原始消息类型
        struct.pack("<i", uncompressed_size) +  # 解压前的消息长度
        struct.pack("<B", compressor_id) +  # 压缩算法标识 (uint8_t)
        compressed_data  # 压缩后的数据
    )
    
    # 客户端发送OP_COMPRESSED消息
    c_packet = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_compressed_msg)
    packets.append(c_packet)
    seq_num += len(op_compressed_msg)
    
    # 服务器响应 OP_REPLY，参考 refer.md
    reply_response_flags = 0
    reply_cursor_id = 0
    reply_starting_from = 0
    reply_number_returned = 2 # 假设返回2个文档，与 refer.md 示例一致
    # BSON documents (示例)
    # 第一个文档: {"_id": ObjectId("0102030405060708090a0b0c"), "status": "A", "item": "canvas"}
    doc1 = (
        b'\x35\x00\x00\x00'  # BSON size (53 bytes)
        b'\x07_id\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c' # ObjectId
        b'\x02status\x00\x02\x00\x00\x00A\x00' # status: "A"
        b'\x02item\x00\x07\x00\x00\x00canvas\x00' # item: "canvas"
        b'\x00' # BSON EOO
    )
    # 第二个文档: {"_id": ObjectId("0d0e0f101112131415161718"), "status": "A", "item": "brush"}
    doc2 = (
        b'\x34\x00\x00\x00'  # BSON size (52 bytes)
        b'\x07_id\x00\x0d\x0e\x0f\x10\x11\x12\x13\x14\x15\x16\x17\x18' # ObjectId
        b'\x02status\x00\x02\x00\x00\x00A\x00' # status: "A"
        b'\x02item\x00\x06\x00\x00\x00brush\x00' # item: "brush"
        b'\x00' # BSON EOO
    )
    reply_documents = doc1 + doc2

    reply_body_len = 4 + 8 + 4 + 4 + len(reply_documents) # responseFlags, cursorID, startingFrom, numberReturned, documents
    reply_msg_len = 16 + reply_body_len # MsgHeader + body
    reply_request_id_server = 2 # 服务器生成的新ID，与 refer.md 示例一致
    reply_response_to = request_id # 对应客户端的requestID
    reply_op_code = 1 # OP_REPLY

    reply_header = struct.pack("<iiii", reply_msg_len, reply_request_id_server, reply_response_to, reply_op_code)
    reply_body_packed = (
        struct.pack("<i", reply_response_flags) +
        struct.pack("<q", reply_cursor_id) +
        struct.pack("<i", reply_starting_from) +
        struct.pack("<i", reply_number_returned) +
        reply_documents
    )
    reply = reply_header + reply_body_packed
    
    s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, reply)
    packets.append(s_packet)
    ack_num += len(reply)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_op_compressed_example.pcap")

def generate_op_query_scenario():
    """生成OP_QUERY消息场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造OP_QUERY消息，按照示例规格
    # MsgHeader参数
    request_id = 1  # 客户端自增请求号
    response_to = 0  # 请求时为0
    op_code = 2004  # OP_QUERY
    
    # OP_QUERY消息体参数
    flags = 0  # 标志位，0表示默认
    full_collection_name = b"test.inventory\x00"  # 集合名称，以null结尾
    number_to_skip = 0  # 跳过文档数
    number_to_return = 2  # 本次希望返回的文档数
    
    # BSON query: {"status": "A"}
    # BSON结构: size(4) + field_type(1) + field_name + null(1) + value + null(1) + eoo(1)
    bson_query = (
        b'\x13\x00\x00\x00'  # BSON size (19 bytes)
        b'\x02'  # String type
        b'status\x00'  # field name "status" + null terminator
        b'\x02\x00\x00\x00'  # string length (2 bytes including null)
        b'A\x00'  # string value "A" + null terminator
        b'\x00'  # BSON end-of-object marker
    )
    
    # BSON returnFieldsSelector: {"item": 1, "_id": 0}
    bson_return_fields_selector = (
        b'\x18\x00\x00\x00'  # BSON size (24 bytes)
        b'\x10'  # Int32 type
        b'item\x00'  # field name "item" + null terminator
        b'\x01\x00\x00\x00'  # int32 value 1
        b'\x10'  # Int32 type
        b'_id\x00'  # field name "_id" + null terminator
        b'\x00\x00\x00\x00'  # int32 value 0
        b'\x00'  # BSON end-of-object marker
    )
    
    # 组装OP_QUERY消息体
    query_body = (
        struct.pack("<i", flags) +  # flags
        full_collection_name +  # fullCollectionName
        struct.pack("<i", number_to_skip) +  # numberToSkip
        struct.pack("<i", number_to_return) +  # numberToReturn
        bson_query +  # query BSON
        bson_return_fields_selector  # returnFieldsSelector BSON
    )
    
    # 计算消息总长度（MsgHeader + 消息体）
    msg_length = 16 + len(query_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_QUERY消息
    op_query_msg = (
        struct.pack("<iiii", msg_length, request_id, response_to, op_code) +  # MsgHeader
        query_body  # OP_QUERY消息体
    )
    
    # 客户端发送OP_QUERY消息
    c_packet = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_query_msg)
    packets.append(c_packet)
    seq_num += len(op_query_msg)
    
    # 服务器响应OP_REPLY，按照示例规格
    # MsgHeader参数
    reply_request_id = 2  # 服务器给本次响应分配的新ID
    reply_response_to = request_id  # 对应客户端的requestID
    reply_op_code = 1  # OP_REPLY
    
    # OP_REPLY消息体参数
    response_flags = 0  # 响应标志，0表示正常
    cursor_id = 0  # 服务器游标ID（0表示已关闭）
    starting_from = 0  # 本批文档中第一条在整个结果集中的偏移
    number_returned = 2  # 实际返回文档数
    
    # BSON documents
    # 第一个文档: {"_id": ObjectId("5f1e7d9b1e9f1a2b3c4d5e6f"), "status": "A", "item": "canvas"}
    doc1 = (
        b'\x35\x00\x00\x00'  # BSON size (53 bytes)
        b'\x07_id\x00\x5f\x1e\x7d\x9b\x1e\x9f\x1a\x2b\x3c\x4d\x5e\x6f'  # ObjectId field
        b'\x02status\x00\x02\x00\x00\x00A\x00'  # String field "status": "A"
        b'\x02item\x00\x07\x00\x00\x00canvas\x00'  # String field "item": "canvas"
        b'\x00'  # BSON end-of-object marker
    )
    
    # 第二个文档: {"_id": ObjectId("5f1e7d9b1e9f1a2b3c4d5e70"), "status": "A", "item": "brush"}
    doc2 = (
        b'\x34\x00\x00\x00'  # BSON size (52 bytes)
        b'\x07_id\x00\x5f\x1e\x7d\x9b\x1e\x9f\x1a\x2b\x3c\x4d\x5e\x70'  # ObjectId field
        b'\x02status\x00\x02\x00\x00\x00A\x00'  # String field "status": "A"
        b'\x02item\x00\x06\x00\x00\x00brush\x00'  # String field "item": "brush"
        b'\x00'  # BSON end-of-object marker
    )
    
    reply_documents = doc1 + doc2
    
    # 组装OP_REPLY消息体
    reply_body = (
        struct.pack("<i", response_flags) +  # responseFlags
        struct.pack("<q", cursor_id) +  # cursorID (8字节长整型)
        struct.pack("<i", starting_from) +  # startingFrom
        struct.pack("<i", number_returned) +  # numberReturned
        reply_documents  # documents
    )
    
    # 计算回复消息总长度
    reply_msg_length = 16 + len(reply_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_REPLY消息
    op_reply_msg = (
        struct.pack("<iiii", reply_msg_length, reply_request_id, reply_response_to, reply_op_code) +  # MsgHeader
        reply_body  # OP_REPLY消息体
    )
    
    # 服务器发送OP_REPLY消息
    s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_reply_msg)
    packets.append(s_packet)
    ack_num += len(op_reply_msg)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_op_query.pcap")

def generate_op_insert_scenario():
    """生成OP_INSERT消息场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造OP_INSERT消息，按照示例规格
    # MsgHeader参数
    request_id = 5  # 客户端请求号
    response_to = 0  # 请求时固定为0
    op_code = 2002  # OP_INSERT
    
    # OP_INSERT消息体参数
    flags = 0  # 标志位，0表示默认
    full_collection_name = b"test.inventory\x00"  # 集合名称，以null结尾
    
    # BSON document1: {"item": "journal", "qty": 25, "tags": ["blank", "red"]}
    # 构造tags数组: ["blank", "red"]
    tags_array1 = (
        b'\x1d\x00\x00\x00'  # 数组BSON大小 (29 bytes)
        b'\x02\x30\x00\x06\x00\x00\x00blank\x00'  # "0": "blank" (14 bytes: 1+2+4+6)
        b'\x02\x31\x00\x04\x00\x00\x00red\x00'  # "1": "red" (12 bytes: 1+2+4+4)
        b'\x00'  # 数组结束标记
    )
    
    document1 = (
        b'\x43\x00\x00\x00'  # BSON size (67 bytes)
        b'\x02item\x00\x08\x00\x00\x00journal\x00'  # String field "item": "journal" (16 bytes: 1+5+4+8)
        b'\x10qty\x00\x19\x00\x00\x00'  # Int32 field "qty": 25 (9 bytes: 1+4+4)
        b'\x04tags\x00' + tags_array1 +  # Array field "tags" (6 bytes prefix + 29 bytes array)
        b'\x00'  # BSON end-of-object marker
    )
    
    # BSON document2: {"item": "notebook", "qty": 50, "tags": ["red", "blank"]}
    # 构造tags数组: ["red", "blank"]
    tags_array2 = (
        b'\x1d\x00\x00\x00'  # 数组BSON大小 (29 bytes)
        b'\x02\x30\x00\x04\x00\x00\x00red\x00'  # "0": "red" (12 bytes)
        b'\x02\x31\x00\x06\x00\x00\x00blank\x00'  # "1": "blank" (14 bytes)
        b'\x00'  # 数组结束标记
    )
    
    document2 = (
        b'\x44\x00\x00\x00'  # BSON size (68 bytes)
        b'\x02item\x00\x09\x00\x00\x00notebook\x00'  # String field "item": "notebook" (17 bytes: 1+5+4+9)
        b'\x10qty\x00\x32\x00\x00\x00'  # Int32 field "qty": 50 (9 bytes: 1+4+4)
        b'\x04tags\x00' + tags_array2 +  # Array field "tags" (6 bytes prefix + 29 bytes array)
        b'\x00'  # BSON end-of-object marker
    )
    
    # 组装OP_INSERT消息体
    insert_body = (
        struct.pack("<i", flags) +  # flags
        full_collection_name +  # fullCollectionName
        document1 +  # document1
        document2   # document2
    )
    
    # 计算消息总长度（MsgHeader + 消息体）
    msg_length = 16 + len(insert_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_INSERT消息
    op_insert_msg = (
        struct.pack("<iiii", msg_length, request_id, response_to, op_code) +  # MsgHeader
        insert_body  # OP_INSERT消息体
    )
    
    # 客户端发送OP_INSERT消息
    c_packet = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_insert_msg)
    packets.append(c_packet)
    seq_num += len(op_insert_msg)
    
    # 服务器响应OP_REPLY，按照示例规格
    # MsgHeader参数
    reply_request_id = 6  # 服务器给本次响应分配的新ID
    reply_response_to = request_id  # 对应客户端的requestID
    reply_op_code = 1  # OP_REPLY
    
    # OP_REPLY消息体参数
    response_flags = 0  # 响应标志，0表示正常
    cursor_id = 0  # 写操作不使用游标
    starting_from = 0  # 起始位置
    number_returned = 1  # 返回文档数（写操作统一封装成一个文档）
    
    # BSON response document: {"ok": 1, "n": 2}
    response_document = (
        b'\x14\x00\x00\x00'  # BSON size (20 bytes)
        b'\x10ok\x00\x01\x00\x00\x00'  # Int32 field "ok": 1 (8 bytes: 1+3+4)
        b'\x10n\x00\x02\x00\x00\x00'  # Int32 field "n": 2 (7 bytes: 1+2+4)
        b'\x00'  # BSON end-of-object marker
    )
    
    # 组装OP_REPLY消息体
    reply_body = (
        struct.pack("<i", response_flags) +  # responseFlags
        struct.pack("<q", cursor_id) +  # cursorID (8字节长整型)
        struct.pack("<i", starting_from) +  # startingFrom
        struct.pack("<i", number_returned) +  # numberReturned
        response_document  # response document
    )
    
    # 计算回复消息总长度
    reply_msg_length = 16 + len(reply_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_REPLY消息
    op_reply_msg = (
        struct.pack("<iiii", reply_msg_length, reply_request_id, reply_response_to, reply_op_code) +  # MsgHeader
        reply_body  # OP_REPLY消息体
    )
    
    # 服务器发送OP_REPLY消息
    s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_reply_msg)
    packets.append(s_packet)
    ack_num += len(op_reply_msg)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_op_insert.pcap")

def generate_op_update_scenario():
    """生成OP_UPDATE消息场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造OP_UPDATE消息，按照示例规格
    # MsgHeader参数
    request_id = 7  # 客户端请求号
    response_to = 0  # 请求时固定为0
    op_code = 2001  # OP_UPDATE
    
    # OP_UPDATE消息体参数
    zero = 0  # 保留字段，必须为0
    full_collection_name = b"test.inventory\x00"  # 集合名称，以null结尾
    flags = 0  # 0表示默认（只更新第一条匹配）
    
    # BSON selector: {"item": "journal"}
    selector = (
        b'\x17\x00\x00\x00'  # BSON size (23 bytes)
        b'\x02item\x00\x08\x00\x00\x00journal\x00'  # String field "item": "journal" (18 bytes: 1+5+4+8)
        b'\x00'  # BSON end-of-object marker
    )
    
    # BSON update: {"$inc": {"qty": 10}}
    # 构造内嵌文档 {"qty": 10}
    inc_doc = (
        b'\x0e\x00\x00\x00'  # 内嵌文档BSON大小 (14 bytes)
        b'\x10qty\x00\x0a\x00\x00\x00'  # Int32 field "qty": 10 (9 bytes: 1+4+4)
        b'\x00'  # 内嵌文档结束标记
    )
    
    update = (
        b'\x19\x00\x00\x00'  # BSON size (25 bytes)
        b'\x03$inc\x00' + inc_doc +  # Document field "$inc" (6 bytes prefix + 14 bytes doc)
        b'\x00'  # BSON end-of-object marker
    )
    
    # 组装OP_UPDATE消息体
    update_body = (
        struct.pack("<i", zero) +  # ZERO
        full_collection_name +  # fullCollectionName
        struct.pack("<i", flags) +  # flags
        selector +  # selector BSON
        update   # update BSON
    )
    
    # 计算消息总长度（MsgHeader + 消息体）
    msg_length = 16 + len(update_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_UPDATE消息
    op_update_msg = (
        struct.pack("<iiii", msg_length, request_id, response_to, op_code) +  # MsgHeader
        update_body  # OP_UPDATE消息体
    )
    
    # 客户端发送OP_UPDATE消息
    c_packet = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_update_msg)
    packets.append(c_packet)
    seq_num += len(op_update_msg)
    
    # 服务器响应OP_REPLY，按照示例规格
    # MsgHeader参数
    reply_request_id = 8  # 服务器给本次响应分配的新ID
    reply_response_to = request_id  # 对应客户端的requestID
    reply_op_code = 1  # OP_REPLY
    
    # OP_REPLY消息体参数
    response_flags = 0  # 响应标志，0表示正常
    cursor_id = 0  # 更新操作不使用游标
    starting_from = 0  # 起始位置
    number_returned = 1  # 返回文档数（更新操作统一封装成一个文档）
    
    # BSON response document: {"ok": 1, "n": 1, "nModified": 1}
    response_document = (
        b'\x23\x00\x00\x00'  # BSON size (35 bytes)
        b'\x10ok\x00\x01\x00\x00\x00'  # Int32 field "ok": 1 (8 bytes: 1+3+4)
        b'\x10n\x00\x01\x00\x00\x00'  # Int32 field "n": 1 (7 bytes: 1+2+4)
        b'\x10nModified\x00\x01\x00\x00\x00'  # Int32 field "nModified": 1 (15 bytes: 1+10+4)
        b'\x00'  # BSON end-of-object marker
    )
    
    # 组装OP_REPLY消息体
    reply_body = (
        struct.pack("<i", response_flags) +  # responseFlags
        struct.pack("<q", cursor_id) +  # cursorID (8字节长整型)
        struct.pack("<i", starting_from) +  # startingFrom
        struct.pack("<i", number_returned) +  # numberReturned
        response_document  # response document
    )
    
    # 计算回复消息总长度
    reply_msg_length = 16 + len(reply_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_REPLY消息
    op_reply_msg = (
        struct.pack("<iiii", reply_msg_length, reply_request_id, reply_response_to, reply_op_code) +  # MsgHeader
        reply_body  # OP_REPLY消息体
    )
    
    # 服务器发送OP_REPLY消息
    s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_reply_msg)
    packets.append(s_packet)
    ack_num += len(op_reply_msg)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_op_update.pcap")

def generate_op_delete_scenario():
    """生成OP_DELETE消息场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造OP_DELETE消息，按照示例规格
    # MsgHeader参数
    request_id = 9  # 客户端请求号
    response_to = 0  # 请求时固定为0
    op_code = 2006  # OP_DELETE
    
    # OP_DELETE消息体参数
    zero = 0  # 保留字段，必须为0
    full_collection_name = b"test.inventory\x00"  # 集合名称，以null结尾
    flags = 0  # 0：只删除第一条匹配；1：删除所有匹配
    
    # BSON selector: {"status": "D"}
    # 计算正确的BSON大小：
    # - BSON大小: 4字节
    # - 字段类型: 1字节 (0x02 = String)
    # - 字段名: "status" + null = 7字节
    # - 字符串长度: 4字节
    # - 字符串值: "D" + null = 2字节
    # - 结束标记: 1字节
    # 总计: 4 + 1 + 7 + 4 + 2 + 1 = 19字节
    selector = (
        b'\x13\x00\x00\x00'  # BSON size (19 bytes)
        b'\x02status\x00\x02\x00\x00\x00D\x00'  # String field "status": "D"
        b'\x00'  # BSON end-of-object marker
    )
    
    # 组装OP_DELETE消息体
    delete_body = (
        struct.pack("<i", zero) +  # ZERO
        full_collection_name +  # fullCollectionName
        struct.pack("<i", flags) +  # flags
        selector   # selector BSON
    )
    
    # 计算消息总长度（MsgHeader + 消息体）
    msg_length = 16 + len(delete_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_DELETE消息
    op_delete_msg = (
        struct.pack("<iiii", msg_length, request_id, response_to, op_code) +  # MsgHeader
        delete_body  # OP_DELETE消息体
    )
    
    # 客户端发送OP_DELETE消息
    c_packet = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_delete_msg)
    packets.append(c_packet)
    seq_num += len(op_delete_msg)
    
    # 服务器响应OP_REPLY，按照示例规格
    # MsgHeader参数
    reply_request_id = 10  # 服务器给本次响应分配的新ID
    reply_response_to = request_id  # 对应客户端的requestID
    reply_op_code = 1  # OP_REPLY
    
    # OP_REPLY消息体参数
    response_flags = 0  # 响应标志，0表示正常
    cursor_id = 0  # 删除操作不使用游标
    starting_from = 0  # 起始位置
    number_returned = 1  # 返回文档数（删除操作统一封装成一个文档）
    
    # BSON response document: {"ok": 1, "n": 1}
    # 计算正确的BSON大小：
    # - BSON大小: 4字节
    # - "ok": 1 字段: 1(类型) + 3(字段名+null) + 4(int32值) = 8字节
    # - "n": 1 字段: 1(类型) + 2(字段名+null) + 4(int32值) = 7字节
    # - 结束标记: 1字节
    # 总计: 4 + 8 + 7 + 1 = 20字节
    response_document = (
        b'\x14\x00\x00\x00'  # BSON size (20 bytes)
        b'\x10ok\x00\x01\x00\x00\x00'  # Int32 field "ok": 1
        b'\x10n\x00\x01\x00\x00\x00'  # Int32 field "n": 1
        b'\x00'  # BSON end-of-object marker
    )
    
    # 组装OP_REPLY消息体
    reply_body = (
        struct.pack("<i", response_flags) +  # responseFlags
        struct.pack("<q", cursor_id) +  # cursorID (8字节长整型)
        struct.pack("<i", starting_from) +  # startingFrom
        struct.pack("<i", number_returned) +  # numberReturned
        response_document  # response document
    )
    
    # 计算回复消息总长度
    reply_msg_length = 16 + len(reply_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_REPLY消息
    op_reply_msg = (
        struct.pack("<iiii", reply_msg_length, reply_request_id, reply_response_to, reply_op_code) +  # MsgHeader
        reply_body  # OP_REPLY消息体
    )
    
    # 服务器发送OP_REPLY消息
    s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_reply_msg)
    packets.append(s_packet)
    ack_num += len(op_reply_msg)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_op_delete.pcap")

def generate_op_msg_scenario():
    """生成OP_MSG消息场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造OP_MSG消息，按照示例规格
    # MsgHeader参数
    request_id = 11  # 客户端请求号
    response_to = 0  # 请求时固定为0
    op_code = 2013  # OP_MSG
    
    # OP_MSG消息体参数
    flag_bits = 0  # 标志位，0表示默认
    
    # Section 0: Body (Kind 0)
    # BSON command document: {
    #     "find": "inventory",
    #     "filter": { "status": "A" },
    #     "projection": { "item": 1, "_id": 0 },
    #     "batchSize": 2,
    #     "$db": "test"
    # }
    
    # 构造内嵌BSON文档 - filter: { "status": "A" }
    filter_doc = (
        b'\x13\x00\x00\x00'  # BSON size (19 bytes)
        b'\x02status\x00\x02\x00\x00\x00A\x00'  # String field "status": "A"
        b'\x00'  # BSON end-of-object marker
    )
    
    # 构造内嵌BSON文档 - projection: { "item": 1, "_id": 0 }
    projection_doc = (
        b'\x18\x00\x00\x00'  # BSON size (24 bytes)
        b'\x10item\x00\x01\x00\x00\x00'  # Int32 field "item": 1
        b'\x10_id\x00\x00\x00\x00\x00'  # Int32 field "_id": 0
        b'\x00'  # BSON end-of-object marker
    )
    
    # 计算主命令文档的大小
    # - BSON大小: 4字节
    # - "find": "inventory" 字段: 1(类型) + 5(字段名+null) + 4(字符串长度) + 10(字符串值+null) = 20字节
    # - "filter": {...} 字段: 1(类型) + 7(字段名+null) + 19(内嵌文档) = 27字节
    # - "projection": {...} 字段: 1(类型) + 11(字段名+null) + 24(内嵌文档) = 36字节
    # - "batchSize": 2 字段: 1(类型) + 10(字段名+null) + 4(int32值) = 15字节
    # - "$db": "test" 字段: 1(类型) + 4(字段名+null) + 4(字符串长度) + 5(字符串值+null) = 14字节
    # - 结束标记: 1字节
    # 总计: 4 + 20 + 27 + 36 + 15 + 14 + 1 = 117字节
    
    command_document = (
        b'\x75\x00\x00\x00'  # BSON size (117 bytes)
        b'\x02find\x00\x0a\x00\x00\x00inventory\x00'  # String field "find": "inventory"
        b'\x03filter\x00' + filter_doc +  # Document field "filter"
        b'\x03projection\x00' + projection_doc +  # Document field "projection"
        b'\x10batchSize\x00\x02\x00\x00\x00'  # Int32 field "batchSize": 2
        b'\x02$db\x00\x05\x00\x00\x00test\x00'  # String field "$db": "test"
        b'\x00'  # BSON end-of-object marker
    )
    
    # 组装OP_MSG消息体
    # Section 0: Kind 0 (Body)
    section_kind = 0  # Kind 0表示Body section
    msg_body = (
        struct.pack("<I", flag_bits) +  # flagBits (uint32)
        struct.pack("<B", section_kind) +  # Section Kind (1 byte)
        command_document  # Section payload (BSON document)
    )
    
    # 计算消息总长度（MsgHeader + 消息体）
    msg_length = 16 + len(msg_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_MSG消息
    op_msg_request = (
        struct.pack("<iiii", msg_length, request_id, response_to, op_code) +  # MsgHeader
        msg_body  # OP_MSG消息体
    )
    
    # 客户端发送OP_MSG消息
    c_packet = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_msg_request)
    packets.append(c_packet)
    seq_num += len(op_msg_request)
    
    # 服务器响应OP_MSG，按照示例规格
    # MsgHeader参数
    reply_request_id = 12  # 服务器给本次响应分配的新ID
    reply_response_to = request_id  # 对应客户端的requestID
    reply_op_code = 2013  # OP_MSG（响应也使用同类型）
    
    # OP_MSG响应消息体参数
    reply_flag_bits = 0  # 0表示无错误
    
    # 构造firstBatch数组中的文档
    # Document 1: { "item": "canvas" }
    batch_doc1 = (
        b'\x16\x00\x00\x00'  # BSON size (22 bytes)
        b'\x02item\x00\x07\x00\x00\x00canvas\x00'  # String field "item": "canvas"
        b'\x00'  # BSON end-of-object marker
    )
    
    # Document 2: { "item": "brush" }
    batch_doc2 = (
        b'\x15\x00\x00\x00'  # BSON size (21 bytes)
        b'\x02item\x00\x06\x00\x00\x00brush\x00'  # String field "item": "brush"
        b'\x00'  # BSON end-of-object marker
    )
    
    # 构造firstBatch数组: [{ "item": "canvas" }, { "item": "brush" }]
    # 数组大小: 4(大小) + 1(类型) + 2(索引"0"+null) + 22(doc1) + 1(类型) + 2(索引"1"+null) + 21(doc2) + 1(结束) = 54字节
    first_batch_array = (
        b'\x36\x00\x00\x00'  # Array BSON size (54 bytes)
        b'\x03\x30\x00' + batch_doc1 +  # "0": document1
        b'\x03\x31\x00' + batch_doc2 +  # "1": document2
        b'\x00'  # Array end-of-object marker
    )
    
    # 构造cursor内嵌文档: {
    #     "id": 0,
    #     "ns": "test.inventory", 
    #     "firstBatch": [...]
    # }
    # 计算cursor文档大小:
    # - BSON大小: 4字节
    # - "id": 0 字段: 1(类型) + 3(字段名+null) + 8(int64值) = 12字节
    # - "ns": "test.inventory" 字段: 1(类型) + 3(字段名+null) + 4(字符串长度) + 15(字符串值+null) = 23字节
    # - "firstBatch": [...] 字段: 1(类型) + 11(字段名+null) + 54(数组) = 66字节
    # - 结束标记: 1字节
    # 总计: 4 + 12 + 23 + 66 + 1 = 106字节
    cursor_doc = (
        b'\x6a\x00\x00\x00'  # BSON size (106 bytes)
        b'\x12id\x00\x00\x00\x00\x00\x00\x00\x00\x00'  # Int64 field "id": 0
        b'\x02ns\x00\x0f\x00\x00\x00test.inventory\x00'  # String field "ns": "test.inventory"
        b'\x04firstBatch\x00' + first_batch_array +  # Array field "firstBatch"
        b'\x00'  # BSON end-of-object marker
    )
    
    # 构造主回复文档: {
    #     "cursor": {...},
    #     "ok": 1
    # }
    # 计算回复文档大小:
    # - BSON大小: 4字节
    # - "cursor": {...} 字段: 1(类型) + 7(字段名+null) + 106(内嵌文档) = 114字节
    # - "ok": 1 字段: 1(类型) + 3(字段名+null) + 4(int32值) = 8字节
    # - 结束标记: 1字节
    # 总计: 4 + 114 + 8 + 1 = 127字节
    reply_document = (
        b'\x7f\x00\x00\x00'  # BSON size (127 bytes)
        b'\x03cursor\x00' + cursor_doc +  # Document field "cursor"
        b'\x10ok\x00\x01\x00\x00\x00'  # Int32 field "ok": 1
        b'\x00'  # BSON end-of-object marker
    )
    
    # 组装OP_MSG响应消息体
    reply_section_kind = 0  # Kind 0表示Body section
    reply_msg_body = (
        struct.pack("<I", reply_flag_bits) +  # flagBits (uint32)
        struct.pack("<B", reply_section_kind) +  # Section Kind (1 byte)
        reply_document  # Section payload (BSON document)
    )
    
    # 计算回复消息总长度
    reply_msg_length = 16 + len(reply_msg_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_MSG响应消息
    op_msg_reply = (
        struct.pack("<iiii", reply_msg_length, reply_request_id, reply_response_to, reply_op_code) +  # MsgHeader
        reply_msg_body  # OP_MSG消息体
    )
    
    # 服务器发送OP_MSG响应消息
    s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_msg_reply)
    packets.append(s_packet)
    ack_num += len(op_msg_reply)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_op_msg.pcap")

def generate_op_msg_with_document_sequence_scenario():
    """生成OP_MSG with document_sequence消息场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造OP_MSG with document_sequence消息，按照示例规格
    # MsgHeader参数
    request_id = 15  # 客户端请求号
    response_to = 0  # 请求时固定为0
    op_code = 2013  # OP_MSG
    
    # OP_MSG消息体参数
    flag_bits = 0  # 标志位，0表示默认
    
    # Section 0: Body (Kind 0)
    # BSON command document: {
    #     "insert": "inventory",
    #     "$db": "test",
    #     "ordered": true
    # }
    
    # 计算命令文档的大小
    # - BSON大小: 4字节
    # - "insert": "inventory" 字段: 1(类型) + 7(字段名+null) + 4(字符串长度) + 10(字符串值+null) = 22字节
    # - "$db": "test" 字段: 1(类型) + 4(字段名+null) + 4(字符串长度) + 5(字符串值+null) = 14字节
    # - "ordered": true 字段: 1(类型) + 8(字段名+null) + 1(boolean值) = 10字节
    # - 结束标记: 1字节
    # 总计: 4 + 22 + 14 + 10 + 1 = 51字节
    
    command_document = (
        b'\x33\x00\x00\x00'  # BSON size (51 bytes)
        b'\x02insert\x00\x0a\x00\x00\x00inventory\x00'  # String field "insert": "inventory"
        b'\x02$db\x00\x05\x00\x00\x00test\x00'  # String field "$db": "test"
        b'\x08ordered\x00\x01'  # Boolean field "ordered": true
        b'\x00'  # BSON end-of-object marker
    )
    
    # Section 1: document_sequence (Kind 1)
    # 构造要插入的文档
    
    # Document 1: { "item": "canvas", "qty": 100, "tags": ["art","supplies"] }
    # 构造tags数组: ["art", "supplies"]
    tags_array1 = (
        b'\x20\x00\x00\x00'  # 数组BSON大小 (32 bytes)
        b'\x02\x30\x00\x04\x00\x00\x00art\x00'  # "0": "art" (11 bytes: 1+2+4+4)
        b'\x02\x31\x00\x09\x00\x00\x00supplies\x00'  # "1": "supplies" (16 bytes: 1+2+4+9)
        b'\x00'  # 数组结束标记
    )
    
    document1 = (
        b'\x45\x00\x00\x00'  # BSON size (69 bytes)
        b'\x02item\x00\x07\x00\x00\x00canvas\x00'  # String field "item": "canvas" (17 bytes)
        b'\x10qty\x00\x64\x00\x00\x00'  # Int32 field "qty": 100 (9 bytes)
        b'\x04tags\x00' + tags_array1 +  # Array field "tags" (6 bytes prefix + 32 bytes array = 38 bytes)
        b'\x00'  # BSON end-of-object marker
    )
    
    # Document 2: { "item": "brush", "qty": 200, "tags": ["art","tools"] }
    # 构造tags数组: ["art", "tools"]
    tags_array2 = (
        b'\x1d\x00\x00\x00'  # 数组BSON大小 (29 bytes)
        b'\x02\x30\x00\x04\x00\x00\x00art\x00'  # "0": "art" (11 bytes: 1+2+4+4)
        b'\x02\x31\x00\x06\x00\x00\x00tools\x00'  # "1": "tools" (13 bytes: 1+2+4+6)
        b'\x00'  # 数组结束标记
    )
    
    document2 = (
        b'\x41\x00\x00\x00'  # BSON size (65 bytes)
        b'\x02item\x00\x06\x00\x00\x00brush\x00'  # String field "item": "brush" (16 bytes)
        b'\x10qty\x00\xc8\x00\x00\x00'  # Int32 field "qty": 200 (9 bytes)
        b'\x04tags\x00' + tags_array2 +  # Array field "tags" (6 bytes prefix + 29 bytes array = 35 bytes)
        b'\x00'  # BSON end-of-object marker
    )
    
    # 构造document_sequence section
    identifier = b"documents\x00"  # cstring identifier
    sequence_documents = document1 + document2
    # 根据MongoDB官方文档，size字段应包含其自身(4字节), identifier和documents的总大小
    size_of_sequence = 4 + len(identifier) + len(sequence_documents)
    
    document_sequence_section = (
        struct.pack("<i", size_of_sequence) +  # int32 sizeOfSequence (必须在最前面)
        identifier +  # cstring identifier
        sequence_documents  # BSON documents
    )
    
    # 组装OP_MSG消息体
    section0_kind = 0  # Kind 0表示Body section
    section1_kind = 1  # Kind 1表示document_sequence section
    
    msg_body = (
        struct.pack("<I", flag_bits) +  # flagBits (uint32)
        struct.pack("<B", section0_kind) +  # Section 0 Kind (1 byte)
        command_document +  # Section 0 payload (BSON document)
        struct.pack("<B", section1_kind) +  # Section 1 Kind (1 byte)
        document_sequence_section  # Section 1 payload (document sequence)
    )
    
    # 计算消息总长度（MsgHeader + 消息体）
    msg_length = 16 + len(msg_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_MSG消息
    op_msg_request = (
        struct.pack("<iiii", msg_length, request_id, response_to, op_code) +  # MsgHeader
        msg_body  # OP_MSG消息体
    )
    
    # 客户端发送OP_MSG消息
    c_packet = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_msg_request)
    packets.append(c_packet)
    seq_num += len(op_msg_request)
    
    # 服务器响应OP_MSG，按照示例规格
    # MsgHeader参数
    reply_request_id = 16  # 服务器给本次响应分配的新ID
    reply_response_to = request_id  # 对应客户端的requestID
    reply_op_code = 2013  # OP_MSG（响应也使用同类型）
    
    # OP_MSG响应消息体参数
    reply_flag_bits = 0  # 0表示无错误
    
    # Section 0: Body (Kind 0)
    # BSON reply document: {
    #     "ok": 1,
    #     "n": 2
    # }
    # 计算回复文档大小:
    # - BSON大小: 4字节
    # - "ok": 1 字段: 1(类型) + 3(字段名+null) + 4(int32值) = 8字节
    # - "n": 2 字段: 1(类型) + 2(字段名+null) + 4(int32值) = 7字节
    # - 结束标记: 1字节
    # 总计: 4 + 8 + 7 + 1 = 20字节
    
    reply_document = (
        b'\x14\x00\x00\x00'  # BSON size (20 bytes)
        b'\x10ok\x00\x01\x00\x00\x00'  # Int32 field "ok": 1
        b'\x10n\x00\x02\x00\x00\x00'  # Int32 field "n": 2
        b'\x00'  # BSON end-of-object marker
    )
    
    # 组装OP_MSG响应消息体（只有Section 0）
    reply_section_kind = 0  # Kind 0表示Body section
    reply_msg_body = (
        struct.pack("<I", reply_flag_bits) +  # flagBits (uint32)
        struct.pack("<B", reply_section_kind) +  # Section Kind (1 byte)
        reply_document  # Section payload (BSON document)
    )
    
    # 计算回复消息总长度
    reply_msg_length = 16 + len(reply_msg_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_MSG响应消息
    op_msg_reply = (
        struct.pack("<iiii", reply_msg_length, reply_request_id, reply_response_to, reply_op_code) +  # MsgHeader
        reply_msg_body  # OP_MSG消息体
    )
    
    # 服务器发送OP_MSG响应消息
    s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_msg_reply)
    packets.append(s_packet)
    ack_num += len(op_msg_reply)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_op_msg_document_sequence.pcap")

def main():
    """主函数，生成所有场景的PCAP文件"""
    print("开始生成MongoDB协议PCAP文件...")
    
    # 生成各种场景的PCAP
    generate_op_compressed_scenario()  # 生成OP_COMPRESSED消息场景
    generate_op_compressed_with_example_scenario()  # 生成与示例完全匹配的OP_COMPRESSED消息场景
    generate_op_query_scenario()  # 生成OP_QUERY消息场景
    generate_op_insert_scenario()  # 生成OP_INSERT消息场景
    generate_op_update_scenario()  # 生成OP_UPDATE消息场景
    generate_op_delete_scenario()  # 生成OP_DELETE消息场景
    generate_op_msg_scenario()  # 生成OP_MSG消息场景
    generate_op_msg_with_document_sequence_scenario()  # 生成OP_MSG with document_sequence消息场景
    
    print(f"所有PCAP文件已生成到 {output_dir} 目录")

if __name__ == "__main__":
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    main()