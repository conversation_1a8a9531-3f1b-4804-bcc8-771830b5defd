#!/usr/bin/env python3
from scapy.all import *
from scapy.layers.inet import IP, TCP
import struct
import random
import os
import zlib
from bson import BSON
from bson.objectid import ObjectId
from collections import OrderedDict

# MongoDB 默认端口
MONGODB_PORT = 27017

# 客户端和服务器IP地址
CLIENT_IP = "************"
SERVER_IP = "************"

# 随机端口和序列号
client_port = random.randint(40000, 65000)
seq_num = random.randint(1000000, 9000000)
ack_num = random.randint(1000000, 9000000)

# 输出目录
output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "mongodb_pcaps")

# TCP分段相关常量
MTU = 1500  # 最大传输单元
TCP_HEADER_SIZE = 20  # TCP头部大小
IP_HEADER_SIZE = 20   # IP头部大小
ETH_HEADER_SIZE = 14  # 以太网头部大小
MAX_PAYLOAD_SIZE = MTU - ETH_HEADER_SIZE - IP_HEADER_SIZE - TCP_HEADER_SIZE  # 1446字节

def create_tcp_segments(src, dst, sport, dport, seq_start, ack, payload, flags="PA"):
    """将大型payload分割成多个TCP段"""
    segments = []
    total_length = len(payload)
    
    if total_length <= MAX_PAYLOAD_SIZE:
        # 不需要分段
        segment = create_mongodb_packet(src, dst, sport, dport, seq_start, ack, payload, flags)
        return [segment], seq_start + total_length
    
    # 需要分段
    offset = 0
    seq_num = seq_start
    
    while offset < total_length:
        # 计算当前段的大小
        remaining = total_length - offset
        segment_size = min(MAX_PAYLOAD_SIZE, remaining)
        
        # 提取当前段的数据
        segment_payload = payload[offset:offset + segment_size]
        
        # 确定TCP标志
        if offset == 0:
            # 第一个段，保持原始标志或使用PSH
            segment_flags = "P" if remaining > MAX_PAYLOAD_SIZE else flags
        elif offset + segment_size >= total_length:
            # 最后一个段，使用原始标志
            segment_flags = flags
        else:
            # 中间段，只使用ACK
            segment_flags = "A"
        
        # 创建TCP段
        segment = create_mongodb_packet(src, dst, sport, dport, seq_num, ack, segment_payload, segment_flags)
        segments.append(segment)
        
        # 更新偏移和序列号
        offset += segment_size
        seq_num += segment_size
    
    return segments, seq_num

def create_mongodb_packet(src, dst, sport, dport, seq, ack, payload, flags="PA"):
    """创建一个MongoDB数据包"""
    eth = Ether(type=0x0800)  # IPv4类型
    ip = IP(src=src, dst=dst)
    tcp = TCP(sport=sport, dport=dport, seq=seq, ack=ack, flags=flags)
    return eth/ip/tcp/payload

def create_tcp_handshake(src, dst, sport, dport):
    """创建TCP三次握手数据包"""
    packets = []
    # 初始序列号
    client_isn = random.randint(1000000, 9000000)
    server_isn = random.randint(1000000, 9000000)
    
    # SYN
    syn = create_mongodb_packet(src, dst, sport, dport, client_isn, 0, b"", flags="S")
    packets.append(syn)
    
    # SYN-ACK
    syn_ack = create_mongodb_packet(dst, src, dport, sport, server_isn, client_isn + 1, b"", flags="SA")
    packets.append(syn_ack)
    
    # ACK
    ack = create_mongodb_packet(src, dst, sport, dport, client_isn + 1, server_isn + 1, b"", flags="A")
    packets.append(ack)
    
    return packets, client_isn + 1, server_isn + 1

def create_tcp_teardown(src, dst, sport, dport, seq, ack):
    """创建TCP四次挥手数据包"""
    packets = []
    
    # FIN-ACK (client)
    fin_ack1 = create_mongodb_packet(src, dst, sport, dport, seq, ack, b"", flags="FA")
    packets.append(fin_ack1)
    
    # ACK (server)
    ack1 = create_mongodb_packet(dst, src, dport, sport, ack, seq + 1, b"", flags="A")
    packets.append(ack1)
    
    # FIN-ACK (server)
    fin_ack2 = create_mongodb_packet(dst, src, dport, sport, ack, seq + 1, b"", flags="FA")
    packets.append(fin_ack2)
    
    # ACK (client)
    ack2 = create_mongodb_packet(src, dst, sport, dport, seq + 1, ack + 1, b"", flags="A")
    packets.append(ack2)
    
    return packets

def write_pcap(packets, filename):
    """将数据包写入PCAP文件"""
    wrpcap(os.path.join(output_dir, filename), packets)
    print(f"已生成 {filename}")

def generate_op_compressed_zlib_scenario():
    """生成OP_COMPRESSED (zlib)消息场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造原始OP_QUERY消息体（不含MsgHeader），参考 refer.md
    op_query_flags = 0
    op_query_full_collection_name = b"test.inventory\x00"
    op_query_number_to_skip = 0
    op_query_number_to_return = 2
    # BSON query: {"status": "A"}
    op_query_bson_query = BSON.encode({"status": "A"})
    # BSON returnFieldsSelector: {"item": 1, "_id": 0}
    op_query_bson_return_fields_selector = BSON.encode(OrderedDict([("item", 1), ("_id", 0)]))

    original_query_body = (
        struct.pack("<i", op_query_flags) +
        op_query_full_collection_name +
        struct.pack("<i", op_query_number_to_skip) +
        struct.pack("<i", op_query_number_to_return) +
        op_query_bson_query +
        op_query_bson_return_fields_selector
    )

    # 计算原始消息的总长度（包括MsgHeader）
    original_msg_length = 16 + len(original_query_body)  # 16字节的MsgHeader + 消息体

    # 压缩原始消息体
    compressed_body = zlib.compress(original_query_body)

    # 构造OP_COMPRESSED消息
    # MsgHeader (16字节)
    # OP_COMPRESSED header: originalOpcode (4) + uncompressedSize (4) + compressorId (1) = 9 bytes
    # Total msg_length = 16 (MsgHeader) + 9 (OP_COMPRESSED specific header) + len(compressed_body)
    msg_length = 16 + 4 + 4 + 1 + len(compressed_body)
    request_id = 1 # 对应 refer.md 示例
    response_to = 0
    op_code = 2012  # OP_COMPRESSED

    # OP_COMPRESSED消息体
    original_opcode = 2004  # OP_QUERY
    uncompressed_size = len(original_query_body)
    compressor_id = 2  # 0: noop, 1: snappy, 2: zlib, 3: zstd

    # 组装完整的OP_COMPRESSED消息
    op_compressed_msg = (
        struct.pack("<iiii", msg_length, request_id, response_to, op_code) +  # MsgHeader
        struct.pack("<i", original_opcode) +  # 原始消息类型
        struct.pack("<i", uncompressed_size) +  # 解压前的消息长度
        struct.pack("<B", compressor_id) +  # 压缩算法标识 (uint8_t)
        compressed_body  # 压缩后的数据
    )

    # 客户端发送OP_COMPRESSED消息
    c_packet = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_compressed_msg)
    packets.append(c_packet)
    seq_num += len(op_compressed_msg)

    # 服务器响应 OP_REPLY，参考 refer.md
    reply_response_flags = 0
    reply_cursor_id = 0
    reply_starting_from = 0
    reply_number_returned = 2
    # BSON documents (示例)
    # 第一个文档: {"_id": ObjectId("0102030405060708090a0b0c"), "status": "A", "item": "canvas"}
    doc1 = BSON.encode(OrderedDict([
        ("_id", ObjectId(b'\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c')),
        ("status", "A"),
        ("item", "canvas")
    ]))
    # 第二个文档: {"_id": ObjectId("0d0e0f101112131415161718"), "status": "A", "item": "brush"}
    doc2 = BSON.encode(OrderedDict([
        ("_id", ObjectId(b'\x0d\x0e\x0f\x10\x11\x12\x13\x14\x15\x16\x17\x18')),
        ("status", "A"),
        ("item", "brush")
    ]))
    reply_documents = doc1 + doc2

    reply_body_len = 4 + 8 + 4 + 4 + len(reply_documents) # responseFlags, cursorID, startingFrom, numberReturned, documents
    reply_msg_len = 16 + reply_body_len # MsgHeader + body
    reply_request_id = 2 # 服务器生成的新ID
    reply_response_to = request_id # 对应客户端的requestID
    reply_op_code = 1 # OP_REPLY

    reply_header = struct.pack("<iiii", reply_msg_len, reply_request_id, reply_response_to, reply_op_code)
    reply_body_packed = (
        struct.pack("<i", reply_response_flags) +
        struct.pack("<q", reply_cursor_id) +
        struct.pack("<i", reply_starting_from) +
        struct.pack("<i", reply_number_returned) +
        reply_documents
    )
    reply = reply_header + reply_body_packed
    
    s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, reply)
    packets.append(s_packet)
    ack_num += len(reply)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_op_compressed_zlib.pcap")

def generate_op_query_scenario():
    """生成OP_QUERY消息场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造OP_QUERY消息，按照示例规格
    # MsgHeader参数
    request_id = 1  # 客户端自增请求号
    response_to = 0  # 请求时为0
    op_code = 2004  # OP_QUERY
    
    # OP_QUERY消息体参数
    flags = 0  # 标志位，0表示默认
    full_collection_name = b"test.inventory\x00"  # 集合名称，以null结尾
    number_to_skip = 0  # 跳过文档数
    number_to_return = 2  # 本次希望返回的文档数
    
    # BSON query: {"status": "A"}
    # BSON结构: size(4) + field_type(1) + field_name + null(1) + value + null(1) + eoo(1)
    bson_query = BSON.encode({'status': 'A'})
    
    # BSON returnFieldsSelector: {"item": 1, "_id": 0}
    bson_return_fields_selector = BSON.encode(OrderedDict([("item", 1), ("_id", 0)]))
    
    # 组装OP_QUERY消息体
    query_body = (
        struct.pack("<i", flags) +  # flags
        full_collection_name +  # fullCollectionName
        struct.pack("<i", number_to_skip) +  # numberToSkip
        struct.pack("<i", number_to_return) +  # numberToReturn
        bson_query +  # query BSON
        bson_return_fields_selector  # returnFieldsSelector BSON
    )
    
    # 计算消息总长度（MsgHeader + 消息体）
    msg_length = 16 + len(query_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_QUERY消息
    op_query_msg = (
        struct.pack("<iiii", msg_length, request_id, response_to, op_code) +  # MsgHeader
        query_body  # OP_QUERY消息体
    )
    
    # 客户端发送OP_QUERY消息
    c_packet = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_query_msg)
    packets.append(c_packet)
    seq_num += len(op_query_msg)
    
    # 服务器响应OP_REPLY，按照示例规格
    # MsgHeader参数
    reply_request_id = 2  # 服务器给本次响应分配的新ID
    reply_response_to = request_id  # 对应客户端的requestID
    reply_op_code = 1  # OP_REPLY
    
    # OP_REPLY消息体参数
    response_flags = 0  # 响应标志，0表示正常
    cursor_id = 0  # 服务器游标ID（0表示已关闭）
    starting_from = 0  # 本批文档中第一条在整个结果集中的偏移
    number_returned = 2  # 实际返回文档数
    
    # BSON documents
    # 第一个文档: {"_id": ObjectId("5f1e7d9b1e9f1a2b3c4d5e6f"), "status": "A", "item": "canvas"}
    doc1 = BSON.encode(OrderedDict([
        ("_id", ObjectId(b'\x5f\x1e\x7d\x9b\x1e\x9f\x1a\x2b\x3c\x4d\x5e\x6f')),
        ("status", "A"),
        ("item", "canvas")
    ]))
    
    # 第二个文档: {"_id": ObjectId("5f1e7d9b1e9f1a2b3c4d5e70"), "status": "A", "item": "brush"}
    doc2 = BSON.encode(OrderedDict([
        ("_id", ObjectId(b'\x5f\x1e\x7d\x9b\x1e\x9f\x1a\x2b\x3c\x4d\x5e\x70')),
        ("status", "A"),
        ("item", "brush")
    ]))
    
    reply_documents = doc1 + doc2
    
    # 组装OP_REPLY消息体
    reply_body = (
        struct.pack("<i", response_flags) +  # responseFlags
        struct.pack("<q", cursor_id) +  # cursorID (8字节长整型)
        struct.pack("<i", starting_from) +  # startingFrom
        struct.pack("<i", number_returned) +  # numberReturned
        reply_documents  # documents
    )
    
    # 计算回复消息总长度
    reply_msg_length = 16 + len(reply_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_REPLY消息
    op_reply_msg = (
        struct.pack("<iiii", reply_msg_length, reply_request_id, reply_response_to, reply_op_code) +  # MsgHeader
        reply_body  # OP_REPLY消息体
    )
    
    # 服务器发送OP_REPLY消息
    s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_reply_msg)
    packets.append(s_packet)
    ack_num += len(op_reply_msg)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_op_query.pcap")

def generate_op_insert_scenario():
    """生成OP_INSERT消息场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造OP_INSERT消息，按照示例规格
    # MsgHeader参数
    request_id = 5  # 客户端请求号
    response_to = 0  # 请求时固定为0
    op_code = 2002  # OP_INSERT
    
    # OP_INSERT消息体参数
    flags = 0  # 标志位，0表示默认
    full_collection_name = b"test.inventory\x00"  # 集合名称，以null结尾
    
    # BSON document1: {"item": "journal", "qty": 25, "tags": ["blank", "red"]}
    document1 = BSON.encode(OrderedDict([
        ("item", "journal"),
        ("qty", 25),
        ("tags", ["blank", "red"])
    ]))
    
    # BSON document2: {"item": "notebook", "qty": 50, "tags": ["red", "blank"]}
    document2 = BSON.encode(OrderedDict([
        ("item", "notebook"),
        ("qty", 50),
        ("tags", ["red", "blank"])
    ]))
    
    # 组装OP_INSERT消息体
    insert_body = (
        struct.pack("<i", flags) +  # flags
        full_collection_name +  # fullCollectionName
        document1 +  # document1
        document2   # document2
    )
    
    # 计算消息总长度（MsgHeader + 消息体）
    msg_length = 16 + len(insert_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_INSERT消息
    op_insert_msg = (
        struct.pack("<iiii", msg_length, request_id, response_to, op_code) +  # MsgHeader
        insert_body  # OP_INSERT消息体
    )
    
    # 客户端发送OP_INSERT消息
    c_packet = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_insert_msg)
    packets.append(c_packet)
    seq_num += len(op_insert_msg)
    
    # 服务器响应OP_REPLY，按照示例规格
    # MsgHeader参数
    reply_request_id = 6  # 服务器给本次响应分配的新ID
    reply_response_to = request_id  # 对应客户端的requestID
    reply_op_code = 1  # OP_REPLY
    
    # OP_REPLY消息体参数
    response_flags = 0  # 响应标志，0表示正常
    cursor_id = 0  # 写操作不使用游标
    starting_from = 0  # 起始位置
    number_returned = 1  # 返回文档数（写操作统一封装成一个文档）
    
    # BSON response document: {"ok": 1, "n": 2}
    response_document = BSON.encode(OrderedDict([("ok", 1), ("n", 2)]))
    
    # 组装OP_REPLY消息体
    reply_body = (
        struct.pack("<i", response_flags) +  # responseFlags
        struct.pack("<q", cursor_id) +  # cursorID (8字节长整型)
        struct.pack("<i", starting_from) +  # startingFrom
        struct.pack("<i", number_returned) +  # numberReturned
        response_document  # response document
    )
    
    # 计算回复消息总长度
    reply_msg_length = 16 + len(reply_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_REPLY消息
    op_reply_msg = (
        struct.pack("<iiii", reply_msg_length, reply_request_id, reply_response_to, reply_op_code) +  # MsgHeader
        reply_body  # OP_REPLY消息体
    )
    
    # 服务器发送OP_REPLY消息
    s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_reply_msg)
    packets.append(s_packet)
    ack_num += len(op_reply_msg)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_op_insert.pcap")

def generate_op_update_scenario():
    """生成OP_UPDATE消息场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造OP_UPDATE消息，按照示例规格
    # MsgHeader参数
    request_id = 7  # 客户端请求号
    response_to = 0  # 请求时固定为0
    op_code = 2001  # OP_UPDATE
    
    # OP_UPDATE消息体参数
    zero = 0  # 保留字段，必须为0
    full_collection_name = b"test.inventory\x00"  # 集合名称，以null结尾
    flags = 0  # 0表示默认（只更新第一条匹配）
    
    # BSON selector: {"item": "journal"}
    selector = BSON.encode({"item": "journal"})
    
    # BSON update: {"$inc": {"qty": 10}}
    update = BSON.encode({"$inc": {"qty": 10}})
    
    # 组装OP_UPDATE消息体
    update_body = (
        struct.pack("<i", zero) +  # ZERO
        full_collection_name +  # fullCollectionName
        struct.pack("<i", flags) +  # flags
        selector +  # selector BSON
        update   # update BSON
    )
    
    # 计算消息总长度（MsgHeader + 消息体）
    msg_length = 16 + len(update_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_UPDATE消息
    op_update_msg = (
        struct.pack("<iiii", msg_length, request_id, response_to, op_code) +  # MsgHeader
        update_body  # OP_UPDATE消息体
    )
    
    # 客户端发送OP_UPDATE消息
    c_packet = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_update_msg)
    packets.append(c_packet)
    seq_num += len(op_update_msg)
    
    # 服务器响应OP_REPLY，按照示例规格
    # MsgHeader参数
    reply_request_id = 8  # 服务器给本次响应分配的新ID
    reply_response_to = request_id  # 对应客户端的requestID
    reply_op_code = 1  # OP_REPLY
    
    # OP_REPLY消息体参数
    response_flags = 0  # 响应标志，0表示正常
    cursor_id = 0  # 更新操作不使用游标
    starting_from = 0  # 起始位置
    number_returned = 1  # 返回文档数（更新操作统一封装成一个文档）
    
    # BSON response document: {"ok": 1, "n": 1, "nModified": 1}
    response_document = BSON.encode(OrderedDict([
        ("ok", 1),
        ("n", 1),
        ("nModified", 1)
    ]))
    
    # 组装OP_REPLY消息体
    reply_body = (
        struct.pack("<i", response_flags) +  # responseFlags
        struct.pack("<q", cursor_id) +  # cursorID (8字节长整型)
        struct.pack("<i", starting_from) +  # startingFrom
        struct.pack("<i", number_returned) +  # numberReturned
        response_document  # response document
    )
    
    # 计算回复消息总长度
    reply_msg_length = 16 + len(reply_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_REPLY消息
    op_reply_msg = (
        struct.pack("<iiii", reply_msg_length, reply_request_id, reply_response_to, reply_op_code) +  # MsgHeader
        reply_body  # OP_REPLY消息体
    )
    
    # 服务器发送OP_REPLY消息
    s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_reply_msg)
    packets.append(s_packet)
    ack_num += len(op_reply_msg)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_op_update.pcap")

def generate_op_delete_scenario():
    """生成OP_DELETE消息场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造OP_DELETE消息，按照示例规格
    # MsgHeader参数
    request_id = 9  # 客户端请求号
    response_to = 0  # 请求时固定为0
    op_code = 2006  # OP_DELETE
    
    # OP_DELETE消息体参数
    zero = 0  # 保留字段，必须为0
    full_collection_name = b"test.inventory\x00"  # 集合名称，以null结尾
    flags = 0  # 0：只删除第一条匹配；1：删除所有匹配
    
    # BSON selector: {"status": "D"}
    selector = BSON.encode({"status": "D"})
    
    # 组装OP_DELETE消息体
    delete_body = (
        struct.pack("<i", zero) +  # ZERO
        full_collection_name +  # fullCollectionName
        struct.pack("<i", flags) +  # flags
        selector   # selector BSON
    )
    
    # 计算消息总长度（MsgHeader + 消息体）
    msg_length = 16 + len(delete_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_DELETE消息
    op_delete_msg = (
        struct.pack("<iiii", msg_length, request_id, response_to, op_code) +  # MsgHeader
        delete_body  # OP_DELETE消息体
    )
    
    # 客户端发送OP_DELETE消息
    c_packet = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_delete_msg)
    packets.append(c_packet)
    seq_num += len(op_delete_msg)
    
    # 服务器响应OP_REPLY，按照示例规格
    # MsgHeader参数
    reply_request_id = 10  # 服务器给本次响应分配的新ID
    reply_response_to = request_id  # 对应客户端的requestID
    reply_op_code = 1  # OP_REPLY
    
    # OP_REPLY消息体参数
    response_flags = 0  # 响应标志，0表示正常
    cursor_id = 0  # 删除操作不使用游标
    starting_from = 0  # 起始位置
    number_returned = 1  # 返回文档数（删除操作统一封装成一个文档）
    
    # BSON response document: {"ok": 1, "n": 1}
    response_document = BSON.encode(OrderedDict([("ok", 1), ("n", 1)]))
    
    # 组装OP_REPLY消息体
    reply_body = (
        struct.pack("<i", response_flags) +  # responseFlags
        struct.pack("<q", cursor_id) +  # cursorID (8字节长整型)
        struct.pack("<i", starting_from) +  # startingFrom
        struct.pack("<i", number_returned) +  # numberReturned
        response_document  # response document
    )
    
    # 计算回复消息总长度
    reply_msg_length = 16 + len(reply_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_REPLY消息
    op_reply_msg = (
        struct.pack("<iiii", reply_msg_length, reply_request_id, reply_response_to, reply_op_code) +  # MsgHeader
        reply_body  # OP_REPLY消息体
    )
    
    # 服务器发送OP_REPLY消息
    s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_reply_msg)
    packets.append(s_packet)
    ack_num += len(op_reply_msg)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_op_delete.pcap")

def generate_op_msg_scenario():
    """生成OP_MSG消息场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造OP_MSG消息，按照示例规格
    # MsgHeader参数
    request_id = 11  # 客户端请求号
    response_to = 0  # 请求时固定为0
    op_code = 2013  # OP_MSG
    
    # OP_MSG消息体参数
    flag_bits = 0  # 标志位，0表示默认
    
    # Section 0: Body (Kind 0)
    # BSON command document: {
    #     "find": "inventory",
    #     "filter": { "status": "A" },
    #     "projection": { "item": 1, "_id": 0 },
    #     "batchSize": 2,
    #     "$db": "test"
    # }
    
    command_document = BSON.encode(OrderedDict([
        ("find", "inventory"),
        ("filter", {"status": "A"}),
        ("projection", OrderedDict([("item", 1), ("_id", 0)])),
        ("batchSize", 2),
        ("$db", "test")
    ]))
    
    # 组装OP_MSG消息体
    # Section 0: Kind 0 (Body)
    section_kind = 0  # Kind 0表示Body section
    msg_body = (
        struct.pack("<I", flag_bits) +  # flagBits (uint32)
        struct.pack("<B", section_kind) +  # Section Kind (1 byte)
        command_document  # Section payload (BSON document)
    )
    
    # 计算消息总长度（MsgHeader + 消息体）
    msg_length = 16 + len(msg_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_MSG消息
    op_msg_request = (
        struct.pack("<iiii", msg_length, request_id, response_to, op_code) +  # MsgHeader
        msg_body  # OP_MSG消息体
    )
    
    # 客户端发送OP_MSG消息
    c_packet = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_msg_request)
    packets.append(c_packet)
    seq_num += len(op_msg_request)
    
    # 服务器响应OP_MSG，按照示例规格
    # MsgHeader参数
    reply_request_id = 12  # 服务器给本次响应分配的新ID
    reply_response_to = request_id  # 对应客户端的requestID
    reply_op_code = 2013  # OP_MSG（响应也使用同类型）
    
    # OP_MSG响应消息体参数
    reply_flag_bits = 0  # 0表示无错误
    
    # 构造firstBatch数组中的文档
    # Document 1: { "item": "canvas" }
    batch_doc1 = BSON.encode({"item": "canvas"})
    
    # Document 2: { "item": "brush" }
    batch_doc2 = BSON.encode({"item": "brush"})
    
    # 构造firstBatch数组: [{ "item": "canvas" }, { "item": "brush" }]
    first_batch_array = BSON.encode({
        "0": {"item": "canvas"},
        "1": {"item": "brush"}
    })
    
    # 构造cursor内嵌文档: {
    #     "id": 0,
    #     "ns": "test.inventory", 
    #     "firstBatch": [...]
    # }
    cursor_doc = BSON.encode(OrderedDict([
        ("id", 0),
        ("ns", "test.inventory"),
        ("firstBatch", [{"item": "canvas"}, {"item": "brush"}])
    ]))
    
    # 构造主回复文档: {
    #     "cursor": {...},
    #     "ok": 1
    # }
    reply_document = BSON.encode(OrderedDict([
        ("cursor", OrderedDict([
            ("id", 0),
            ("ns", "test.inventory"),
            ("firstBatch", [{"item": "canvas"}, {"item": "brush"}])
        ])),
        ("ok", 1)
    ]))
    
    # 组装OP_MSG响应消息体
    reply_section_kind = 0  # Kind 0表示Body section
    reply_msg_body = (
        struct.pack("<I", reply_flag_bits) +  # flagBits (uint32)
        struct.pack("<B", reply_section_kind) +  # Section Kind (1 byte)
        reply_document  # Section payload (BSON document)
    )
    
    # 计算回复消息总长度
    reply_msg_length = 16 + len(reply_msg_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_MSG响应消息
    op_msg_reply = (
        struct.pack("<iiii", reply_msg_length, reply_request_id, reply_response_to, reply_op_code) +  # MsgHeader
        reply_msg_body  # OP_MSG消息体
    )
    
    # 服务器发送OP_MSG响应消息
    s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_msg_reply)
    packets.append(s_packet)
    ack_num += len(op_msg_reply)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_op_msg.pcap")

def generate_op_msg_with_document_sequence_scenario():
    """生成OP_MSG with document_sequence消息场景的数据包"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造OP_MSG with document_sequence消息，按照示例规格
    # MsgHeader参数
    request_id = 15  # 客户端请求号
    response_to = 0  # 请求时固定为0
    op_code = 2013  # OP_MSG
    
    # OP_MSG消息体参数
    flag_bits = 0  # 标志位，0表示默认
    
    # Section 0: Body (Kind 0)
    # BSON command document: {
    #     "insert": "inventory",
    #     "$db": "test",
    #     "ordered": true
    # }
    
    command_document = BSON.encode(OrderedDict([
        ("insert", "inventory"),
        ("$db", "test"),
        ("ordered", True)
    ]))
    
    # Section 1: document_sequence (Kind 1)
    # 构造要插入的文档
    
    # Document 1: { "item": "canvas", "qty": 100, "tags": ["art","supplies"] }
    document1 = BSON.encode(OrderedDict([
        ("item", "canvas"),
        ("qty", 100),
        ("tags", ["art", "supplies"])
    ]))
    
    # Document 2: { "item": "brush", "qty": 200, "tags": ["art","tools"] }
    document2 = BSON.encode(OrderedDict([
        ("item", "brush"),
        ("qty", 200),
        ("tags", ["art", "tools"])
    ]))
    
    # 构造document_sequence section
    identifier = b"documents\x00"  # cstring identifier
    sequence_documents = document1 + document2
    # 根据MongoDB官方文档，size字段应包含其自身(4字节), identifier和documents的总大小
    size_of_sequence = 4 + len(identifier) + len(sequence_documents)
    
    document_sequence_section = (
        struct.pack("<i", size_of_sequence) +  # int32 sizeOfSequence (必须在最前面)
        identifier +  # cstring identifier
        sequence_documents  # BSON documents
    )
    
    # 组装OP_MSG消息体
    section0_kind = 0  # Kind 0表示Body section
    section1_kind = 1  # Kind 1表示document_sequence section
    
    msg_body = (
        struct.pack("<I", flag_bits) +  # flagBits (uint32)
        struct.pack("<B", section0_kind) +  # Section 0 Kind (1 byte)
        command_document +  # Section 0 payload (BSON document)
        struct.pack("<B", section1_kind) +  # Section 1 Kind (1 byte)
        document_sequence_section  # Section 1 payload (document sequence)
    )
    
    # 计算消息总长度（MsgHeader + 消息体）
    msg_length = 16 + len(msg_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_MSG消息
    op_msg_request = (
        struct.pack("<iiii", msg_length, request_id, response_to, op_code) +  # MsgHeader
        msg_body  # OP_MSG消息体
    )
    
    # 客户端发送OP_MSG消息
    c_packet = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_msg_request)
    packets.append(c_packet)
    seq_num += len(op_msg_request)
    
    # 服务器响应OP_MSG，按照示例规格
    # MsgHeader参数
    reply_request_id = 16  # 服务器给本次响应分配的新ID
    reply_response_to = request_id  # 对应客户端的requestID
    reply_op_code = 2013  # OP_MSG（响应也使用同类型）
    
    # OP_MSG响应消息体参数
    reply_flag_bits = 0  # 0表示无错误
    
    # Section 0: Body (Kind 0)
    # BSON reply document: {
    #     "ok": 1,
    #     "n": 2
    # }
    reply_document = BSON.encode(OrderedDict([("ok", 1), ("n", 2)]))
    
    # 组装OP_MSG响应消息体（只有Section 0）
    reply_section_kind = 0  # Kind 0表示Body section
    reply_msg_body = (
        struct.pack("<I", reply_flag_bits) +  # flagBits (uint32)
        struct.pack("<B", reply_section_kind) +  # Section Kind (1 byte)
        reply_document  # Section payload (BSON document)
    )
    
    # 计算回复消息总长度
    reply_msg_length = 16 + len(reply_msg_body)  # 16字节MsgHeader + 消息体长度
    
    # 组装完整的OP_MSG响应消息
    op_msg_reply = (
        struct.pack("<iiii", reply_msg_length, reply_request_id, reply_response_to, reply_op_code) +  # MsgHeader
        reply_msg_body  # OP_MSG消息体
    )
    
    # 服务器发送OP_MSG响应消息
    s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_msg_reply)
    packets.append(s_packet)
    ack_num += len(op_msg_reply)
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_op_msg_document_sequence.pcap")

def generate_op_get_more_scenario():
    """生成OP_GET_MORE消息场景的数据包"""
    packets = []
    global seq_num, ack_num

    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)

    # 构造OP_GET_MORE消息
    # MsgHeader参数
    request_id = 3
    response_to = 0
    op_code = 2005  # OP_GET_MORE

    # OP_GET_MORE消息体参数
    zero = 0
    full_collection_name = b"test.inventory\x00"
    number_to_return = 2
    cursor_id = 1234567890

    get_more_body = (
        struct.pack("<i", zero) +
        full_collection_name +
        struct.pack("<i", number_to_return) +
        struct.pack("<q", cursor_id)
    )

    msg_length = 16 + len(get_more_body)

    op_get_more_msg = (
        struct.pack("<iiii", msg_length, request_id, response_to, op_code) +
        get_more_body
    )

    # 客户端发送OP_GET_MORE消息
    c_packet = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_get_more_msg)
    packets.append(c_packet)
    seq_num += len(op_get_more_msg)

    # 服务器响应OP_REPLY
    # MsgHeader参数
    reply_request_id = 4
    reply_response_to = request_id
    reply_op_code = 1  # OP_REPLY

    # OP_REPLY消息体参数
    response_flags = 0
    reply_cursor_id = 1234567890
    starting_from = 2
    number_returned = 2

    # BSON documents
    doc1 = BSON.encode(OrderedDict([("_id", 1), ("item", "pen"), ("status", "A")]))
    doc2 = BSON.encode(OrderedDict([("_id", 2), ("item", "paper"), ("status", "A")]))
    reply_documents = doc1 + doc2

    reply_body = (
        struct.pack("<i", response_flags) +
        struct.pack("<q", reply_cursor_id) +
        struct.pack("<i", starting_from) +
        struct.pack("<i", number_returned) +
        reply_documents
    )

    reply_msg_length = 16 + len(reply_body)

    op_reply_msg = (
        struct.pack("<iiii", reply_msg_length, reply_request_id, reply_response_to, reply_op_code) +
        reply_body
    )

    # 服务器发送OP_REPLY消息
    s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_reply_msg)
    packets.append(s_packet)
    ack_num += len(op_reply_msg)

    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "mongodb_op_get_more.pcap")

def generate_paged_query_and_kill_cursors_scenario():
    """生成一个完整的分页查询，并最终使用OP_KILL_CURSORS关闭游标的场景"""
    packets = []
    global seq_num, ack_num

    # 1. TCP Handshake
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)

    # 2. 客户端发送 OP_QUERY，启动一个分页查询
    query_request_id = 1
    full_collection_name = b"test.paged_collection\x00"
    op_query_msg = (
        struct.pack("<iiii", 16 + 4 + len(full_collection_name) + 4 + 4 + len(BSON.encode({})), query_request_id, 0, 2004) +
        struct.pack("<i", 0) +
        full_collection_name +
        struct.pack("<i", 0) +
        struct.pack("<i", 2) +
        BSON.encode({})
    )
    c_packet_query = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_query_msg)
    packets.append(c_packet_query)
    seq_num += len(op_query_msg)

    # 3. 服务器响应第一批数据，并返回一个活动的游标ID
    reply1_request_id = 2
    cursor_id_to_use = 1234567890  # 这个游标将在后续被KILL
    doc1 = BSON.encode(OrderedDict([("_id", 1), ("data", "doc1")]))
    doc2 = BSON.encode(OrderedDict([("_id", 2), ("data", "doc2")]))
    reply1_documents = doc1 + doc2
    reply1_body = (
        struct.pack("<i", 0) +
        struct.pack("<q", cursor_id_to_use) +
        struct.pack("<i", 0) +
        struct.pack("<i", 2) +
        reply1_documents
    )
    op_reply1_msg = struct.pack("<iiii", 16 + len(reply1_body), reply1_request_id, query_request_id, 1) + reply1_body
    s_packet_reply1 = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_reply1_msg)
    packets.append(s_packet_reply1)
    ack_num += len(op_reply1_msg)

    # 4. 客户端发送 OP_GET_MORE 获取下一批数据
    get_more_request_id = 3
    get_more_body = (
        struct.pack("<i", 0) +
        full_collection_name +
        struct.pack("<i", 2) +
        struct.pack("<q", cursor_id_to_use)
    )
    op_get_more_msg = struct.pack("<iiii", 16 + len(get_more_body), get_more_request_id, 0, 2005) + get_more_body
    c_packet_get_more = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_get_more_msg)
    packets.append(c_packet_get_more)
    seq_num += len(op_get_more_msg)

    # 5. 服务器响应最后一批数据，并返回一个值为0的游标ID，表示游标已耗尽
    reply2_request_id = 4
    exhausted_cursor_id = 0
    doc3 = BSON.encode(OrderedDict([("_id", 3), ("data", "doc3")]))
    doc4 = BSON.encode(OrderedDict([("_id", 4), ("data", "doc4")]))
    reply2_documents = doc3 + doc4
    reply2_body = (
        struct.pack("<i", 0) +
        struct.pack("<q", exhausted_cursor_id) +
        struct.pack("<i", 2) +
        struct.pack("<i", 2) +
        reply2_documents
    )
    op_reply2_msg = struct.pack("<iiii", 16 + len(reply2_body), reply2_request_id, get_more_request_id, 1) + reply2_body
    s_packet_reply2 = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_reply2_msg)
    packets.append(s_packet_reply2)
    ack_num += len(op_reply2_msg)

    # 6. 客户端发送 OP_KILL_CURSORS 清理游标
    # 在查询结束后，客户端会发送此消息来释放服务器上的游标资源。
    kill_request_id = 5
    op_code_kill = 2007
    number_of_cursor_ids = 1  # 我们只关闭本次查询创建的一个游标
    cursor_ids_to_kill = [cursor_id_to_use]  # 就是之前OP_REPLY返回的那个

    kill_body = (
        struct.pack("<i", 0) +  # ZERO
        struct.pack("<i", number_of_cursor_ids) +
        struct.pack("<q", *cursor_ids_to_kill)  # 单个cursorID
    )

    # 计算消息总长度: 16 (header) + 4 (ZERO) + 4 (num IDs) + 8 (1 ID) = 32
    message_length = 16 + len(kill_body)

    op_kill_cursors_msg = struct.pack("<iiii", message_length, kill_request_id, 0, op_code_kill) + kill_body
    c_packet_kill = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_kill_cursors_msg)
    packets.append(c_packet_kill)
    seq_num += len(op_kill_cursors_msg)

    # 7. TCP Teardown (由客户端发起，因为OP_KILL_CURSORS没有服务器响应)
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_pcap(packets, "mongodb_paged_query_kill_cursors.pcap")

def generate_op_compressed_snappy_scenario():
    """生成OP_COMPRESSED (snappy)消息场景的数据包"""
    try:
        import snappy
    except ImportError:
        print("python-snappy库未安装，跳过snappy压缩场景。请运行 'pip install python-snappy'。")
        return

    packets = []
    global seq_num, ack_num
    
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    op_query_full_collection_name = b"test.inventory\x00"
    op_query_bson_query = BSON.encode({"status": "A"})
    op_query_bson_return_fields_selector = BSON.encode(OrderedDict([("item", 1), ("_id", 0)]))
    original_query_body = (
        struct.pack("<i", 0) +
        op_query_full_collection_name +
        struct.pack("<i", 0) +
        struct.pack("<i", 2) +
        op_query_bson_query +
        op_query_bson_return_fields_selector
    )
    compressed_body = snappy.compress(original_query_body)
    msg_length = 16 + 4 + 4 + 1 + len(compressed_body)
    request_id = random.randint(100, 1000)
    op_code = 2012
    original_opcode = 2004
    uncompressed_size = len(original_query_body)
    compressor_id = 1  # snappy

    op_compressed_msg = (
        struct.pack("<iiii", msg_length, request_id, 0, op_code) +
        struct.pack("<i", original_opcode) +
        struct.pack("<i", uncompressed_size) +
        struct.pack("<B", compressor_id) +
        compressed_body
    )
    c_packet = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_compressed_msg)
    packets.append(c_packet)
    seq_num += len(op_compressed_msg)

    doc1 = BSON.encode(OrderedDict([("_id", ObjectId(b'\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c')),("status", "A"),("item", "canvas")]))
    doc2 = BSON.encode(OrderedDict([("_id", ObjectId(b'\x0d\x0e\x0f\x10\x11\x12\x13\x14\x15\x16\x17\x18')),("status", "A"),("item", "brush")]))
    reply_documents = doc1 + doc2
    reply_body_packed = (struct.pack("<i", 0) + struct.pack("<q", 0) + struct.pack("<i", 0) + struct.pack("<i", 2) + reply_documents)
    reply_msg_len = 16 + len(reply_body_packed)
    reply_request_id = request_id + 1
    reply = struct.pack("<iiii", reply_msg_len, reply_request_id, request_id, 1) + reply_body_packed
    s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, reply)
    packets.append(s_packet)
    ack_num += len(reply)
    
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_op_compressed_snappy.pcap")

def generate_op_compressed_zstd_scenario():
    """生成OP_COMPRESSED (zstd)消息场景的数据包"""
    try:
        import zstandard as zstd
    except ImportError:
        print("zstandard库未安装，跳过zstd压缩场景。请运行 'pip install zstandard'。")
        return
        
    packets = []
    global seq_num, ack_num
    
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    op_query_full_collection_name = b"test.inventory\x00"
    op_query_bson_query = BSON.encode({"status": "A"})
    op_query_bson_return_fields_selector = BSON.encode(OrderedDict([("item", 1), ("_id", 0)]))
    original_query_body = (
        struct.pack("<i", 0) +
        op_query_full_collection_name +
        struct.pack("<i", 0) +
        struct.pack("<i", 2) +
        op_query_bson_query +
        op_query_bson_return_fields_selector
    )
    compressor = zstd.ZstdCompressor()
    compressed_body = compressor.compress(original_query_body)
    msg_length = 16 + 4 + 4 + 1 + len(compressed_body)
    request_id = random.randint(100, 1000)
    op_code = 2012
    original_opcode = 2004
    uncompressed_size = len(original_query_body)
    compressor_id = 3  # zstd

    op_compressed_msg = (
        struct.pack("<iiii", msg_length, request_id, 0, op_code) +
        struct.pack("<i", original_opcode) +
        struct.pack("<i", uncompressed_size) +
        struct.pack("<B", compressor_id) +
        compressed_body
    )
    c_packet = create_mongodb_packet(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_compressed_msg)
    packets.append(c_packet)
    seq_num += len(op_compressed_msg)

    doc1 = BSON.encode(OrderedDict([("_id", ObjectId(b'\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c')),("status", "A"),("item", "canvas")]))
    doc2 = BSON.encode(OrderedDict([("_id", ObjectId(b'\x0d\x0e\x0f\x10\x11\x12\x13\x14\x15\x16\x17\x18')),("status", "A"),("item", "brush")]))
    reply_documents = doc1 + doc2
    reply_body_packed = (struct.pack("<i", 0) + struct.pack("<q", 0) + struct.pack("<i", 0) + struct.pack("<i", 2) + reply_documents)
    reply_msg_len = 16 + len(reply_body_packed)
    reply_request_id = request_id + 1
    reply = struct.pack("<iiii", reply_msg_len, reply_request_id, request_id, 1) + reply_body_packed
    s_packet = create_mongodb_packet(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, reply)
    packets.append(s_packet)
    ack_num += len(reply)
    
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_op_compressed_zstd.pcap")

def generate_large_op_query_scenario():
    """生成大型OP_QUERY消息查询场景的数据包（带TCP分段）"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造大型OP_QUERY消息，包含复杂查询条件和大型投影
    request_id = 101
    response_to = 0
    op_code = 2004  # OP_QUERY
    
    flags = 0
    full_collection_name = b"test.large_collection\x00"
    number_to_skip = 0
    number_to_return = 100  # 请求返回100个文档
    
    # 构造复杂的查询条件：包含多个字段和复杂嵌套结构
    complex_query = OrderedDict([
        ("$and", [
            {"status": {"$in": ["active", "pending", "completed"]}},
            {"category": {"$ne": "deleted"}},
            {"score": {"$gte": 80, "$lte": 100}},
            {"tags": {"$size": {"$gte": 3}}},
            {"metadata.version": {"$exists": True}},
            {"created_at": {"$gte": "2023-01-01T00:00:00Z"}}
        ]),
        ("$or", [
            {"priority": "high"},
            {"urgent": True}
        ])
    ])
    bson_query = BSON.encode(complex_query)
    
    # 构造大型投影：包含多个字段的详细投影
    large_projection = OrderedDict([
        ("_id", 1),
        ("title", 1),
        ("description", 1),
        ("status", 1),
        ("category", 1),
        ("score", 1),
        ("tags", 1),
        ("metadata", 1),
        ("created_at", 1),
        ("updated_at", 1),
        ("author", 1),
        ("comments", 1),
        ("attachments", 1),
        ("priority", 1),
        ("urgent", 1),
        ("history", {"$slice": 10}),  # 限制历史记录数量
        ("statistics.views", 1),
        ("statistics.likes", 1),
        ("statistics.shares", 1)
    ])
    bson_return_fields_selector = BSON.encode(large_projection)
    
    # 组装OP_QUERY消息体
    query_body = (
        struct.pack("<i", flags) +
        full_collection_name +
        struct.pack("<i", number_to_skip) +
        struct.pack("<i", number_to_return) +
        bson_query +
        bson_return_fields_selector
    )
    
    msg_length = 16 + len(query_body)
    
    op_query_msg = (
        struct.pack("<iiii", msg_length, request_id, response_to, op_code) +
        query_body
    )
    
    # 客户端发送大型OP_QUERY消息（使用TCP分段）
    query_segments, new_seq_num = create_tcp_segments(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_query_msg)
    packets.extend(query_segments)
    seq_num = new_seq_num
    
    # 服务器响应大型OP_REPLY
    reply_request_id = 102
    reply_response_to = request_id
    reply_op_code = 1  # OP_REPLY
    
    response_flags = 0
    cursor_id = 0  # 所有数据在此批次返回
    starting_from = 0
    number_returned = 50  # 实际返回50个文档
    
    # 生成50个大型文档作为查询结果
    reply_documents = b""
    for i in range(50):
        doc = OrderedDict([
            ("_id", ObjectId(bytes([i % 256] + [0] * 11))),
            ("title", f"Large Document {i+1}"),
            ("description", "This is a large document with extensive content for testing purposes. " * 5),
            ("status", ["active", "pending", "completed"][i % 3]),
            ("category", f"category_{i % 10}"),
            ("score", 80 + (i % 20)),
            ("tags", [f"tag_{j}" for j in range(i % 5 + 3)]),
            ("metadata", {
                "version": f"1.{i}",
                "size": 1024 * (i % 10 + 1),
                "type": "document",
                "encoding": "utf-8"
            }),
            ("created_at", "2023-01-01T00:00:00Z"),
            ("updated_at", "2023-12-01T00:00:00Z"),
            ("author", {
                "name": f"Author {i+1}",
                "email": f"author{i+1}@example.com",
                "role": "editor"
            }),
            ("comments", [f"Comment {j+1} for document {i+1}" for j in range(i % 3 + 1)]),
            ("attachments", [f"file_{i}_{j}.pdf" for j in range(i % 2 + 1)]),
            ("priority", ["low", "medium", "high"][i % 3]),
            ("urgent", i % 5 == 0),
            ("statistics", {
                "views": i * 100,
                "likes": i * 10,
                "shares": i * 5
            })
        ])
        reply_documents += BSON.encode(doc)
    
    reply_body = (
        struct.pack("<i", response_flags) +
        struct.pack("<q", cursor_id) +
        struct.pack("<i", starting_from) +
        struct.pack("<i", number_returned) +
        reply_documents
    )
    
    reply_msg_length = 16 + len(reply_body)
    
    op_reply_msg = (
        struct.pack("<iiii", reply_msg_length, reply_request_id, reply_response_to, reply_op_code) +
        reply_body
    )
    
    # 服务器发送大型OP_REPLY消息（使用TCP分段）
    reply_segments, new_ack_num = create_tcp_segments(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_reply_msg)
    packets.extend(reply_segments)
    ack_num = new_ack_num
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_large_op_query_segmented.pcap")

def generate_large_op_msg_scenario():
    """生成大型OP_MSG消息查询场景的数据包（带TCP分段）"""
    packets = []
    global seq_num, ack_num
    
    # 添加TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT)
    packets.extend(handshake_packets)
    
    # 构造大型OP_MSG查询消息
    request_id = 201
    response_to = 0
    op_code = 2013  # OP_MSG
    
    flag_bits = 0
    
    # 构造复杂的聚合管道查询
    complex_command = OrderedDict([
        ("aggregate", "large_collection"),
        ("pipeline", [
            {
                "$match": {
                    "$and": [
                        {"status": {"$in": ["active", "pending"]}},
                        {"score": {"$gte": 85}},
                        {"category": {"$ne": "archived"}},
                        {"created_at": {"$gte": "2023-01-01"}}
                    ]
                }
            },
            {
                "$lookup": {
                    "from": "users",
                    "localField": "author_id",
                    "foreignField": "_id",
                    "as": "author_details"
                }
            },
            {
                "$unwind": "$author_details"
            },
            {
                "$project": {
                    "_id": 1,
                    "title": 1,
                    "description": 1,
                    "status": 1,
                    "score": 1,
                    "category": 1,
                    "tags": 1,
                    "created_at": 1,
                    "author_name": "$author_details.name",
                    "author_email": "$author_details.email",
                    "computed_priority": {
                        "$cond": {
                            "if": {"$gte": ["$score", 95]},
                            "then": "high",
                            "else": "normal"
                        }
                    }
                }
            },
            {
                "$sort": {"score": -1, "created_at": -1}
            },
            {
                "$limit": 100
            }
        ]),
        ("cursor", {"batchSize": 50}),
        ("$db", "test"),
        ("allowDiskUse", True),
        ("maxTimeMS", 30000)
    ])
    
    command_document = BSON.encode(complex_command)
    
    # 组装OP_MSG消息体
    section_kind = 0
    msg_body = (
        struct.pack("<I", flag_bits) +
        struct.pack("<B", section_kind) +
        command_document
    )
    
    msg_length = 16 + len(msg_body)
    
    op_msg_request = (
        struct.pack("<iiii", msg_length, request_id, response_to, op_code) +
        msg_body
    )
    
    # 客户端发送大型OP_MSG消息（使用TCP分段）
    query_segments, new_seq_num = create_tcp_segments(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num, op_msg_request)
    packets.extend(query_segments)
    seq_num = new_seq_num
    
    # 服务器响应大型OP_MSG
    reply_request_id = 202
    reply_response_to = request_id
    reply_op_code = 2013  # OP_MSG
    
    reply_flag_bits = 0
    
    # 生成30个复杂的聚合结果文档
    first_batch_docs = []
    for i in range(30):
        doc = OrderedDict([
            ("_id", ObjectId(bytes([i % 256] + [1] * 11))),
            ("title", f"Aggregated Document {i+1}"),
            ("description", "This is a complex aggregated document with joined data and computed fields. " * 3),
            ("status", "active" if i % 2 == 0 else "pending"),
            ("score", 95 + (i % 5)),
            ("category", f"premium_category_{i % 5}"),
            ("tags", [f"aggregated_tag_{j}" for j in range(i % 4 + 2)]),
            ("created_at", "2023-06-01T00:00:00Z"),
            ("author_name", f"Premium Author {i+1}"),
            ("author_email", f"premium.author{i+1}@example.com"),
            ("computed_priority", "high" if (95 + (i % 5)) >= 95 else "normal")
        ])
        first_batch_docs.append(doc)
    
    # 构造cursor响应
    cursor_response = OrderedDict([
        ("id", 9876543210),  # 非零cursor ID表示还有更多数据
        ("ns", "test.large_collection"),
        ("firstBatch", first_batch_docs)
    ])
    
    reply_document = OrderedDict([
        ("cursor", cursor_response),
        ("ok", 1)
    ])
    
    reply_doc_encoded = BSON.encode(reply_document)
    
    reply_section_kind = 0
    reply_msg_body = (
        struct.pack("<I", reply_flag_bits) +
        struct.pack("<B", reply_section_kind) +
        reply_doc_encoded
    )
    
    reply_msg_length = 16 + len(reply_msg_body)
    
    op_msg_reply = (
        struct.pack("<iiii", reply_msg_length, reply_request_id, reply_response_to, reply_op_code) +
        reply_msg_body
    )
    
    # 服务器发送大型OP_MSG响应消息（使用TCP分段）
    reply_segments, new_ack_num = create_tcp_segments(SERVER_IP, CLIENT_IP, MONGODB_PORT, client_port, ack_num, seq_num, op_msg_reply)
    packets.extend(reply_segments)
    ack_num = new_ack_num
    
    # 添加TCP四次挥手
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, MONGODB_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)
    
    write_pcap(packets, "mongodb_large_op_msg_segmented.pcap")

def main():
    """主函数，生成所有场景的PCAP文件"""
    print("开始生成MongoDB协议PCAP文件...")
    
    # 生成各种场景的PCAP
    generate_op_compressed_zlib_scenario()  # 生成OP_COMPRESSED (zlib)消息场景
    generate_op_compressed_snappy_scenario()  # 生成OP_COMPRESSED (snappy)消息场景
    generate_op_compressed_zstd_scenario()  # 生成OP_COMPRESSED (zstd)消息场景
    generate_op_query_scenario()  # 生成OP_QUERY消息场景
    generate_op_insert_scenario()  # 生成OP_INSERT消息场景
    generate_op_update_scenario()  # 生成OP_UPDATE消息场景
    generate_op_delete_scenario()  # 生成OP_DELETE消息场景
    generate_op_msg_scenario()  # 生成OP_MSG消息场景
    generate_op_msg_with_document_sequence_scenario()  # 生成OP_MSG with document_sequence消息场景
    generate_op_get_more_scenario() # 生成OP_GET_MORE消息场景
    generate_paged_query_and_kill_cursors_scenario() # 生成分页查询并KILL游标的场景
    
    # 生成大型数据查询/返回测试场景
    generate_large_op_query_scenario()  # 生成大型OP_QUERY消息查询场景
    generate_large_op_msg_scenario()  # 生成大型OP_MSG消息查询场景
    
    print(f"所有PCAP文件已生成到 {output_dir} 目录")

if __name__ == "__main__":
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    main()