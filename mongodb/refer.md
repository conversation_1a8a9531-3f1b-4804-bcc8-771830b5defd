## op_query 结构体定义
```c
// OP_QUERY消息结构
typedef struct mongo_op_query {
    mongo_msg_header_t header;            // 标准消息头
    int32_t flags;                        // 查询标志位
    char *fullCollectionName;             // 完整集合名(数据库.集合)
    int32_t fullCollectionName_len;       // 集合名长度 (不包括 \0)
    int32_t numberToSkip;                 // 跳过的文档数
    int32_t numberToReturn;               // 返回的文档数
    uint8_t *query_bson;                  // 查询文档 (BSON) - 使用 libbson 解析
    int32_t query_bson_len;               // 查询文档BSON长度
    uint8_t *returnFieldsSelector_bson;   // 字段选择器(可选 BSON) - 使用 libbson 解析
    int32_t returnFieldsSelector_bson_len;// 字段选择器BSON长度
} mongo_op_query_t;
```
## op_reply 结构体定义
```c
typedef struct mongo_op_reply {
    mongo_msg_header_t header;            // 标准消息头
    int32_t responseFlags;                // 响应标志位
    int64_t cursorID;                     // 游标ID
    int32_t startingFrom;                 // 起始位置
    int32_t numberReturned;               // 返回的文档数
    uint8_t *documents;                   // BSON文档数据
    int32_t documents_len;                // 文档数据长度
    int32_t document_count;               // 文档数量
} mongo_op_reply_t;
```

## op_query 消息示例
```c
/* MsgHeader */
int32   messageLength = 96;          // 整个报文长度（字节）
int32   requestID     = 1;           // 客户端自增请求号
int32   responseTo    = 0;           // 请求时为 0
int32   opCode        = 2004;        // OP_QUERY

/* OP_QUERY body */
int32   flags         = 0;           // 标志位，0 表示默认
cstring fullCollectionName = "test.inventory"; // "dbname.collectionname"
int32   numberToSkip  = 0;           // 跳过文档数
int32   numberToReturn= 2;           // 本次希望返回的文档数
BSON    query         = {
    "status": "A"
};
BSON    returnFieldsSelector = {
    "item": 1,  // 只返回 item 字段
    "_id": 0     // 可选：不返回 _id
};
```

## op_reply 消息示例
```c
/* MsgHeader */
int32   messageLength = 152;         // 整个报文长度
int32   requestID     = 2;           // 服务器给本次响应分配的新 ID
int32   responseTo    = 1;           // 对应客户端的 requestID
int32   opCode        = 1;           // OP_REPLY

/* OP_REPLY body */
int32   responseFlags = 0;           // 响应标志，0 表示正常
int64   cursorID      = 0;           // 服务器游标 ID（0 表示已关闭）
int32   startingFrom  = 0;           // 本批文档中第一条在整个结果集中的偏移
int32   numberReturned= 2;           // 实际返回文档数
BSON    documents[2]  = [
    {
      "_id": ObjectId("5f1e7d9b1e9f1a2b3c4d5e6f"),
      "status": "A",
      "item": "canvas"
    },
    {
      "_id": ObjectId("5f1e7d9b1e9f1a2b3c4d5e70"),
      "status": "A",
      "item": "brush"
    }
];
```