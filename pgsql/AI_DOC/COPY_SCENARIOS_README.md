# PostgreSQL COPY操作测试场景完善

本文档描述了新增的PostgreSQL COPY操作测试场景，完善了COPY协议的测试覆盖。

## 新增测试场景概述

### 1. COPY FROM STDIN 操作场景 (`generate_copy_from_stdin_scenario`)

**文件名**: `pgsql_copy_from_stdin.pcap`

**测试目的**: 验证PostgreSQL COPY IN协议流程，测试客户端向服务器传输数据的完整过程。

**协议特性**:
- COPY FROM STDIN命令 → CopyInResponse → CopyData序列 → CopyDone → CommandComplete
- 使用CSV格式传输员工数据（id, name, department, salary）
- 包含7行测试数据，涵盖不同部门和薪资范围

**消息序列**:
1. 客户端发送Query消息：`COPY employees (id, name, department, salary) FROM STDIN WITH CSV HEADER;`
2. 服务器响应CopyInResponse消息（消息类型'G'）
3. 客户端发送CSV头部：`id,name,department,salary`
4. 客户端发送7行员工数据（每行一个CopyData消息）
5. 客户端发送CopyDone消息（消息类型'c'）
6. 服务器响应CommandComplete：`COPY 7`
7. 服务器发送ReadyForQuery消息

**技术特点**:
- 使用动态长度计算，遵循PostgreSQL协议规范
- CopyInResponse正确设置4列文本格式
- 每个CopyData消息包含完整的CSV行数据
- 生成的pcap文件能在Wireshark中正确解析

### 2. COPY BOTH 操作场景 (`generate_copy_both_scenario`)

**文件名**: `pgsql_copy_both.pcap`

**测试目的**: 验证PostgreSQL COPY BOTH双向数据传输协议流程，模拟流复制协议的消息序列。

**协议特性**:
- 流复制启动 → CopyBothResponse → 双向CopyData交换 → 状态反馈机制
- 模拟逻辑复制数据流（INSERT、UPDATE、DELETE操作）
- 包含复制反馈和Keep-Alive机制

**消息序列**:
1. 客户端发送流复制启动消息（replication=database）
2. 服务器认证成功并发送参数状态
3. 客户端发送START_REPLICATION命令
4. 服务器响应CopyBothResponse消息（消息类型'W'）
5. 服务器发送WAL数据流：
   - INSERT操作的逻辑复制数据
   - UPDATE操作的逻辑复制数据  
   - DELETE操作的逻辑复制数据
6. 客户端发送复制反馈消息（确认接收和应用状态）
7. 服务器发送Keep-Alive消息
8. 客户端响应Keep-Alive请求

**技术特点**:
- 实现完整的双向数据流协议
- WAL数据包含LSN（Log Sequence Number）和时间戳
- 逻辑复制数据使用JSON格式表示数据库变更
- 复制反馈包含接收、刷新、应用LSN的状态跟踪
- 符合PostgreSQL流复制协议规范

## 现有COPY操作场景对比

### 已有场景
1. **COPY TO STDOUT** (`generate_copy_to_stdout_scenario`): 服务器向客户端发送数据
2. **COPY错误处理** (`generate_copy_fail_scenario`): CopyFail消息处理
3. **二进制COPY** (`generate_binary_copy_scenario`): 二进制格式数据传输
4. **认证+COPY组合** (`generate_auth_with_copy_scenario`): 认证流程结合COPY操作

### 新增场景补充
- **COPY FROM STDIN**: 补充了客户端向服务器传输数据的标准场景
- **COPY BOTH**: 补充了双向数据传输的流复制协议场景

## 协议规范遵循

### 消息格式规范
- 所有消息使用动态长度计算，避免硬编码
- 消息长度字段包含长度字段本身的4字节
- 字符串正确使用NULL终止符
- 使用网络字节序（大端序）

### COPY协议特性
- **CopyInResponse ('G')**: 正确设置格式标志和列数
- **CopyOutResponse ('H')**: 服务器准备发送数据
- **CopyBothResponse ('W')**: 建立双向数据流连接
- **CopyData ('d')**: 实际数据传输
- **CopyDone ('c')**: 数据传输完成标志

### 流复制协议特性
- WAL数据消息格式：消息类型 + LSN + 时间戳 + 数据
- 复制反馈消息：接收/刷新/应用LSN + 时间戳 + 标志
- Keep-Alive机制：服务器LSN + 时间戳 + 回复请求

## 测试验证

使用 `test_copy_scenarios.py` 脚本可以验证生成的pcap文件：

```bash
python3 test_copy_scenarios.py
```

验证内容包括：
- 数据包数量和结构
- PostgreSQL消息类型和长度
- 协议消息序列的正确性
- Wireshark解析兼容性

## 使用方法

在 `generate_pgsql_pcap.py` 的main函数中，新增的场景已自动包含：

```python
# 新增COPY操作完善测试场景
generate_copy_to_stdout_scenario()      # COPY TO STDOUT操作场景
generate_copy_from_stdin_scenario()     # COPY FROM STDIN操作场景 (新增)
generate_copy_both_scenario()           # COPY BOTH双向数据传输场景 (新增)
generate_copy_fail_scenario()           # COPY操作错误处理场景
generate_binary_copy_scenario()         # 二进制格式COPY操作场景
```

运行完整测试：
```bash
python3 generate_pgsql_pcap.py
```

单独测试新增场景：
```python
from generate_pgsql_pcap import generate_copy_from_stdin_scenario, generate_copy_both_scenario
generate_copy_from_stdin_scenario()
generate_copy_both_scenario()
```

## 总结

通过新增COPY FROM STDIN和COPY BOTH两个测试场景，PostgreSQL协议测试现在完整覆盖了：

1. **COPY IN**: 客户端向服务器传输数据
2. **COPY OUT**: 服务器向客户端传输数据  
3. **COPY BOTH**: 双向数据传输（流复制协议）
4. **COPY错误处理**: 异常情况处理
5. **二进制COPY**: 二进制格式传输

所有场景都严格遵循PostgreSQL wire protocol规范，生成的pcap文件能在Wireshark中正确显示和解析，为PostgreSQL协议解析器的开发和测试提供了完整的测试数据。
