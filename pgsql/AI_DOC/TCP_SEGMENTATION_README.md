# PostgreSQL协议TCP分段传输功能实现

## 概述

本文档描述了在PostgreSQL协议测试脚本中实现的TCP分段传输功能，用于模拟真实网络环境中的数据包分段和重组过程。

## 功能特性

### 1. 自动TCP分段
- **MTU设置**: 1500字节（标准以太网MTU）
- **最大TCP载荷**: 1460字节（MTU - IP头部20字节 - TCP头部20字节）
- **自动分段**: 当PostgreSQL消息超过1460字节时，自动分割成多个TCP段
- **序列号管理**: 保持TCP序列号的连续性和正确性

### 2. 智能标志位处理
- **第一个段**: 保持原始TCP标志位，但移除PSH标志（如果有多个段）
- **中间段**: 只设置ACK标志
- **最后一个段**: 设置PSH标志（如果原始消息包含PSH）

### 3. PostgreSQL协议完整性
- **消息边界保护**: 确保PostgreSQL协议消息在分段后能正确重组
- **协议规范遵循**: 严格按照`.augment/rules/pgsql.md`中的规则实现
- **动态长度计算**: 所有消息长度都动态计算，避免硬编码

## 核心函数

### `create_segmented_pgsql_packets()`

```python
def create_segmented_pgsql_packets(src, dst, sport, dport, seq, ack, payload, flags="PA"):
    """创建TCP分段的PostgreSQL数据包
    
    Args:
        src: 源IP地址
        dst: 目标IP地址
        sport: 源端口
        dport: 目标端口
        seq: 起始序列号
        ack: 确认号
        payload: 要发送的数据
        flags: TCP标志位
        
    Returns:
        tuple: (packets_list, final_seq_num) - 分段数据包列表和最终序列号
    """
```

**功能说明**:
- 自动检测payload大小
- 小于1460字节时创建单个包
- 大于1460字节时自动分段
- 返回分段包列表和更新后的序列号

## 应用场景

### 1. 大查询消息分段
- **场景**: `generate_max_message_length_scenario()`
- **消息大小**: 约16KB
- **分段数量**: 约12个TCP段
- **用途**: 测试大型SQL查询的分段传输

### 2. 大结果集分段
- **场景**: `generate_large_result_scenario()`
- **数据量**: 200行数据，每行约500字节
- **批次处理**: 每批50行，约24KB
- **分段数量**: 每批约17个TCP段

### 3. 高性能场景分段
- **场景**: `generate_high_performance_scenario()`
- **批量处理**: 每批100个查询
- **数据量**: 每批约50KB
- **分段数量**: 每批约35个TCP段

### 4. 最大参数数量分段
- **场景**: `generate_max_parameters_scenario()`
- **Parse消息**: 约10KB，分成7个TCP段
- **Bind消息**: 约12KB，分成9个TCP段
- **用途**: 测试大量参数的预处理语句分段传输

### 5. 最大字符串长度分段
- **场景**: `generate_max_string_length_scenario()`
- **查询消息**: 约16KB，分成12个TCP段
- **DataRow响应**: 约8KB，分成6个TCP段
- **用途**: 测试超长字符串的分段传输

### 6. 管道化查询分段
- **场景**: `generate_pipelined_queries_scenario()`
- **批量响应**: 多个查询响应组合
- **用途**: 测试管道化查询的批量响应分段

### 7. 事务分隔分段
- **场景**: `generate_sync_separated_transactions_scenario()`
- **事务响应**: 多个事务操作的组合响应
- **用途**: 测试事务分隔场景的响应分段

## 验证方法

### 1. Wireshark验证
```bash
# 打开生成的PCAP文件
wireshark pgsql_pcaps/pgsql_tcp_segmentation_demo.pcap
```

**检查要点**:
- TCP层显示分段信息
- 序列号连续性
- PostgreSQL协议正确重组
- 无"Malformed Packet"错误

### 2. 测试脚本
```bash
cd pgsql
python3 test_tcp_segmentation.py
```

**输出示例**:
```
=== TCP分段演示场景 ===
MTU: 1500 字节
最大TCP载荷: 1460 字节
大查询消息总大小: 5630 字节
预计需要 4 个TCP段
实际生成了 4 个TCP段
```

## 生成的测试文件

| 文件名 | 大小 | TCP分段 | 描述 |
|--------|------|---------|------|
| `pgsql_max_message_length.pcap` | 37KB | 12段 | 最大消息长度测试 |
| `pgsql_large_result.pcap` | 104KB | 多段 | 大结果集测试 |
| `pgsql_max_parameters.pcap` | 24KB | 16段 | 最大参数数量测试 |
| `pgsql_max_string_length.pcap` | 27KB | 18段 | 最大字符串长度测试 |
| `pgsql_pipelined_queries.pcap` | 2KB | 无 | 管道化查询测试 |
| `pgsql_sync_separated_transactions.pcap` | 2KB | 无 | 事务分隔测试 |

## 技术细节

### 1. MTU配置
```python
MTU = 1500  # 标准以太网MTU
IP_HEADER_SIZE = 20  # IP头部大小
TCP_HEADER_SIZE = 20  # TCP头部大小
MAX_TCP_PAYLOAD = MTU - IP_HEADER_SIZE - TCP_HEADER_SIZE  # 1460字节
```

### 2. 分段算法
```python
segments_needed = (len(payload) + MAX_TCP_PAYLOAD - 1) // MAX_TCP_PAYLOAD
```

### 3. 序列号更新
```python
current_seq += len(segment_data)
```

## 兼容性

- **Wireshark**: 完全兼容，支持TCP重组和PostgreSQL协议解析
- **网络分析工具**: 标准TCP分段格式，兼容所有主流工具
- **PostgreSQL客户端**: 生成的数据包符合PostgreSQL协议规范

## 使用建议

1. **测试网络解析器**: 使用生成的PCAP文件测试TCP重组功能
2. **协议分析**: 验证PostgreSQL协议解析器的分段处理能力
3. **性能测试**: 模拟真实网络环境中的大数据传输场景
4. **边界测试**: 测试接近MTU限制的消息处理能力

## 注意事项

1. **内存使用**: 大数据场景会消耗较多内存，建议在充足内存环境下运行
2. **文件大小**: 生成的PCAP文件可能较大，注意磁盘空间
3. **处理时间**: 大量分段会增加生成时间，请耐心等待
4. **网络环境**: 实际网络中的MTU可能不同，可根据需要调整配置
