# PostgreSQL协议PCAP生成工具

这个工具使用Python的Scapy库生成各种PostgreSQL协议场景的PCAP文件，用于测试流量解析器。

## 依赖

- Python 3.6+
- Scapy库

## 安装依赖

```bash
pip install scapy
```

## 功能特性

### 基础场景
- 认证场景 (pgsql_authentication.pcap)
- 简单查询场景 (pgsql_simple_query.pcap)
- 预处理语句场景 (pgsql_prepared_statement.pcap)
- 错误处理场景 (pgsql_error.pcap)
- 事务场景 (pgsql_transaction.pcap)
- 通知场景 (pgsql_notification.pcap)
- 大结果集场景 (pgsql_large_result.pcap)
- 批量操作场景 (pgsql_batch_operations.pcap)
- 取消查询场景 (pgsql_cancel_query.pcap)
- 多查询场景 (pgsql_multi_query.pcap)
- 二进制扩展查询场景 (pgsql_binary_extended_query.pcap)
- 认证+COPY场景 (pgsql_auth_with_copy.pcap)

### 高性能测试场景
- **高性能测试场景** (pgsql_performance_100k_qps.pcap)
  - 目标性能: 100k QPS (每秒10万个请求-响应对)
  - 报文大小: 平均约500字节
  - 文件大小: 约95MB (包含200,000个报文)
  - 测试时长: 1秒的测试数据
  - **负载均衡特性**: 多IP多端口，触发TCP负载均衡机制
    - 16个客户端IP地址 (************-25)
    - 8个服务器IP地址 (************-17)
    - 10,000个并发会话 (100k QPS / 10 QPS per session)
    - 丰富的四元组分布 (IP+端口组合)

## 性能测试参数分析

### 100k QPS性能测试
- **QPS定义**: 每秒查询数(请求-响应对数量)
- **报文数量**: 200,000个报文 (100k请求 + 100k响应)
- **单报文大小**: 约500字节
- **总数据量**: 约100MB/秒
- **网络带宽**: 约800-1000 Mbps

### tcpreplay回放建议
```bash
# 为达到100k QPS，建议使用以下参数：
tcpreplay -i eth0 -M 1000 pgsql_performance_100k_qps.pcap

# 参数说明：
# -i eth0: 指定网络接口
# -M 1000: 以1000 Mbps速率回放
# 实际速率可根据测试环境调整(800-1200 Mbps)

# 负载均衡测试建议：
# 多网卡环境下可以测试负载均衡效果
# 不同的四元组会被分配到不同的处理队列
```

### 性能测试环境要求
- 网络带宽: ≥1 Gbps
- CPU: 多核处理器，主频≥2.4GHz
- 内存: ≥4GB可用内存
- 存储: SSD推荐(快速读取PCAP文件)

## 使用方法

### 生成所有场景的PCAP文件
```bash
python generate_pgsql_pcap.py
```

### 单独生成高性能测试文件
```bash
# 使用交互式脚本生成指定QPS的测试文件
python generate_performance_test.py

# 示例输出：
# 请输入目标QPS (默认100000): 50000
# 将生成50k QPS的测试文件
```

生成的文件将保存在 `pgsql_pcaps/` 目录下

## 测试验证

### 使用内置验证工具
```bash
# 交互式验证PCAP文件
python validate_pcap.py

# 直接验证指定文件
python validate_pcap.py pgsql_pcaps/pgsql_performance_100k_qps.pcap
```

### 使用第三方工具
生成的PCAP文件也可以使用以下工具进行验证：
- **Wireshark**: 查看协议解析结果和流量分析
- **tcpdump**: 基础包分析和过滤
- **tcpreplay**: 流量回放测试
- **自定义解析器**: 性能和准确性测试

## 注意事项

1. 高性能测试文件较大(~95MB)，生成时间较长
2. 回放时需要足够的网络带宽和系统资源
3. 实际QPS可能受到网络延迟、系统负载等因素影响
4. 建议在测试环境中先进行小规模验证