# PostgreSQL协议TCP网络丢包异常测试场景

本目录包含专门用于测试PostgreSQL协议在TCP网络丢包情况下的异常行为的pcap文件。这些测试场景模拟真实生产环境中可能遇到的各种网络异常情况。

**当前包含21个完整的丢包测试场景，覆盖PostgreSQL协议的所有关键阶段。**

## 📁 文件结构

每个测试场景包含两个文件：
- `*.pcap` - 包含丢包情况的网络数据包文件
- `*_loss_report.txt` - 详细的丢包分析报告

## 🎯 测试场景分类（21个场景）

### 1. 认证阶段丢包场景

#### `pgsql_startup_message_lost.pcap`
- **测试目的**：模拟客户端启动消息在网络传输中丢失
- **预期结果**：服务器无法收到连接请求，客户端可能超时重试
- **网络表现**：TCP连接建立后，没有PostgreSQL协议数据传输
- **生产场景**：网络抖动导致初始连接请求丢失

#### `pgsql_auth_request_lost.pcap`
- **测试目的**：模拟服务器认证请求消息丢失
- **预期结果**：客户端无法收到认证要求，连接处于等待状态
- **网络表现**：客户端发送StartupMessage后，没有收到服务器响应
- **生产场景**：防火墙或负载均衡器过滤服务器响应

#### `pgsql_password_response_lost.pcap`
- **测试目的**：模拟客户端密码响应消息丢失
- **预期结果**：服务器无法收到密码，认证流程中断
- **网络表现**：服务器发送认证请求后，没有收到客户端密码响应
- **生产场景**：客户端网络不稳定导致认证失败

#### `pgsql_auth_ok_lost.pcap`
- **测试目的**：模拟服务器认证成功消息丢失
- **预期结果**：客户端无法确认认证状态，可能重试或超时
- **网络表现**：客户端发送密码后，没有收到认证成功确认
- **生产场景**：网络拥塞导致认证确认消息丢失

#### `pgsql_backend_key_lost.pcap`
- **测试目的**：模拟服务器后端密钥数据丢失
- **预期结果**：客户端无法获得取消操作所需的密钥，影响查询取消功能
- **网络表现**：认证成功后，BackendKeyData消息丢失
- **生产场景**：关键认证后续信息在网络传输中丢失

#### `pgsql_ready_for_query_lost.pcap`
- **测试目的**：模拟服务器就绪状态消息丢失
- **预期结果**：客户端无法确认服务器状态，不知道何时可以发送下一个查询
- **网络表现**：认证完成后，ReadyForQuery消息丢失
- **生产场景**：状态同步消息在网络中丢失

### 2. 查询执行阶段丢包场景

#### `pgsql_query_message_lost.pcap`
- **测试目的**：模拟简单查询消息丢失
- **预期结果**：服务器无法收到查询请求，客户端等待响应超时
- **网络表现**：认证完成后，查询请求消息丢失
- **生产场景**：高负载时查询请求在网络中丢失

#### `pgsql_parse_message_lost.pcap`
- **测试目的**：模拟扩展查询协议中Parse消息丢失
- **预期结果**：服务器无法解析预处理语句，后续Bind/Execute失败
- **网络表现**：Parse消息丢失，但Bind和Execute消息正常发送
- **生产场景**：预处理语句在复杂网络环境中的传输问题

#### `pgsql_bind_message_lost.pcap`
- **测试目的**：模拟扩展查询协议中Bind消息丢失
- **预期结果**：服务器无法绑定参数到预处理语句，Execute失败
- **网络表现**：Parse成功，但Bind消息丢失，Execute会报错
- **生产场景**：参数绑定阶段的网络传输问题

#### `pgsql_execute_message_lost.pcap`
- **测试目的**：模拟扩展查询协议中Execute消息丢失
- **预期结果**：服务器完成Parse和Bind，但无法执行查询
- **网络表现**：Parse和Bind成功，但Execute消息丢失
- **生产场景**：查询执行请求在网络中丢失

#### `pgsql_sync_message_lost.pcap`
- **测试目的**：模拟扩展查询协议中Sync消息丢失
- **预期结果**：服务器无法同步状态，可能影响后续查询
- **网络表现**：Parse/Bind/Execute正常，但Sync消息丢失
- **生产场景**：协议同步消息在管道化查询中丢失

### 3. 响应数据丢包场景

#### `pgsql_row_description_lost.pcap`
- **测试目的**：模拟查询结果的行描述信息丢失
- **预期结果**：客户端无法解析后续的DataRow，数据格式错误
- **网络表现**：查询成功但RowDescription消息丢失，DataRow无法正确解析
- **生产场景**：大结果集传输时元数据丢失

### 4. 大数据传输和高级功能场景

#### `pgsql_large_query_segment_lost.pcap`
- **测试目的**：模拟大型查询在TCP分段传输中某个分段丢失
- **预期结果**：查询数据不完整，服务器无法正确解析
- **网络表现**：大查询被分成多个TCP段，其中一个段丢失
- **生产场景**：大型INSERT/UPDATE语句在网络传输中的分段丢失

#### `pgsql_ssl_request_lost.pcap`
- **测试目的**：模拟SSL协商请求在网络传输中丢失
- **预期结果**：无法建立SSL连接，可能回退到非SSL连接或失败
- **网络表现**：SSL请求消息丢失，服务器无响应
- **生产场景**：安全连接建立过程中的网络问题

#### `pgsql_copy_data_lost.pcap`
- **测试目的**：模拟COPY操作中数据传输丢失
- **预期结果**：COPY操作不完整，数据导入失败
- **网络表现**：COPY命令成功，但部分CopyData消息丢失
- **生产场景**：批量数据导入过程中的网络传输问题

### 5. 连接管理和错误处理场景

#### `pgsql_cancel_request_lost.pcap`
- **测试目的**：模拟查询取消请求在网络传输中丢失
- **预期结果**：无法取消正在执行的查询，查询继续运行
- **网络表现**：CancelRequest消息丢失，长时间查询无法被中断
- **生产场景**：紧急查询取消操作失败

#### `pgsql_terminate_lost.pcap`
- **测试目的**：模拟连接终止消息在网络传输中丢失
- **预期结果**：服务器无法正常关闭连接，可能导致资源泄露
- **网络表现**：Terminate消息丢失，连接异常关闭
- **生产场景**：客户端异常断开导致的连接清理问题

#### `pgsql_error_response_lost.pcap`
- **测试目的**：模拟服务器错误响应消息在网络传输中丢失
- **预期结果**：客户端无法获知查询错误，可能一直等待响应
- **网络表现**：错误查询执行后，ErrorResponse消息丢失
- **生产场景**：错误信息在网络传输中丢失，影响故障诊断

### 6. 事务控制和通知系统场景

#### `pgsql_transaction_begin_lost.pcap`
- **测试目的**：模拟事务开始响应消息在网络传输中丢失
- **预期结果**：客户端无法确认事务状态，可能导致事务管理混乱
- **网络表现**：BEGIN命令发送后，CommandComplete响应丢失
- **生产场景**：事务状态同步问题导致的数据一致性风险

#### `pgsql_notification_lost.pcap`
- **测试目的**：模拟NOTIFY通知消息在网络传输中丢失
- **预期结果**：客户端无法收到异步通知，错过重要事件
- **网络表现**：NotificationResponse消息丢失
- **生产场景**：异步通知系统中的消息丢失

#### `pgsql_data_row_lost.pcap`
- **测试目的**：模拟查询结果中部分数据行丢失
- **预期结果**：客户端收到不完整的结果集，数据缺失
- **网络表现**：RowDescription正常，但部分DataRow消息丢失
- **生产场景**：网络不稳定导致数据传输不完整

## 🔍 使用方法

### 1. Wireshark分析
```bash
# 打开pcap文件
wireshark pgsql_startup_message_lost.pcap

# 应用PostgreSQL协议过滤器
pgsql

# 查看TCP流分析
tcp.stream eq 0
```

### 2. 预期观察结果
- **TCP层面**：重传、乱序、丢失段标记
- **PostgreSQL协议层面**：不完整的消息、协议解析错误
- **连接状态**：超时、重置、异常关闭

### 3. 测试验证点
1. **协议解析器鲁棒性**：能否正确处理不完整消息
2. **异常检测能力**：能否识别网络异常模式
3. **错误恢复机制**：能否从丢包中恢复
4. **监控告警**：能否触发相应的网络异常告警

## 📊 丢包统计信息

每个测试场景的详细丢包信息请查看对应的`*_loss_report.txt`文件，包含：
- 丢包位置和时间
- 丢包数据大小
- 丢包率统计
- 预期影响分析

## 🛠️ 生成新场景

使用`generate_pgsql_lost_pcap.py`生成更多丢包场景：

```bash
# 生成所有21个场景
python3 generate_pgsql_lost_pcap.py --all

# 生成特定场景
python3 generate_pgsql_lost_pcap.py --scenario startup_lost
python3 generate_pgsql_lost_pcap.py --scenario bind_lost
python3 generate_pgsql_lost_pcap.py --scenario large_query_segment_lost

# 查看所有可用场景
python3 generate_pgsql_lost_pcap.py --list

# 显示帮助信息
python3 generate_pgsql_lost_pcap.py --help
```

## 🎯 生产环境应用

这些丢包测试场景可以帮助：

1. **网络监控系统测试**：验证监控工具能否检测PostgreSQL连接异常
2. **协议解析器测试**：确保解析器在网络异常时的稳定性
3. **应用程序健壮性测试**：测试应用在网络不稳定时的表现
4. **故障排查训练**：为运维人员提供真实的故障场景
5. **网络设备测试**：验证防火墙、负载均衡器等设备的处理能力

## ⚠️ 注意事项

1. 这些pcap文件仅用于测试目的，不包含真实的生产数据
2. 在生产环境使用前，请确保理解每个场景的影响
3. 建议在隔离的测试环境中进行验证
4. 某些场景可能触发安全监控告警，请提前通知相关团队

## 📝 技术规范

- 严格遵循PostgreSQL协议规范
- 符合TCP/IP网络协议标准
- 兼容Wireshark等主流网络分析工具
- 支持各种PostgreSQL客户端和服务器版本

---

**生成时间**：2024-01-15  
**版本**：1.0  
**维护者**：PostgreSQL协议测试团队
