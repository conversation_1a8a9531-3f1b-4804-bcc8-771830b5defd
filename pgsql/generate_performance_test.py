#!/usr/bin/env python3
"""
PostgreSQL高性能测试PCAP生成器
专门用于生成100k QPS性能测试的PCAP文件
"""

from scapy.all import *
from scapy.layers.inet import IP, TCP
import struct
import random
import os
import time

# PostgreSQL 默认端口
PGSQL_PORT = 5432

# 客户端和服务器IP地址池 - 增加四元组丰富性
CLIENT_IPS = [
    "************", "************", "************", "************",
    "************", "************", "************", "************",
    "************", "************", "************", "************",
    "************", "************", "************", "************"
]

SERVER_IPS = [
    "************", "************", "************", "************",
    "************", "************", "************", "************"
]

def create_pgsql_packet(src, dst, sport, dport, seq, ack, payload, flags="PA"):
    """创建一个PostgreSQL数据包"""
    eth = Ether(type=0x0800)  # IPv4类型
    ip = IP(src=src, dst=dst)
    tcp = TCP(sport=sport, dport=dport, seq=seq, ack=ack, flags=flags)
    return eth/ip/tcp/payload

def create_tcp_handshake(src, dst, sport, dport):
    """创建TCP三次握手数据包"""
    packets = []
    # 初始序列号
    client_isn = random.randint(1000000, 9000000)
    server_isn = random.randint(1000000, 9000000)
    
    # SYN
    syn = create_pgsql_packet(src, dst, sport, dport, client_isn, 0, b"", flags="S")
    packets.append(syn)
    
    # SYN-ACK
    syn_ack = create_pgsql_packet(dst, src, dport, sport, server_isn, client_isn + 1, b"", flags="SA")
    packets.append(syn_ack)
    
    # ACK
    ack = create_pgsql_packet(src, dst, sport, dport, client_isn + 1, server_isn + 1, b"", flags="A")
    packets.append(ack)
    
    return packets, client_isn + 1, server_isn + 1

def create_tcp_teardown(src, dst, sport, dport, seq, ack):
    """创建TCP四次挥手数据包"""
    packets = []
    
    # FIN-ACK (client)
    fin_ack1 = create_pgsql_packet(src, dst, sport, dport, seq, ack, b"", flags="FA")
    packets.append(fin_ack1)
    
    # ACK (server)
    ack1 = create_pgsql_packet(dst, src, dport, sport, ack, seq + 1, b"", flags="A")
    packets.append(ack1)
    
    # FIN-ACK (server)
    fin_ack2 = create_pgsql_packet(dst, src, dport, sport, ack, seq + 1, b"", flags="FA")
    packets.append(fin_ack2)
    
    # ACK (client)
    ack2 = create_pgsql_packet(src, dst, sport, dport, seq + 1, ack + 1, b"", flags="A")
    packets.append(ack2)
    
    return packets

def generate_performance_test_pcap(target_qps=100000, output_dir="pgsql_pcaps"):
    """生成高性能测试PCAP文件"""
    
    print("=" * 60)
    print("PostgreSQL 高性能测试 PCAP 生成器")
    print("=" * 60)
    print(f"目标QPS: {target_qps:,}")
    print(f"目标报文大小: ~500字节")
    print(f"测试时长: 1秒")
    
    # 计算参数
    packets_needed = target_qps * 2  # 每个QPS包含请求+响应
    estimated_size_mb = packets_needed * 500 / 1024 / 1024
    estimated_bandwidth_mbps = estimated_size_mb * 8
    
    # 计算会话数量
    max_sessions = len(CLIENT_IPS) * len(SERVER_IPS) * 100  # 每个IP对最多100个端口
    sessions_needed = min(target_qps // 10, max_sessions)  # 每个会话处理10个QPS
    
    print(f"需要生成报文数: {packets_needed:,}")
    print(f"预计文件大小: {estimated_size_mb:.1f} MB")
    print(f"需要网络带宽: {estimated_bandwidth_mbps:.0f} Mbps")
    print(f"客户端IP数量: {len(CLIENT_IPS)}")
    print(f"服务器IP数量: {len(SERVER_IPS)}")
    print(f"预计会话数: {sessions_needed:,}")
    print("-" * 60)
    
    # 确认继续
    response = input("是否继续生成? (y/N): ").lower()
    if response != 'y':
        print("已取消生成")
        return
    
    start_time = time.time()
    packets = []
    
    print("开始生成数据包...")
    
    # 创建会话池 - 存储每个会话的状态
    sessions = []
    for i in range(sessions_needed):
        client_ip = CLIENT_IPS[i % len(CLIENT_IPS)]
        server_ip = SERVER_IPS[i % len(SERVER_IPS)]
        client_port = 40000 + (i % 20000)  # 端口范围40000-59999
        
        # 为每个会话生成初始序列号
        seq_num = random.randint(1000000, 9000000)
        ack_num = random.randint(1000000, 9000000)
        
        sessions.append({
            'client_ip': client_ip,
            'server_ip': server_ip,
            'client_port': client_port,
            'seq_num': seq_num,
            'ack_num': ack_num,
            'handshake_done': False
        })
    
    print(f"创建了 {len(sessions)} 个会话")
    
    # 生成查询-响应对
    batch_size = 1000
    total_batches = target_qps // batch_size
    queries_per_session = target_qps // len(sessions)  # 每个会话的查询数量
    
    # 首先为所有会话建立TCP连接
    print("建立TCP连接...")
    for i, session in enumerate(sessions):
        if i % 1000 == 0 and i > 0:
            print(f"已建立 {i} 个连接...")
        
        # 为每个会话建立TCP三次握手
        handshake_packets, session['seq_num'], session['ack_num'] = create_tcp_handshake(
            session['client_ip'], session['server_ip'], session['client_port'], PGSQL_PORT)
        packets.extend(handshake_packets)
        session['handshake_done'] = True
    
    print(f"所有 {len(sessions)} 个TCP连接已建立")
    
    # 在所有会话中分布查询
    query_count = 0
    session_query_counts = [0] * len(sessions)  # 跟踪每个会话的查询数量
    
    for batch in range(total_batches):
        if batch % 20 == 0:  # 每20批显示一次进度
            progress = (batch / total_batches) * 100
            elapsed = time.time() - start_time
            print(f"进度: {progress:.1f}% ({batch}/{total_batches}) - 已用时: {elapsed:.1f}s")
        
        for i in range(batch_size):
            query_count += 1
            # 轮询选择会话，确保均匀分布
            session_idx = (query_count - 1) % len(sessions)
            session = sessions[session_idx]
            session_query_counts[session_idx] += 1
            
            # 构造约500字节的查询请求
            base_query = f"SELECT u.id, u.name, u.email, u.created_at, p.title FROM users u LEFT JOIN posts p ON u.id = p.user_id WHERE u.id = {query_count}"
            # 添加注释填充到目标大小
            padding_size = max(0, 400 - len(base_query))
            padding = " -- " + "performance_test_padding_" * (padding_size // 25)
            full_query = base_query + padding + ";\0"
            
            query_bytes = full_query.encode()
            query_msg = b"Q" + struct.pack("!I", len(query_bytes) + 4) + query_bytes
            
            # 客户端请求包
            c_packet = create_pgsql_packet(session['client_ip'], session['server_ip'], 
                                         session['client_port'], PGSQL_PORT, 
                                         session['seq_num'], session['ack_num'], query_msg)
            packets.append(c_packet)
            session['seq_num'] += len(query_msg)
            
            # 构造约500字节的响应
            # 行描述
            row_desc_data = (
                b"id\0" + struct.pack("!IHIHIH", 0, 0, 23, 4, 4, 0) +
                b"name\0" + struct.pack("!IHIHIH", 0, 0, 25, 64, 64, 0) +
                b"email\0" + struct.pack("!IHIHIH", 0, 0, 25, 128, 128, 0) +
                b"created_at\0" + struct.pack("!IHIHIH", 0, 0, 1184, 19, 19, 0) +
                b"title\0" + struct.pack("!IHIHIH", 0, 0, 25, 255, 255, 0)
            )
            row_desc = b"T" + struct.pack("!IH", len(row_desc_data) + 6, 5) + row_desc_data
            
            # 数据行
            user_name = f"TestUser{query_count:06d}"
            user_email = f"testuser{query_count:06d}@performance-test-domain.com"
            timestamp = "2024-01-01 12:00:00"
            post_title = f"Performance Test Post Title {query_count:06d} - This is a sample post for testing"
            
            data_row_content = struct.pack("!H", 5)  # 5列
            data_row_content += struct.pack("!I", len(str(query_count))) + str(query_count).encode()
            data_row_content += struct.pack("!I", len(user_name)) + user_name.encode()
            data_row_content += struct.pack("!I", len(user_email)) + user_email.encode()
            data_row_content += struct.pack("!I", len(timestamp)) + timestamp.encode()
            data_row_content += struct.pack("!I", len(post_title)) + post_title.encode()
            
            data_row = b"D" + struct.pack("!I", len(data_row_content) + 4) + data_row_content
            
            # 命令完成
            cmd_complete_data = b"SELECT 1\0"
            cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_data) + 4) + cmd_complete_data
            
            # 准备就绪
            ready = b"Z" + struct.pack("!IB", 5, 73)
            
            # 组合服务器响应
            server_resp = row_desc + data_row + cmd_complete + ready
            s_packet = create_pgsql_packet(session['server_ip'], session['client_ip'], 
                                         PGSQL_PORT, session['client_port'], 
                                         session['ack_num'], session['seq_num'], server_resp)
            packets.append(s_packet)
            session['ack_num'] += len(server_resp)
    
    # 为所有会话添加TCP四次挥手
    print("关闭TCP连接...")
    for i, session in enumerate(sessions):
        if i % 1000 == 0 and i > 0:
            print(f"已关闭 {i} 个连接...")
        
        teardown_packets = create_tcp_teardown(session['client_ip'], session['server_ip'], 
                                             session['client_port'], PGSQL_PORT, 
                                             session['seq_num'], session['ack_num'])
        packets.extend(teardown_packets)
    
    # 统计信息
    print(f"会话查询分布统计:")
    print(f"最小查询数/会话: {min(session_query_counts)}")
    print(f"最大查询数/会话: {max(session_query_counts)}")
    print(f"平均查询数/会话: {sum(session_query_counts) / len(session_query_counts):.1f}")
    
    generation_time = time.time() - start_time
    print(f"数据包生成完成! 用时: {generation_time:.1f}s")
    print(f"实际生成报文数: {len(packets):,}")
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 写入PCAP文件
    filename = f"pgsql_performance_{target_qps//1000}k_qps.pcap"
    filepath = os.path.join(output_dir, filename)
    
    print(f"正在写入文件: {filepath}")
    write_start = time.time()
    wrpcap(filepath, packets)
    write_time = time.time() - write_start
    
    # 获取实际文件大小
    file_size = os.path.getsize(filepath)
    file_size_mb = file_size / 1024 / 1024
    
    print("=" * 60)
    print("生成完成!")
    print(f"文件路径: {filepath}")
    print(f"文件大小: {file_size_mb:.1f} MB")
    print(f"报文数量: {len(packets):,}")
    print(f"会话数量: {len(sessions):,}")
    print(f"四元组数量: {len(sessions):,}")
    print(f"客户端IP数量: {len(set([s['client_ip'] for s in sessions]))}")
    print(f"服务器IP数量: {len(set([s['server_ip'] for s in sessions]))}")
    print(f"生成用时: {generation_time:.1f}s")
    print(f"写入用时: {write_time:.1f}s")
    print(f"总用时: {generation_time + write_time:.1f}s")
    print("=" * 60)
    
    # 四元组统计
    unique_tuples = set()
    for session in sessions:
        tuple_key = (session['client_ip'], session['client_port'], 
                    session['server_ip'], PGSQL_PORT)
        unique_tuples.add(tuple_key)
    
    print("负载均衡特性:")
    print(f"唯一四元组数量: {len(unique_tuples):,}")
    print(f"平均每个客户端IP的连接数: {len(sessions) / len(set([s['client_ip'] for s in sessions])):.1f}")
    print(f"平均每个服务器IP的连接数: {len(sessions) / len(set([s['server_ip'] for s in sessions])):.1f}")
    print(f"端口分布范围: {min([s['client_port'] for s in sessions])} - {max([s['client_port'] for s in sessions])}")
    
    # tcpreplay建议
    recommended_mbps = int(file_size_mb * 8 * 1.1)  # 加10%余量
    print("-" * 60)
    print("tcpreplay 使用建议:")
    print(f"tcpreplay -i eth0 -M {recommended_mbps} {filepath}")
    print(f"预期QPS: {target_qps:,}")
    print(f"建议带宽: {recommended_mbps} Mbps")
    print("负载均衡效果: 多IP多端口，可触发TCP负载均衡机制")

def main():
    """主函数"""
    print("PostgreSQL 高性能测试 PCAP 生成器")
    print("支持自定义QPS目标")
    print()
    
    # 获取用户输入
    try:
        qps_input = input("请输入目标QPS (默认100000): ").strip()
        if qps_input:
            target_qps = int(qps_input)
        else:
            target_qps = 100000
            
        if target_qps <= 0:
            print("QPS必须大于0")
            return
            
    except ValueError:
        print("无效的QPS值")
        return
    
    # 生成PCAP文件
    generate_performance_test_pcap(target_qps)

if __name__ == "__main__":
    main() 