#!/usr/bin/env python3
"""
PostgreSQL协议TCP网络丢包异常测试场景生成器

本模块专门用于生成各种TCP网络丢包情况下的PostgreSQL协议测试数据包，
用于验证协议解析器在网络异常情况下的鲁棒性和异常检测能力。

作者：PostgreSQL协议测试团队
日期：2024-01-15
版本：1.0

丢包测试场景分类：
1. 认证阶段关键消息丢失
2. 查询执行过程中的请求丢失  
3. 响应数据的部分丢失
4. 大数据传输中的分段丢失
5. 连接管理消息丢失
6. 错误处理消息丢失
7. 事务控制消息丢失
8. 复杂场景下的丢包组合
"""

import sys
import os
import random
import struct
from scapy.all import *
from scapy.layers.inet import IP, TCP

# 添加父目录到路径，以便导入原始模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入原始模块的核心函数和常量
from generate_pgsql_pcap import (
    create_pgsql_packet, create_segmented_pgsql_packets, 
    create_tcp_handshake, create_tcp_teardown,
    CLIENT_IP, SERVER_IP, PGSQL_PORT, MAX_TCP_PAYLOAD
)

# 丢包测试专用配置
LOST_OUTPUT_DIR = "pgsql_lost_pcaps"

# 丢包策略枚举
class PacketLossType:
    """数据包丢失类型枚举"""
    SINGLE_PACKET = "single_packet"      # 单个数据包丢失
    MULTIPLE_PACKETS = "multiple_packets" # 多个连续数据包丢失
    RANDOM_LOSS = "random_loss"          # 随机丢包
    SEGMENT_LOSS = "segment_loss"        # TCP分段丢失
    RESPONSE_LOSS = "response_loss"      # 响应消息丢失
    REQUEST_LOSS = "request_loss"        # 请求消息丢失

class PacketLossPosition:
    """数据包丢失位置枚举"""
    # 认证阶段
    STARTUP_MESSAGE = "startup_message"
    AUTH_REQUEST = "auth_request" 
    PASSWORD_RESPONSE = "password_response"
    AUTH_OK = "auth_ok"
    BACKEND_KEY = "backend_key"
    READY_FOR_QUERY = "ready_for_query"
    
    # 查询阶段
    QUERY_MESSAGE = "query_message"
    PARSE_MESSAGE = "parse_message"
    BIND_MESSAGE = "bind_message"
    EXECUTE_MESSAGE = "execute_message"
    SYNC_MESSAGE = "sync_message"
    
    # 响应阶段
    ROW_DESCRIPTION = "row_description"
    DATA_ROW = "data_row"
    COMMAND_COMPLETE = "command_complete"
    PARSE_COMPLETE = "parse_complete"
    BIND_COMPLETE = "bind_complete"
    
    # 连接管理
    CANCEL_REQUEST = "cancel_request"
    TERMINATE = "terminate"
    SSL_REQUEST = "ssl_request"
    
    # 错误处理
    ERROR_RESPONSE = "error_response"
    NOTICE_RESPONSE = "notice_response"

    # 大数据传输
    LARGE_QUERY_SEGMENT = "large_query_segment"
    LARGE_RESULT_SEGMENT = "large_result_segment"
    COPY_DATA_SEGMENT = "copy_data_segment"
    WAL_DATA_SEGMENT = "wal_data_segment"

    # 扩展查询协议
    DESCRIBE_MESSAGE = "describe_message"
    CLOSE_MESSAGE = "close_message"
    FLUSH_MESSAGE = "flush_message"

    # 高级功能
    SSL_RESPONSE = "ssl_response"
    FUNCTION_CALL = "function_call"
    FUNCTION_RESULT = "function_result"
    NOTIFICATION = "notification"
    COPY_IN_RESPONSE = "copy_in_response"
    COPY_OUT_RESPONSE = "copy_out_response"
    COPY_DONE = "copy_done"
    COPY_FAIL = "copy_fail"

def create_packet_loss_controller():
    """创建数据包丢失控制器

    Returns:
        dict: 丢包控制状态字典
    """
    return {
        'loss_positions': set(),  # 要丢失的数据包位置
        'loss_probability': 0.0,  # 随机丢包概率
        'current_position': None, # 当前数据包位置
        'packet_counter': 0,      # 数据包计数器
        'lost_packets': [],       # 已丢失的数据包记录
        'seq_adjustments': {}     # TCP序列号调整记录
    }

def create_tcp_segment_loss_controller(lost_segments=None):
    """创建TCP分段丢失控制器

    Args:
        lost_segments (list): 要丢失的分段编号列表，如[1]表示丢失第1段，[1,2]表示丢失第1和第2段
                             None表示不丢失任何段

    Returns:
        dict: TCP分段丢失控制状态字典
    """
    if lost_segments is None:
        lost_segments = []

    return {
        'lost_segments': set(lost_segments),  # 要丢失的分段编号集合
        'current_segment': 0,                 # 当前分段编号
        'total_segments': 3,                  # 总分段数（统一为3段）
        'lost_packets': [],                   # 已丢失的分段记录
        'segment_info': {}                    # 分段信息记录
    }

def should_drop_tcp_segment(controller, segment_number, segment_data=None):
    """判断是否应该丢弃当前TCP分段

    Args:
        controller (dict): TCP分段丢失控制器
        segment_number (int): 当前分段编号（1-based）
        segment_data (bytes): 分段数据

    Returns:
        bool: True表示应该丢弃，False表示正常发送
    """
    controller['current_segment'] = segment_number

    # 记录分段信息
    controller['segment_info'][segment_number] = {
        'size': len(segment_data) if segment_data else 0,
        'dropped': False
    }

    # 检查是否在指定丢失分段列表中
    if segment_number in controller['lost_segments']:
        controller['lost_packets'].append({
            'segment_number': segment_number,
            'data_size': len(segment_data) if segment_data else 0
        })
        controller['segment_info'][segment_number]['dropped'] = True
        return True

    return False

def split_message_into_segments(message_data, segment_count=3):
    """将消息数据分割为指定数量的TCP段

    Args:
        message_data (bytes): 要分割的消息数据
        segment_count (int): 分段数量，默认为3

    Returns:
        list: 分段数据列表
    """
    if segment_count <= 0:
        raise ValueError("分段数量必须大于0")

    if len(message_data) == 0:
        return [b""]

    # 计算每段的基础大小
    base_size = len(message_data) // segment_count
    remainder = len(message_data) % segment_count

    segments = []
    offset = 0

    for i in range(segment_count):
        # 前remainder个段多分配1字节
        segment_size = base_size + (1 if i < remainder else 0)

        if i == segment_count - 1:
            # 最后一段包含所有剩余数据
            segment = message_data[offset:]
        else:
            segment = message_data[offset:offset + segment_size]

        segments.append(segment)
        offset += segment_size

    return segments

def create_tcp_segmented_packets_with_loss(controller, src, dst, sport, dport, seq, ack,
                                         message_data, message_type="unknown", flags="PA"):
    """创建带丢失控制的TCP分段数据包序列

    Args:
        controller (dict): TCP分段丢失控制器
        src, dst, sport, dport: 网络参数
        seq, ack: TCP序列号和确认号
        message_data (bytes): 要分段的消息数据
        message_type (str): 消息类型描述
        flags (str): TCP标志

    Returns:
        tuple: (packets_list, final_seq) - 数据包列表和最终序列号
    """
    packets = []
    current_seq = seq

    # 分割消息为3段
    segments = split_message_into_segments(message_data, 3)

    print(f"[调试] {message_type}消息被分成 {len(segments)} 个TCP段:")
    for i, segment in enumerate(segments, 1):
        print(f"  - 第{i}段: {len(segment)} 字节")

    # 处理每个分段
    for i, segment in enumerate(segments, 1):
        should_drop = should_drop_tcp_segment(controller, i, segment)

        if should_drop:
            print(f"[丢包模拟] 丢弃{message_type}第 {i} 段，序列号: {current_seq}, 大小: {len(segment)} 字节")
        else:
            packet = create_pgsql_packet(src, dst, sport, dport, current_seq, ack, segment, flags)
            packets.append(packet)
            print(f"[调试] 发送{message_type}第 {i} 段，大小: {len(segment)} 字节")

        current_seq += len(segment)

    return packets, current_seq

def should_drop_packet(controller, position=None, packet_data=None):
    """判断是否应该丢弃当前数据包
    
    Args:
        controller (dict): 丢包控制器
        position (str): 当前数据包位置标识
        packet_data (bytes): 数据包内容
        
    Returns:
        bool: True表示应该丢弃，False表示正常发送
    """
    controller['packet_counter'] += 1
    controller['current_position'] = position
    
    # 检查是否在指定丢包位置
    if position and position in controller['loss_positions']:
        controller['lost_packets'].append({
            'position': position,
            'counter': controller['packet_counter'],
            'data_size': len(packet_data) if packet_data else 0
        })
        return True
    
    # 检查随机丢包
    if controller['loss_probability'] > 0:
        if random.random() < controller['loss_probability']:
            controller['lost_packets'].append({
                'position': position or 'random',
                'counter': controller['packet_counter'],
                'data_size': len(packet_data) if packet_data else 0
            })
            return True
    
    return False

def create_pgsql_packet_with_loss_control(controller, src, dst, sport, dport, seq, ack, payload, flags="PA", position=None):
    """创建带丢包控制的PostgreSQL数据包
    
    Args:
        controller (dict): 丢包控制器
        src, dst, sport, dport, seq, ack, payload, flags: 与原函数相同的参数
        position (str): 数据包位置标识
        
    Returns:
        tuple: (packet, should_add) - 数据包对象和是否应该添加到包列表的标志
    """
    # 检查是否应该丢弃此包
    if should_drop_packet(controller, position, payload):
        print(f"[丢包模拟] 丢弃数据包: {position}, 序列号: {seq}, 大小: {len(payload)} 字节")
        return None, False
    
    # 创建正常数据包
    packet = create_pgsql_packet(src, dst, sport, dport, seq, ack, payload, flags)
    return packet, True

def write_lost_pcap(packets, filename, controller=None):
    """写入丢包测试的PCAP文件

    Args:
        packets (list): 数据包列表
        filename (str): 文件名
        controller (dict, optional): 丢包控制器（已废弃，保留用于兼容性）
    """
    # 确保输出目录存在
    if not os.path.exists(LOST_OUTPUT_DIR):
        os.makedirs(LOST_OUTPUT_DIR)

    # 写入PCAP文件
    filepath = os.path.join(LOST_OUTPUT_DIR, filename)
    wrpcap(filepath, packets)

    print(f"已生成丢包测试文件: {filepath}")

def reset_global_counters():
    """重置全局计数器，为新的测试场景做准备"""
    global client_port, seq_num, ack_num
    client_port = random.randint(40000, 65000)
    seq_num = random.randint(1000000, 9000000)
    ack_num = random.randint(1000000, 9000000)

# datetime导入已移除（不再生成报告）

# 全局变量初始化
client_port = random.randint(40000, 65000)
seq_num = random.randint(1000000, 9000000)
ack_num = random.randint(1000000, 9000000)

# ============================================================================
# 通用完整流程生成函数
# ============================================================================

def create_complete_pgsql_flow():
    """创建完整的PostgreSQL请求-响应流程

    Returns:
        dict: 包含完整流程的所有消息包的字典
    """
    packets = []
    global seq_num, ack_num
    reset_global_counters()

    # 1. TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 2. 认证流程
    # StartupMessage
    startup_data = b"user\0postgres\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    startup_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    seq_num += len(startup_msg)

    # AuthenticationMD5Password
    auth_req = b"R" + struct.pack("!II", 12, 5) + b"salt"
    auth_req_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_req)
    ack_num += len(auth_req)

    # PasswordMessage
    password_data = b"md50123456789abcdef0123456789abcdef"
    password_resp = b"p" + struct.pack("!I", len(password_data) + 5) + password_data + b"\0"
    password_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, password_resp)
    seq_num += len(password_resp)

    # AuthenticationOk
    auth_ok_content = struct.pack("!I", 0)
    auth_ok = b"R" + struct.pack("!I", len(auth_ok_content) + 4) + auth_ok_content
    auth_ok_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_ok)
    ack_num += len(auth_ok)

    # BackendKeyData
    process_id = 12345
    secret_key = 67890
    backend_key_data = struct.pack("!II", process_id, secret_key)
    backend_key = b"K" + struct.pack("!I", len(backend_key_data) + 4) + backend_key_data
    backend_key_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, backend_key)
    ack_num += len(backend_key)

    # ReadyForQuery
    ready = b"Z" + struct.pack("!IB", 5, 73)  # 73 = 'I' (空闲状态)
    ready_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, ready)
    ack_num += len(ready)

    # 3. 查询流程
    # Query
    query_str = b"SELECT id, name FROM users WHERE id = 1;\0"
    query = b"Q" + struct.pack("!I", len(query_str) + 4) + query_str
    query_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, query)
    seq_num += len(query)

    # RowDescription
    row_desc_data = struct.pack("!H", 2)  # 2个字段
    row_desc_data += b"id\0" + struct.pack("!IHIhih", 0, 0, 23, 4, 4, 0)  # int4字段
    row_desc_data += b"name\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)  # text字段
    row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data
    row_desc_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, row_desc)
    ack_num += len(row_desc)

    # DataRow
    data_row_data = struct.pack("!HI", 2, 1) + b"1" + struct.pack("!I", 5) + b"Alice"
    data_row = b"D" + struct.pack("!I", len(data_row_data) + 4) + data_row_data
    data_row_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, data_row)
    ack_num += len(data_row)

    # CommandComplete
    cmd_complete_text = b"SELECT 1\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    cmd_complete_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, cmd_complete)
    ack_num += len(cmd_complete)

    # ReadyForQuery (final)
    ready_final = b"Z" + struct.pack("!IB", 5, 73)
    ready_final_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, ready_final)
    ack_num += len(ready_final)

    # 4. 连接关闭
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)

    return {
        'handshake': handshake_packets,
        'startup_message': startup_packet,
        'auth_request': auth_req_packet,
        'password_response': password_packet,
        'auth_ok': auth_ok_packet,
        'backend_key': backend_key_packet,
        'ready_for_query': ready_packet,
        'query_message': query_packet,
        'row_description': row_desc_packet,
        'data_row': data_row_packet,
        'command_complete': cmd_complete_packet,
        'ready_for_query_final': ready_final_packet,
        'teardown': teardown_packets,
        'seq_num': seq_num,
        'ack_num': ack_num,
        'client_port': client_port
    }

# ============================================================================
# 认证阶段丢包测试场景（重构版）
# ============================================================================

def generate_startup_message_lost_scenario():
    """生成StartupMessage丢失场景的数据包

    测试目的：模拟客户端启动消息在网络传输中丢失的情况，但保留完整的query-result流程
    预期结果：StartupMessage丢失，但包含完整的查询响应流程用于对比分析
    网络表现：完整流程中StartupMessage消息丢失
    """
    print("\n=== 生成StartupMessage丢失场景 ===")

    # 获取完整流程
    flow = create_complete_pgsql_flow()
    packets = []

    # 添加所有消息包，除了startup_message
    packets.extend(flow['handshake'])
    # 跳过 startup_message（丢失）
    packets.append(flow['auth_request'])
    packets.append(flow['password_response'])
    packets.append(flow['auth_ok'])
    packets.append(flow['backend_key'])
    packets.append(flow['ready_for_query'])
    packets.append(flow['query_message'])
    packets.append(flow['row_description'])
    packets.append(flow['data_row'])
    packets.append(flow['command_complete'])
    packets.append(flow['ready_for_query_final'])
    packets.extend(flow['teardown'])

    write_lost_pcap(packets, "pgsql_startup_message_lost.pcap")

def generate_auth_request_lost_scenario():
    """生成AuthenticationMD5Password丢失场景的数据包

    测试目的：模拟服务器认证请求消息在网络传输中丢失的情况，但保留完整的query-result流程
    预期结果：AuthenticationMD5Password丢失，但包含完整的查询响应流程用于对比分析
    网络表现：完整流程中AuthenticationMD5Password消息丢失
    """
    print("\n=== 生成AuthenticationMD5Password丢失场景 ===")

    # 获取完整流程
    flow = create_complete_pgsql_flow()
    packets = []

    # 添加所有消息包，除了auth_request
    packets.extend(flow['handshake'])
    packets.append(flow['startup_message'])
    # 跳过 auth_request（丢失）
    packets.append(flow['password_response'])
    packets.append(flow['auth_ok'])
    packets.append(flow['backend_key'])
    packets.append(flow['ready_for_query'])
    packets.append(flow['query_message'])
    packets.append(flow['row_description'])
    packets.append(flow['data_row'])
    packets.append(flow['command_complete'])
    packets.append(flow['ready_for_query_final'])
    packets.extend(flow['teardown'])

    write_lost_pcap(packets, "pgsql_auth_request_lost.pcap")

def generate_password_response_lost_scenario():
    """生成PasswordMessage丢失场景的数据包

    测试目的：模拟客户端密码响应消息在网络传输中丢失的情况，但保留完整的query-result流程
    预期结果：PasswordMessage丢失，但包含完整的查询响应流程用于对比分析
    网络表现：完整流程中PasswordMessage消息丢失
    """
    print("\n=== 生成PasswordMessage丢失场景 ===")

    # 获取完整流程
    flow = create_complete_pgsql_flow()
    packets = []

    # 添加所有消息包，除了password_response
    packets.extend(flow['handshake'])
    packets.append(flow['startup_message'])
    packets.append(flow['auth_request'])
    # 跳过 password_response（丢失）
    packets.append(flow['auth_ok'])
    packets.append(flow['backend_key'])
    packets.append(flow['ready_for_query'])
    packets.append(flow['query_message'])
    packets.append(flow['row_description'])
    packets.append(flow['data_row'])
    packets.append(flow['command_complete'])
    packets.append(flow['ready_for_query_final'])
    packets.extend(flow['teardown'])

    write_lost_pcap(packets, "pgsql_password_response_lost.pcap")

def generate_auth_ok_lost_scenario():
    """生成AuthenticationOk丢失场景的数据包

    测试目的：模拟服务器认证成功消息在网络传输中丢失的情况，但保留完整的query-result流程
    预期结果：AuthenticationOk丢失，但包含完整的查询响应流程用于对比分析
    网络表现：完整流程中AuthenticationOk消息丢失
    """
    print("\n=== 生成AuthenticationOk丢失场景 ===")

    # 获取完整流程
    flow = create_complete_pgsql_flow()
    packets = []

    # 添加所有消息包，除了auth_ok
    packets.extend(flow['handshake'])
    packets.append(flow['startup_message'])
    packets.append(flow['auth_request'])
    packets.append(flow['password_response'])
    # 跳过 auth_ok（丢失）
    packets.append(flow['backend_key'])
    packets.append(flow['ready_for_query'])
    packets.append(flow['query_message'])
    packets.append(flow['row_description'])
    packets.append(flow['data_row'])
    packets.append(flow['command_complete'])
    packets.append(flow['ready_for_query_final'])
    packets.extend(flow['teardown'])

    write_lost_pcap(packets, "pgsql_auth_ok_lost.pcap")

def generate_backend_key_lost_scenario():
    """生成BackendKeyData丢失场景的数据包

    测试目的：模拟服务器后端密钥数据在网络传输中丢失的情况，但保留完整的query-result流程
    预期结果：BackendKeyData丢失，但包含完整的查询响应流程用于对比分析
    网络表现：完整流程中BackendKeyData消息丢失
    """
    print("\n=== 生成BackendKeyData丢失场景 ===")

    # 获取完整流程
    flow = create_complete_pgsql_flow()
    packets = []

    # 添加所有消息包，除了backend_key
    packets.extend(flow['handshake'])
    packets.append(flow['startup_message'])
    packets.append(flow['auth_request'])
    packets.append(flow['password_response'])
    packets.append(flow['auth_ok'])
    # 跳过 backend_key（丢失）
    packets.append(flow['ready_for_query'])
    packets.append(flow['query_message'])
    packets.append(flow['row_description'])
    packets.append(flow['data_row'])
    packets.append(flow['command_complete'])
    packets.append(flow['ready_for_query_final'])
    packets.extend(flow['teardown'])

    write_lost_pcap(packets, "pgsql_backend_key_lost.pcap")

def generate_ready_for_query_lost_scenario():
    """生成ReadyForQuery丢失场景的数据包

    测试目的：模拟服务器就绪状态消息在网络传输中丢失的情况，但保留完整的query-result流程
    预期结果：ReadyForQuery丢失，但包含完整的查询响应流程用于对比分析
    网络表现：完整流程中ReadyForQuery消息丢失
    """
    print("\n=== 生成ReadyForQuery丢失场景 ===")

    # 获取完整流程
    flow = create_complete_pgsql_flow()
    packets = []

    # 添加所有消息包，除了ready_for_query
    packets.extend(flow['handshake'])
    packets.append(flow['startup_message'])
    packets.append(flow['auth_request'])
    packets.append(flow['password_response'])
    packets.append(flow['auth_ok'])
    packets.append(flow['backend_key'])
    # 跳过 ready_for_query（丢失）
    packets.append(flow['query_message'])
    packets.append(flow['row_description'])
    packets.append(flow['data_row'])
    packets.append(flow['command_complete'])
    packets.append(flow['ready_for_query_final'])
    packets.extend(flow['teardown'])

    write_lost_pcap(packets, "pgsql_ready_for_query_lost.pcap")

# ============================================================================
# 查询执行阶段丢包测试场景
# ============================================================================

def generate_query_message_lost_scenario():
    """生成Query消息丢失场景的数据包

    测试目的：模拟简单查询消息在网络传输中丢失的情况，但保留完整的认证流程和响应流程
    预期结果：Query消息丢失，但包含完整的认证和响应流程用于对比分析
    网络表现：完整流程中Query消息丢失
    """
    print("\n=== 生成Query消息丢失场景 ===")

    # 获取完整流程
    flow = create_complete_pgsql_flow()
    packets = []

    # 添加所有消息包，除了query_message
    packets.extend(flow['handshake'])
    packets.append(flow['startup_message'])
    packets.append(flow['auth_request'])
    packets.append(flow['password_response'])
    packets.append(flow['auth_ok'])
    packets.append(flow['backend_key'])
    packets.append(flow['ready_for_query'])
    # 跳过 query_message（丢失）
    packets.append(flow['row_description'])
    packets.append(flow['data_row'])
    packets.append(flow['command_complete'])
    packets.append(flow['ready_for_query_final'])
    packets.extend(flow['teardown'])

    write_lost_pcap(packets, "pgsql_query_message_lost.pcap")

def create_complete_extended_query_flow():
    """创建完整的扩展查询协议流程

    Returns:
        dict: 包含完整扩展查询流程的所有消息包的字典
    """
    packets = []
    global seq_num, ack_num
    reset_global_counters()

    # 1. TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 2. 认证流程（简化）
    startup_data = b"user\0postgres\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    startup_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    seq_num += len(startup_msg)

    # 简化认证响应（AuthenticationOk + ReadyForQuery）
    auth_ok = b"R" + struct.pack("!II", 8, 0)
    ready = b"Z" + struct.pack("!IB", 5, 73)
    auth_resp = auth_ok + ready
    auth_resp_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_resp)
    ack_num += len(auth_resp)

    # 3. 扩展查询协议流程
    # Parse消息
    parse_data = b"stmt1\0" + b"SELECT * FROM users WHERE id = $1;\0" + struct.pack("!H", 1) + struct.pack("!I", 23)
    parse_msg = b"P" + struct.pack("!I", len(parse_data) + 4) + parse_data
    parse_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, parse_msg)
    seq_num += len(parse_msg)

    # Bind消息
    bind_data = b"portal1\0stmt1\0" + struct.pack("!HHI", 1, 0, 1) + b"1" + struct.pack("!H", 0)
    bind_msg = b"B" + struct.pack("!I", len(bind_data) + 4) + bind_data
    bind_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind_msg)
    seq_num += len(bind_msg)

    # Execute消息
    execute_data = b"portal1\0" + struct.pack("!I", 0)
    execute_msg = b"E" + struct.pack("!I", len(execute_data) + 4) + execute_data
    execute_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, execute_msg)
    seq_num += len(execute_msg)

    # Sync消息
    sync_msg = b"S" + struct.pack("!I", 4)
    sync_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sync_msg)
    seq_num += len(sync_msg)

    # 4. 服务器响应序列
    # ParseComplete
    parse_complete = b"1" + struct.pack("!I", 4)
    parse_complete_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, parse_complete)
    ack_num += len(parse_complete)

    # BindComplete
    bind_complete = b"2" + struct.pack("!I", 4)
    bind_complete_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, bind_complete)
    ack_num += len(bind_complete)

    # RowDescription
    row_desc_data = struct.pack("!H", 2)  # 2个字段
    row_desc_data += b"id\0" + struct.pack("!IHIhih", 0, 0, 23, 4, 4, 0)
    row_desc_data += b"name\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data
    row_desc_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, row_desc)
    ack_num += len(row_desc)

    # DataRow
    data_row_data = struct.pack("!HI", 2, 1) + b"1" + struct.pack("!I", 5) + b"Alice"
    data_row = b"D" + struct.pack("!I", len(data_row_data) + 4) + data_row_data
    data_row_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, data_row)
    ack_num += len(data_row)

    # CommandComplete
    cmd_complete_text = b"SELECT 1\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    cmd_complete_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, cmd_complete)
    ack_num += len(cmd_complete)

    # ReadyForQuery (final)
    ready_final = b"Z" + struct.pack("!IB", 5, 73)
    ready_final_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, ready_final)
    ack_num += len(ready_final)

    # 5. 连接关闭
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)

    return {
        'handshake': handshake_packets,
        'startup_and_auth': [startup_packet, auth_resp_packet],
        'parse_message': parse_packet,
        'bind_message': bind_packet,
        'execute_message': execute_packet,
        'sync_message': sync_packet,
        'parse_complete': parse_complete_packet,
        'bind_complete': bind_complete_packet,
        'row_description': row_desc_packet,
        'data_row': data_row_packet,
        'command_complete': cmd_complete_packet,
        'ready_for_query_final': ready_final_packet,
        'teardown': teardown_packets,
        'seq_num': seq_num,
        'ack_num': ack_num
    }

def generate_parse_message_lost_scenario():
    """生成Parse消息丢失场景的数据包

    测试目的：模拟扩展查询协议中Parse消息丢失的情况，但保留完整的扩展查询流程
    预期结果：Parse消息丢失，但包含完整的扩展查询流程用于对比分析
    网络表现：完整扩展查询流程中Parse消息丢失
    """
    print("\n=== 生成Parse消息丢失场景 ===")

    # 获取完整扩展查询流程
    flow = create_complete_extended_query_flow()
    packets = []

    # 添加所有消息包，除了parse_message
    packets.extend(flow['handshake'])
    packets.extend(flow['startup_and_auth'])
    # 跳过 parse_message（丢失）
    packets.append(flow['bind_message'])
    packets.append(flow['execute_message'])
    packets.append(flow['sync_message'])
    packets.append(flow['parse_complete'])
    packets.append(flow['bind_complete'])
    packets.append(flow['row_description'])
    packets.append(flow['data_row'])
    packets.append(flow['command_complete'])
    packets.append(flow['ready_for_query_final'])
    packets.extend(flow['teardown'])

    write_lost_pcap(packets, "pgsql_parse_message_lost.pcap")

def generate_bind_message_lost_scenario():
    """生成Bind消息丢失场景的数据包

    测试目的：模拟扩展查询协议中Bind消息丢失的情况，但保留完整的扩展查询流程
    预期结果：Bind消息丢失，但包含完整的扩展查询流程用于对比分析
    网络表现：完整扩展查询流程中Bind消息丢失
    """
    print("\n=== 生成Bind消息丢失场景 ===")

    # 获取完整扩展查询流程
    flow = create_complete_extended_query_flow()
    packets = []

    # 添加所有消息包，除了bind_message
    packets.extend(flow['handshake'])
    packets.extend(flow['startup_and_auth'])
    packets.append(flow['parse_message'])
    # 跳过 bind_message（丢失）
    packets.append(flow['execute_message'])
    packets.append(flow['sync_message'])
    packets.append(flow['parse_complete'])
    packets.append(flow['bind_complete'])
    packets.append(flow['row_description'])
    packets.append(flow['data_row'])
    packets.append(flow['command_complete'])
    packets.append(flow['ready_for_query_final'])
    packets.extend(flow['teardown'])

    write_lost_pcap(packets, "pgsql_bind_message_lost.pcap")

def generate_execute_message_lost_scenario():
    """生成Execute消息丢失场景的数据包

    测试目的：模拟扩展查询协议中Execute消息丢失的情况，但保留完整的扩展查询流程
    预期结果：Execute消息丢失，但包含完整的扩展查询流程用于对比分析
    网络表现：完整扩展查询流程中Execute消息丢失
    """
    print("\n=== 生成Execute消息丢失场景 ===")

    # 获取完整扩展查询流程
    flow = create_complete_extended_query_flow()
    packets = []

    # 添加所有消息包，除了execute_message
    packets.extend(flow['handshake'])
    packets.extend(flow['startup_and_auth'])
    packets.append(flow['parse_message'])
    packets.append(flow['bind_message'])
    # 跳过 execute_message（丢失）
    packets.append(flow['sync_message'])
    packets.append(flow['parse_complete'])
    packets.append(flow['bind_complete'])
    packets.append(flow['row_description'])
    packets.append(flow['data_row'])
    packets.append(flow['command_complete'])
    packets.append(flow['ready_for_query_final'])
    packets.extend(flow['teardown'])

    write_lost_pcap(packets, "pgsql_execute_message_lost.pcap")

def generate_sync_message_lost_scenario():
    """生成Sync消息丢失场景的数据包

    测试目的：模拟扩展查询协议中Sync消息丢失的情况，但保留完整的扩展查询流程
    预期结果：Sync消息丢失，但包含完整的扩展查询流程用于对比分析
    网络表现：完整扩展查询流程中Sync消息丢失
    """
    print("\n=== 生成Sync消息丢失场景 ===")

    # 获取完整扩展查询流程
    flow = create_complete_extended_query_flow()
    packets = []

    # 添加所有消息包，除了sync_message
    packets.extend(flow['handshake'])
    packets.extend(flow['startup_and_auth'])
    packets.append(flow['parse_message'])
    packets.append(flow['bind_message'])
    packets.append(flow['execute_message'])
    # 跳过 sync_message（丢失）
    packets.append(flow['parse_complete'])
    packets.append(flow['bind_complete'])
    packets.append(flow['row_description'])
    packets.append(flow['data_row'])
    packets.append(flow['command_complete'])
    packets.append(flow['ready_for_query_final'])
    packets.extend(flow['teardown'])

    write_lost_pcap(packets, "pgsql_sync_message_lost.pcap")

def generate_large_query_segment_lost_scenario():
    """生成大查询TCP分段丢失场景的数据包

    测试目的：模拟大型查询在TCP分段传输中某个分段丢失的情况
    预期结果：查询数据不完整，服务器无法正确解析
    网络表现：大查询被分成多个TCP段，其中一个段丢失
    """
    print("\n=== 生成大查询TCP分段丢失场景 ===")

    packets = []
    controller = create_packet_loss_controller()
    controller['loss_positions'].add(PacketLossPosition.LARGE_QUERY_SEGMENT)

    global seq_num, ack_num
    reset_global_counters()

    # 1. 建立连接和认证（简化）
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    startup_data = b"user\0postgres\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(c_packet)
    seq_num += len(startup_msg)

    auth_resp = b"R" + struct.pack("!II", 8, 0) + b"Z" + struct.pack("!IB", 5, 73)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_resp)
    packets.append(s_packet)
    ack_num += len(auth_resp)

    # 2. 构造大型查询（超过MTU，需要分段）
    # 创建一个包含大量数据的INSERT语句
    large_values = []
    for i in range(100):
        large_values.append(f"({i}, 'user_{i}', 'This is a very long description for user {i} with lots of text to make the query large enough to require TCP segmentation')")

    large_query = f"INSERT INTO users (id, name, description) VALUES {', '.join(large_values)};\0"
    large_query_bytes = large_query.encode('utf-8')

    # 创建Query消息
    query_msg = b"Q" + struct.pack("!I", len(large_query_bytes) + 4) + large_query_bytes

    # 3. 模拟大查询分段传输，第二段丢失
    # 将大查询分成两个段
    segment1_size = 1000
    segment1_data = query_msg[:segment1_size]
    segment2_data = query_msg[segment1_size:]

    # 发送第一段（正常）
    segment1 = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, segment1_data)
    packets.append(segment1)
    seq_num += len(segment1_data)

    # 第二段（丢失）
    packet, should_add = create_pgsql_packet_with_loss_control(
        controller, CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT,
        seq_num, ack_num, segment2_data, "PA", PacketLossPosition.LARGE_QUERY_SEGMENT
    )

    if should_add:
        packets.append(packet)
    seq_num += len(segment2_data)

    seq_num += len(query_msg)

    # 5. 服务器可能发送错误响应（数据不完整）
    error_content = b"SERROR\0C08P01\0Mincomplete message\0\0"
    error_resp = b"E" + struct.pack("!I", len(error_content) + 4) + error_content
    ready = b"Z" + struct.pack("!IB", 5, 73)
    server_resp = error_resp + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_lost_pcap(packets, "pgsql_large_query_segment_lost.pcap")

def generate_ssl_request_lost_scenario():
    """生成SSL请求丢失场景的数据包

    测试目的：模拟SSL协商请求在网络传输中丢失的情况
    预期结果：无法建立SSL连接，可能回退到非SSL连接或失败
    网络表现：SSL请求消息丢失，服务器无响应
    """
    print("\n=== 生成SSL请求丢失场景 ===")

    packets = []
    controller = create_packet_loss_controller()
    controller['loss_positions'].add(PacketLossPosition.SSL_REQUEST)

    global seq_num, ack_num
    reset_global_counters()

    # 1. TCP三次握手
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 2. SSL请求（丢失）
    # SSL请求的格式：长度(4字节) + SSL请求码(4字节，值为80877103)
    ssl_request = struct.pack("!II", 8, 80877103)

    packet, should_add = create_pgsql_packet_with_loss_control(
        controller, CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT,
        seq_num, ack_num, ssl_request, "PA", PacketLossPosition.SSL_REQUEST
    )

    if should_add:
        packets.append(packet)
    seq_num += len(ssl_request)

    # 3. 客户端等待SSL响应超时后，可能发送普通StartupMessage
    startup_data = b"user\0postgres\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(c_packet)
    seq_num += len(startup_msg)

    # 4. 服务器正常响应认证请求
    auth_req = b"R" + struct.pack("!II", 12, 5) + b"salt"
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_req)
    packets.append(s_packet)
    ack_num += len(auth_req)

    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_lost_pcap(packets, "pgsql_ssl_request_lost.pcap")

def generate_copy_data_lost_scenario():
    """生成COPY数据丢失场景的数据包

    测试目的：模拟COPY操作中数据传输丢失的情况
    预期结果：COPY操作不完整，数据导入失败
    网络表现：COPY命令成功，但部分CopyData消息丢失
    """
    print("\n=== 生成COPY数据丢失场景 ===")

    packets = []
    controller = create_packet_loss_controller()
    controller['loss_positions'].add(PacketLossPosition.COPY_DATA_SEGMENT)

    global seq_num, ack_num
    reset_global_counters()

    # 1. 建立连接和认证（简化）
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    startup_data = b"user\0postgres\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(c_packet)
    seq_num += len(startup_msg)

    auth_resp = b"R" + struct.pack("!II", 8, 0) + b"Z" + struct.pack("!IB", 5, 73)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_resp)
    packets.append(s_packet)
    ack_num += len(auth_resp)

    # 2. 发送COPY命令
    copy_query = b"COPY users FROM STDIN WITH CSV;\0"
    copy_cmd = b"Q" + struct.pack("!I", len(copy_query) + 4) + copy_query
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_cmd)
    packets.append(c_packet)
    seq_num += len(copy_cmd)

    # 3. 服务器响应CopyInResponse
    copy_in_data = struct.pack("!BH", 0, 3)  # 文本格式，3个字段
    copy_in_data += struct.pack("!HHH", 0, 0, 0)  # 字段格式代码
    copy_in_resp = b"G" + struct.pack("!I", len(copy_in_data) + 4) + copy_in_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, copy_in_resp)
    packets.append(s_packet)
    ack_num += len(copy_in_resp)

    # 4. 客户端发送COPY数据（第一行正常）
    copy_data1 = b"1,Alice,Engineer\n"
    copy_msg1 = b"d" + struct.pack("!I", len(copy_data1) + 4) + copy_data1
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_msg1)
    packets.append(c_packet)
    seq_num += len(copy_msg1)

    # 5. 客户端发送COPY数据（第二行丢失）
    copy_data2 = b"2,Bob,Manager\n"
    copy_msg2 = b"d" + struct.pack("!I", len(copy_data2) + 4) + copy_data2

    packet, should_add = create_pgsql_packet_with_loss_control(
        controller, CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT,
        seq_num, ack_num, copy_msg2, "PA", PacketLossPosition.COPY_DATA_SEGMENT
    )

    if should_add:
        packets.append(packet)
    seq_num += len(copy_msg2)

    # 6. 客户端发送COPY数据（第三行正常）
    copy_data3 = b"3,Charlie,Developer\n"
    copy_msg3 = b"d" + struct.pack("!I", len(copy_data3) + 4) + copy_data3
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_msg3)
    packets.append(c_packet)
    seq_num += len(copy_msg3)

    # 7. 客户端发送CopyDone
    copy_done = b"c" + struct.pack("!I", 4)
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, copy_done)
    packets.append(c_packet)
    seq_num += len(copy_done)

    # 8. 服务器响应CommandComplete（可能显示不完整的行数）
    cmd_complete_text = b"COPY 2\0"  # 只有2行成功，因为第二行丢失
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    ready = b"Z" + struct.pack("!IB", 5, 73)
    server_resp = cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, server_resp)
    packets.append(s_packet)
    ack_num += len(server_resp)

    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_lost_pcap(packets, "pgsql_copy_data_lost.pcap")

def generate_extended_query_large_parse_segment_lost_scenario(lost_segments=None):
    """生成扩展查询协议大Parse消息TCP分段丢失场景的数据包

    测试目的：模拟扩展查询协议中包含大SQL查询的Parse消息在TCP分段传输中指定分段丢失的情况
    预期结果：Parse消息数据不完整，服务器无法正确解析预处理语句，后续Bind/Execute失败
    网络表现：大Parse消息被分成3个TCP段，指定的段丢失，其他P/D/B/E/S消息正常传输
    协议特性：完整的扩展查询协议序列 P(Parse) -> D(Describe) -> B(Bind) -> E(Execute) -> S(Sync)
    分段策略：统一采用三段分割模式，与其他场景保持一致

    Args:
        lost_segments (list): 要丢失的分段编号列表，如[1], [2], [3], [1,2], [2,3], [1,2,3]
                             默认为[3]（丢失第3段，保持向后兼容）
    """
    if lost_segments is None:
        lost_segments = [3]  # 默认丢失第3段，保持向后兼容
    lost_segments_str = "_".join(map(str, sorted(lost_segments)))
    print(f"\n=== 生成扩展查询协议大Parse消息TCP分段丢失场景 (丢失第{lost_segments_str}段) ===")

    packets = []
    segment_controller = create_tcp_segment_loss_controller(lost_segments)

    global seq_num, ack_num
    reset_global_counters()

    # 1. 建立连接和认证（简化）
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    startup_data = b"user\0postgres\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(c_packet)
    seq_num += len(startup_msg)

    auth_resp = b"R" + struct.pack("!II", 8, 0) + b"Z" + struct.pack("!IB", 5, 73)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_resp)
    packets.append(s_packet)
    ack_num += len(auth_resp)

    # 2. 构造大型SQL查询（约2000字节，确保需要TCP分段）
    # 创建一个复杂的SELECT查询，包含大量字段和条件
    large_sql_parts = [
        "SELECT u.id, u.username, u.email, u.first_name, u.last_name, u.created_at, u.updated_at,",
        "       p.profile_id, p.bio, p.avatar_url, p.phone_number, p.address, p.city, p.country,",
        "       o.order_id, o.order_date, o.total_amount, o.status, o.shipping_address,",
        "       oi.item_id, oi.product_name, oi.quantity, oi.unit_price, oi.total_price,",
        "       c.category_id, c.category_name, c.description as category_desc,",
        "       r.review_id, r.rating, r.comment, r.review_date",
        "FROM users u",
        "LEFT JOIN user_profiles p ON u.id = p.user_id",
        "LEFT JOIN orders o ON u.id = o.user_id",
        "LEFT JOIN order_items oi ON o.order_id = oi.order_id",
        "LEFT JOIN categories c ON oi.category_id = c.category_id",
        "LEFT JOIN reviews r ON oi.item_id = r.item_id AND u.id = r.user_id",
        "WHERE u.created_at >= $1 AND u.created_at <= $2",
        "  AND (u.email LIKE $3 OR u.username LIKE $4)",
        "  AND p.country IN ($5, $6, $7, $8, $9)",
        "  AND o.status IN ('pending', 'processing', 'shipped', 'delivered')",
        "  AND oi.unit_price BETWEEN $10 AND $11",
        "  AND r.rating >= $12",
        "ORDER BY u.created_at DESC, o.order_date DESC, r.rating DESC",
        "LIMIT $13 OFFSET $14"
    ]

    large_sql = " ".join(large_sql_parts) + ";\0"
    large_sql_bytes = large_sql.encode('utf-8')

    # 确保查询足够大（如果不够大，添加注释）
    while len(large_sql_bytes) < 2000:
        comment = f" /* 这是一个用于测试TCP分段的长注释，用于增加查询大小以确保需要多个TCP段传输。注释编号：{len(large_sql_bytes)} */ "
        large_sql = large_sql[:-2] + comment + large_sql[-2:]  # 在\0前插入注释
        large_sql_bytes = large_sql.encode('utf-8')

    print(f"[调试] 大SQL查询大小: {len(large_sql_bytes)} 字节")

    # 3. 构造Parse消息
    stmt_name = b"large_stmt\0"
    param_count = struct.pack("!H", 14)  # 14个参数
    # 参数类型OID：timestamp, timestamp, text, text, text, text, text, text, text, numeric, numeric, integer, integer, integer
    param_types = struct.pack("!14I", 1114, 1114, 25, 25, 25, 25, 25, 25, 25, 1700, 1700, 23, 23, 23)

    parse_data = stmt_name + large_sql_bytes + param_count + param_types
    parse_msg = b"P" + struct.pack("!I", len(parse_data) + 4) + parse_data

    print(f"[调试] Parse消息总大小: {len(parse_msg)} 字节")

    # 4. 构造后续的扩展查询消息（Describe、Bind、Execute、Sync）
    # 4.1 Describe消息
    describe_data = b"S" + b"large_stmt\0"  # 描述预处理语句
    describe_msg = b"D" + struct.pack("!I", len(describe_data) + 4) + describe_data
    print(f"[调试] 构造Describe消息，大小: {len(describe_msg)} 字节")

    # 4.2 Bind消息
    portal_name = b"large_portal\0"
    stmt_name_ref = b"large_stmt\0"
    param_format_count = struct.pack("!H", 14)  # 14个参数格式
    param_formats = struct.pack("!14H", *([0] * 14))  # 所有参数都是文本格式
    param_value_count = struct.pack("!H", 14)  # 14个参数值

    # 构造参数值（示例数据）
    param_values = b""
    param_data = [
        b"2023-01-01 00:00:00",  # $1: start_date
        b"2023-12-31 23:59:59",  # $2: end_date
        b"%@example.com",        # $3: email_pattern
        b"user_%",               # $4: username_pattern
        b"USA",                  # $5: country1
        b"Canada",               # $6: country2
        b"UK",                   # $7: country3
        b"Germany",              # $8: country4
        b"France",               # $9: country5
        b"10.00",                # $10: min_price
        b"1000.00",              # $11: max_price
        b"4",                    # $12: min_rating
        b"100",                  # $13: limit
        b"0"                     # $14: offset
    ]

    for param in param_data:
        param_values += struct.pack("!I", len(param)) + param

    result_format_count = struct.pack("!H", 0)  # 使用默认格式

    bind_data = portal_name + stmt_name_ref + param_format_count + param_formats + param_value_count + param_values + result_format_count
    bind_msg = b"B" + struct.pack("!I", len(bind_data) + 4) + bind_data
    print(f"[调试] 构造Bind消息，大小: {len(bind_msg)} 字节")

    # 4.3 Execute消息
    execute_data = b"large_portal\0" + struct.pack("!I", 0)  # 执行所有行
    execute_msg = b"E" + struct.pack("!I", len(execute_data) + 4) + execute_data
    print(f"[调试] 构造Execute消息，大小: {len(execute_msg)} 字节")

    # 4.4 Sync消息
    sync_msg = b"S" + struct.pack("!I", 4)
    print(f"[调试] 构造Sync消息，大小: {len(sync_msg)} 字节")

    # 5. 实现新的分段策略：Parse消息前两段独立，第3段与后续消息合并
    # 5.1 将Parse消息分为3段
    segments = split_message_into_segments(parse_msg, 3)
    print(f"[调试] Parse消息被分成 3 个TCP段:")
    for i, segment in enumerate(segments, 1):
        print(f"  - 第{i}段: {len(segment)} 字节")

    # 5.2 发送Parse消息的前两段
    for i in range(2):  # 发送第1段和第2段
        segment_number = i + 1
        should_drop = should_drop_tcp_segment(segment_controller, segment_number, segments[i])

        if should_drop:
            print(f"[丢包模拟] 丢弃Parse第 {segment_number} 段，序列号: {seq_num}, 大小: {len(segments[i])} 字节")
        else:
            packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, segments[i])
            packets.append(packet)
            print(f"[调试] 发送Parse第 {segment_number} 段，大小: {len(segments[i])} 字节")

        seq_num += len(segments[i])

    # 5.3 将Parse消息第3段与后续消息合并为一个TCP分段
    combined_segment = segments[2] + describe_msg + bind_msg + execute_msg + sync_msg
    print(f"[调试] 合并第3段：Parse第3段({len(segments[2])}字节) + Describe({len(describe_msg)}字节) + Bind({len(bind_msg)}字节) + Execute({len(execute_msg)}字节) + Sync({len(sync_msg)}字节) = {len(combined_segment)}字节")

    # 5.4 处理合并后的第3段（可能丢失）
    should_drop = should_drop_tcp_segment(segment_controller, 3, combined_segment)

    if should_drop:
        print(f"[丢包模拟] 丢弃合并的第 3 段（Parse尾部+扩展查询消息），序列号: {seq_num}, 大小: {len(combined_segment)} 字节")
    else:
        packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, combined_segment)
        packets.append(packet)
        print(f"[调试] 发送合并的第 3 段（Parse尾部+扩展查询消息），大小: {len(combined_segment)} 字节")

    seq_num += len(combined_segment)

    # 6. 服务器响应序列（正常响应，因为服务器实际收到了完整请求）
    # 虽然抓包时合并的第3段可能丢失，但服务器端实际收到了完整的请求
    # 这种情况模拟的是网络监控点的丢包，而不是真实的端到端丢包

    # 6.1 ParseComplete
    parse_complete = b"1" + struct.pack("!I", 4)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, parse_complete)
    packets.append(s_packet)
    ack_num += len(parse_complete)
    print(f"[调试] 服务器发送ParseComplete，大小: {len(parse_complete)} 字节")

    # 6.2 ParameterDescription
    param_desc_data = struct.pack("!H", 14)  # 14个参数
    # 参数类型OID：timestamp, timestamp, text, text, text, text, text, text, text, numeric, numeric, integer, integer, integer
    param_desc_data += struct.pack("!14I", 1114, 1114, 25, 25, 25, 25, 25, 25, 25, 1700, 1700, 23, 23, 23)
    param_desc = b"t" + struct.pack("!I", len(param_desc_data) + 4) + param_desc_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, param_desc)
    packets.append(s_packet)
    ack_num += len(param_desc)
    print(f"[调试] 服务器发送ParameterDescription，大小: {len(param_desc)} 字节")

    # 6.3 RowDescription
    row_desc_data = struct.pack("!H", 6)  # 6个字段

    # 字段1: user_id (integer)
    row_desc_data += b"user_id\0" + struct.pack("!IHIhih", 0, 0, 23, 4, -1, 0)
    # 字段2: username (text)
    row_desc_data += b"username\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    # 字段3: email (text)
    row_desc_data += b"email\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    # 字段4: order_count (bigint)
    row_desc_data += b"order_count\0" + struct.pack("!IHIhih", 0, 0, 20, 8, -1, 0)
    # 字段5: total_amount (numeric)
    row_desc_data += b"total_amount\0" + struct.pack("!IHIhih", 0, 0, 1700, -1, -1, 0)
    # 字段6: avg_rating (numeric)
    row_desc_data += b"avg_rating\0" + struct.pack("!IHIhih", 0, 0, 1700, -1, -1, 0)

    row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, row_desc)
    packets.append(s_packet)
    ack_num += len(row_desc)
    print(f"[调试] 服务器发送RowDescription，大小: {len(row_desc)} 字节")

    # 6.4 BindComplete
    bind_complete = b"2" + struct.pack("!I", 4)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, bind_complete)
    packets.append(s_packet)
    ack_num += len(bind_complete)
    print(f"[调试] 服务器发送BindComplete，大小: {len(bind_complete)} 字节")

    # 6.5 DataRow (模拟查询结果数据)
    # 第一行数据
    data_row1_data = struct.pack("!H", 6)  # 6个字段
    # user_id: 1001
    field1 = b"1001"
    data_row1_data += struct.pack("!I", len(field1)) + field1
    # username: john_doe
    field2 = b"john_doe"
    data_row1_data += struct.pack("!I", len(field2)) + field2
    # email: <EMAIL>
    field3 = b"<EMAIL>"
    data_row1_data += struct.pack("!I", len(field3)) + field3
    # order_count: 15
    field4 = b"15"
    data_row1_data += struct.pack("!I", len(field4)) + field4
    # total_amount: 2567.89
    field5 = b"2567.89"
    data_row1_data += struct.pack("!I", len(field5)) + field5
    # avg_rating: 4.5
    field6 = b"4.5"
    data_row1_data += struct.pack("!I", len(field6)) + field6

    data_row1 = b"D" + struct.pack("!I", len(data_row1_data) + 4) + data_row1_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, data_row1)
    packets.append(s_packet)
    ack_num += len(data_row1)
    print(f"[调试] 服务器发送DataRow 1，大小: {len(data_row1)} 字节")

    # 第二行数据
    data_row2_data = struct.pack("!H", 6)  # 6个字段
    # user_id: 1002
    field1 = b"1002"
    data_row2_data += struct.pack("!I", len(field1)) + field1
    # username: jane_smith
    field2 = b"jane_smith"
    data_row2_data += struct.pack("!I", len(field2)) + field2
    # email: <EMAIL>
    field3 = b"<EMAIL>"
    data_row2_data += struct.pack("!I", len(field3)) + field3
    # order_count: 8
    field4 = b"8"
    data_row2_data += struct.pack("!I", len(field4)) + field4
    # total_amount: 1234.56
    field5 = b"1234.56"
    data_row2_data += struct.pack("!I", len(field5)) + field5
    # avg_rating: 4.8
    field6 = b"4.8"
    data_row2_data += struct.pack("!I", len(field6)) + field6

    data_row2 = b"D" + struct.pack("!I", len(data_row2_data) + 4) + data_row2_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, data_row2)
    packets.append(s_packet)
    ack_num += len(data_row2)
    print(f"[调试] 服务器发送DataRow 2，大小: {len(data_row2)} 字节")

    # 6.6 CommandComplete
    cmd_complete_text = b"SELECT 2\0"  # 返回2行数据
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, cmd_complete)
    packets.append(s_packet)
    ack_num += len(cmd_complete)
    print(f"[调试] 服务器发送CommandComplete，大小: {len(cmd_complete)} 字节")

    # 6.7 ReadyForQuery
    ready = b"Z" + struct.pack("!IB", 5, 73)  # 73 = 'I' (空闲状态)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, ready)
    packets.append(s_packet)
    ack_num += len(ready)
    print(f"[调试] 服务器发送ReadyForQuery，大小: {len(ready)} 字节")

    # 7. 连接关闭
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    # 生成文件
    filename = f"pgsql_extended_query_large_parse_segments_{lost_segments_str}_lost.pcap"
    write_lost_pcap(packets, filename)
    print(f"[完成] 扩展查询协议大Parse消息TCP分段丢失场景生成完成 (丢失第{lost_segments_str}段)")

def generate_extended_query_multiple_large_parse_segment_lost_scenario():
    """生成扩展查询协议多个大Parse消息TCP分段丢失场景的数据包

    测试目的：模拟在同一个PostgreSQL会话中连续执行两个扩展查询协议请求，
             每个请求都包含大型Parse消息并发生TCP分段丢包的情况
    预期结果：两个Parse消息的最后分段都丢失，但服务器正常处理并返回完整结果集
    网络表现：在单个TCP连接中，两个大Parse消息的最后TCP段都丢失，其他消息正常传输
    协议特性：两个完整的扩展查询协议序列 P/D/B/E/S -> 响应 -> P/D/B/E/S -> 响应
    丢包类型：抓包丢包（非真实网络丢包），服务器实际收到完整请求
    """
    print("\n=== 生成扩展查询协议多个大Parse消息TCP分段丢失场景 ===")

    packets = []
    controller = create_packet_loss_controller()
    # 添加三个丢包位置标识符：第一个查询的最后分段，第二个查询的第2和第3分段
    controller['loss_positions'].add("large_parse_last_segment_1")
    controller['loss_positions'].add("large_parse_segment_2_2")  # 第二个查询的第2段
    controller['loss_positions'].add("large_parse_segment_3_2")  # 第二个查询的第3段

    global seq_num, ack_num
    reset_global_counters()

    # 1. 建立连接和认证（简化）
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    startup_data = b"user\0postgres\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(c_packet)
    seq_num += len(startup_msg)

    auth_resp = b"R" + struct.pack("!II", 8, 0) + b"Z" + struct.pack("!IB", 5, 73)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_resp)
    packets.append(s_packet)
    ack_num += len(auth_resp)

    print(f"[调试] 连接建立完成，开始第一个扩展查询")

    # ========================================================================
    # 第一个扩展查询协议序列
    # ========================================================================

    # 2. 构造第一个大型SQL查询（约2000字节）
    large_sql_parts_1 = [
        "SELECT u.id, u.username, u.email, u.first_name, u.last_name, u.created_at, u.updated_at,",
        "       p.profile_id, p.bio, p.avatar_url, p.phone_number, p.address, p.city, p.country,",
        "       o.order_id, o.order_date, o.total_amount, o.status, o.shipping_address,",
        "       oi.item_id, oi.product_name, oi.quantity, oi.unit_price, oi.total_price,",
        "       c.category_id, c.category_name, c.description as category_desc",
        "FROM users u",
        "LEFT JOIN user_profiles p ON u.id = p.user_id",
        "LEFT JOIN orders o ON u.id = o.user_id",
        "LEFT JOIN order_items oi ON o.order_id = oi.order_id",
        "LEFT JOIN categories c ON oi.category_id = c.category_id",
        "WHERE u.created_at >= $1 AND u.created_at <= $2",
        "  AND (u.email LIKE $3 OR u.username LIKE $4)",
        "  AND p.country IN ($5, $6, $7)",
        "  AND o.status IN ('pending', 'processing', 'shipped')",
        "  AND oi.unit_price BETWEEN $8 AND $9",
        "ORDER BY u.created_at DESC, o.order_date DESC",
        "LIMIT $10 OFFSET $11"
    ]

    large_sql_1 = " ".join(large_sql_parts_1) + ";\0"
    large_sql_bytes_1 = large_sql_1.encode('utf-8')

    # 确保查询足够大
    while len(large_sql_bytes_1) < 2000:
        comment = f" /* 第一个查询的测试注释，用于增加查询大小以确保TCP分段。注释编号：{len(large_sql_bytes_1)} */ "
        large_sql_1 = large_sql_1[:-2] + comment + large_sql_1[-2:]
        large_sql_bytes_1 = large_sql_1.encode('utf-8')

    print(f"[调试] 第一个大SQL查询大小: {len(large_sql_bytes_1)} 字节")

    # 3. 构造第一个Parse消息
    stmt_name_1 = b"large_stmt_1\0"
    param_count_1 = struct.pack("!H", 11)  # 11个参数
    param_types_1 = struct.pack("!11I", 1114, 1114, 25, 25, 25, 25, 25, 1700, 1700, 23, 23)

    parse_data_1 = stmt_name_1 + large_sql_bytes_1 + param_count_1 + param_types_1
    parse_msg_1 = b"P" + struct.pack("!I", len(parse_data_1) + 4) + parse_data_1

    print(f"[调试] 第一个Parse消息总大小: {len(parse_msg_1)} 字节")

    # 4. 模拟第一个Parse消息TCP分段传输，最后一个分段丢失
    segment_size = 1200
    segments_1 = []

    for i in range(0, len(parse_msg_1), segment_size):
        segment = parse_msg_1[i:i + segment_size]
        segments_1.append(segment)

    print(f"[调试] 第一个Parse消息被分成 {len(segments_1)} 个TCP段")

    # 发送除最后一个段之外的所有段（正常）
    for i, segment in enumerate(segments_1[:-1]):
        segment_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, segment)
        packets.append(segment_packet)
        seq_num += len(segment)
        print(f"[调试] 发送第一个Parse消息第 {i+1} 段，大小: {len(segment)} 字节")

    # 最后一个段（丢失）
    last_segment_1 = segments_1[-1]
    packet, should_add = create_pgsql_packet_with_loss_control(
        controller, CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT,
        seq_num, ack_num, last_segment_1, "PA", "large_parse_last_segment_1"
    )

    if should_add:
        packets.append(packet)
    seq_num += len(last_segment_1)
    print(f"[调试] 第一个Parse消息最后一段丢失，大小: {len(last_segment_1)} 字节")

    # 5. 发送第一个查询的D/B/E/S消息序列
    # 5.1 Describe消息
    describe_data_1 = b"S" + b"large_stmt_1\0"
    describe_msg_1 = b"D" + struct.pack("!I", len(describe_data_1) + 4) + describe_data_1
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, describe_msg_1)
    packets.append(c_packet)
    seq_num += len(describe_msg_1)
    print(f"[调试] 发送第一个Describe消息，大小: {len(describe_msg_1)} 字节")

    # 5.2 Bind消息
    portal_name_1 = b"large_portal_1\0"
    stmt_name_ref_1 = b"large_stmt_1\0"
    param_format_count_1 = struct.pack("!H", 11)
    param_formats_1 = struct.pack("!11H", *([0] * 11))
    param_value_count_1 = struct.pack("!H", 11)

    param_data_1 = [
        b"2023-01-01 00:00:00", b"2023-12-31 23:59:59", b"%@company.com", b"admin_%",
        b"USA", b"Canada", b"UK", b"100.00", b"5000.00", b"50", b"0"
    ]

    param_values_1 = b""
    for param in param_data_1:
        param_values_1 += struct.pack("!I", len(param)) + param

    result_format_count_1 = struct.pack("!H", 0)
    bind_data_1 = portal_name_1 + stmt_name_ref_1 + param_format_count_1 + param_formats_1 + param_value_count_1 + param_values_1 + result_format_count_1
    bind_msg_1 = b"B" + struct.pack("!I", len(bind_data_1) + 4) + bind_data_1
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind_msg_1)
    packets.append(c_packet)
    seq_num += len(bind_msg_1)
    print(f"[调试] 发送第一个Bind消息，大小: {len(bind_msg_1)} 字节")

    # 5.3 Execute消息
    execute_data_1 = b"large_portal_1\0" + struct.pack("!I", 0)
    execute_msg_1 = b"E" + struct.pack("!I", len(execute_data_1) + 4) + execute_data_1
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, execute_msg_1)
    packets.append(c_packet)
    seq_num += len(execute_msg_1)
    print(f"[调试] 发送第一个Execute消息，大小: {len(execute_msg_1)} 字节")

    # 5.4 Sync消息
    sync_msg_1 = b"S" + struct.pack("!I", 4)
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sync_msg_1)
    packets.append(c_packet)
    seq_num += len(sync_msg_1)
    print(f"[调试] 发送第一个Sync消息，大小: {len(sync_msg_1)} 字节")

    # 6. 第一个查询的服务器响应序列（1/t/T/2/D/C/Z）
    # 6.1 ParseComplete
    parse_complete_1 = b"1" + struct.pack("!I", 4)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, parse_complete_1)
    packets.append(s_packet)
    ack_num += len(parse_complete_1)
    print(f"[调试] 服务器发送第一个ParseComplete，大小: {len(parse_complete_1)} 字节")

    # 6.2 ParameterDescription
    param_desc_data_1 = struct.pack("!H", 11) + struct.pack("!11I", 1114, 1114, 25, 25, 25, 25, 25, 1700, 1700, 23, 23)
    param_desc_1 = b"t" + struct.pack("!I", len(param_desc_data_1) + 4) + param_desc_data_1
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, param_desc_1)
    packets.append(s_packet)
    ack_num += len(param_desc_1)
    print(f"[调试] 服务器发送第一个ParameterDescription，大小: {len(param_desc_1)} 字节")

    # 6.3 RowDescription
    row_desc_data_1 = struct.pack("!H", 5)  # 5个字段
    row_desc_data_1 += b"user_id\0" + struct.pack("!IHIhih", 0, 0, 23, 4, -1, 0)
    row_desc_data_1 += b"username\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    row_desc_data_1 += b"email\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    row_desc_data_1 += b"country\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    row_desc_data_1 += b"total_orders\0" + struct.pack("!IHIhih", 0, 0, 20, 8, -1, 0)

    row_desc_1 = b"T" + struct.pack("!I", len(row_desc_data_1) + 4) + row_desc_data_1
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, row_desc_1)
    packets.append(s_packet)
    ack_num += len(row_desc_1)
    print(f"[调试] 服务器发送第一个RowDescription，大小: {len(row_desc_1)} 字节")

    # 6.4 BindComplete
    bind_complete_1 = b"2" + struct.pack("!I", 4)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, bind_complete_1)
    packets.append(s_packet)
    ack_num += len(bind_complete_1)
    print(f"[调试] 服务器发送第一个BindComplete，大小: {len(bind_complete_1)} 字节")

    # 6.5 DataRow
    data_row1_1_data = struct.pack("!H", 5)  # 5个字段
    fields_1 = [b"2001", b"admin_user", b"<EMAIL>", b"USA", b"25"]
    for field in fields_1:
        data_row1_1_data += struct.pack("!I", len(field)) + field

    data_row1_1 = b"D" + struct.pack("!I", len(data_row1_1_data) + 4) + data_row1_1_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, data_row1_1)
    packets.append(s_packet)
    ack_num += len(data_row1_1)
    print(f"[调试] 服务器发送第一个查询DataRow，大小: {len(data_row1_1)} 字节")

    # 6.6 CommandComplete
    cmd_complete_1_text = b"SELECT 1\0"
    cmd_complete_1 = b"C" + struct.pack("!I", len(cmd_complete_1_text) + 4) + cmd_complete_1_text
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, cmd_complete_1)
    packets.append(s_packet)
    ack_num += len(cmd_complete_1)
    print(f"[调试] 服务器发送第一个CommandComplete，大小: {len(cmd_complete_1)} 字节")

    # 6.7 ReadyForQuery
    ready_1 = b"Z" + struct.pack("!IB", 5, 73)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, ready_1)
    packets.append(s_packet)
    ack_num += len(ready_1)
    print(f"[调试] 服务器发送第一个ReadyForQuery，大小: {len(ready_1)} 字节")

    print(f"[调试] 第一个扩展查询完成，开始第二个扩展查询")

    # ========================================================================
    # 第二个扩展查询协议序列
    # ========================================================================

    # 7. 构造第二个大型SQL查询（约1800字节，与第一个不同）
    large_sql_parts_2 = [
        "SELECT p.product_id, p.product_name, p.description, p.price, p.stock_quantity,",
        "       p.category_id, p.brand, p.weight, p.dimensions, p.color,",
        "       c.category_name, c.parent_category_id,",
        "       s.supplier_id, s.supplier_name, s.contact_email, s.phone,",
        "       i.inventory_id, i.warehouse_location, i.last_updated,",
        "       r.review_count, r.average_rating, r.total_reviews",
        "FROM products p",
        "LEFT JOIN categories c ON p.category_id = c.category_id",
        "LEFT JOIN suppliers s ON p.supplier_id = s.supplier_id",
        "LEFT JOIN inventory i ON p.product_id = i.product_id",
        "LEFT JOIN (",
        "    SELECT product_id, COUNT(*) as review_count, AVG(rating) as average_rating,",
        "           COUNT(*) as total_reviews FROM reviews GROUP BY product_id",
        ") r ON p.product_id = r.product_id",
        "WHERE p.price BETWEEN $1 AND $2",
        "  AND p.stock_quantity > $3",
        "  AND c.category_name LIKE $4",
        "  AND s.supplier_name IN ($5, $6, $7, $8)",
        "  AND i.warehouse_location = $9",
        "  AND r.average_rating >= $10",
        "ORDER BY p.price ASC, r.average_rating DESC",
        "LIMIT $11 OFFSET $12"
    ]

    large_sql_2 = " ".join(large_sql_parts_2) + ";\0"
    large_sql_bytes_2 = large_sql_2.encode('utf-8')

    # 确保查询足够大
    while len(large_sql_bytes_2) < 1800:
        comment = f" /* 第二个查询的测试注释，用于增加查询大小。注释编号：{len(large_sql_bytes_2)} */ "
        large_sql_2 = large_sql_2[:-2] + comment + large_sql_2[-2:]
        large_sql_bytes_2 = large_sql_2.encode('utf-8')

    print(f"[调试] 第二个大SQL查询大小: {len(large_sql_bytes_2)} 字节")

    # 8. 构造第二个Parse消息
    stmt_name_2 = b"large_stmt_2\0"
    param_count_2 = struct.pack("!H", 12)  # 12个参数
    param_types_2 = struct.pack("!12I", 1700, 1700, 23, 25, 25, 25, 25, 25, 25, 1700, 23, 23)

    parse_data_2 = stmt_name_2 + large_sql_bytes_2 + param_count_2 + param_types_2
    parse_msg_2 = b"P" + struct.pack("!I", len(parse_data_2) + 4) + parse_data_2

    print(f"[调试] 第二个Parse消息总大小: {len(parse_msg_2)} 字节")

    # 9. 模拟第二个Parse消息TCP分段传输，后两个分段丢失
    # 第二个查询使用更小的分段大小（600字节），以产生更多分段
    segment_size_2 = 600  # 第二个查询专用的分段大小
    segments_2 = []

    for i in range(0, len(parse_msg_2), segment_size_2):
        segment = parse_msg_2[i:i + segment_size_2]
        segments_2.append(segment)

    print(f"[调试] 第二个Parse消息被分成 {len(segments_2)} 个TCP段（每段{segment_size_2}字节）")

    # 只发送第一个段（正常），后两个段都丢失
    if len(segments_2) > 0:
        first_segment_2 = segments_2[0]
        segment_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, first_segment_2)
        packets.append(segment_packet)
        seq_num += len(first_segment_2)
        print(f"[调试] 发送第二个Parse消息第 1 段，大小: {len(first_segment_2)} 字节")

    # 第二个段（丢失）
    if len(segments_2) > 1:
        second_segment_2 = segments_2[1]
        packet, should_add = create_pgsql_packet_with_loss_control(
            controller, CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT,
            seq_num, ack_num, second_segment_2, "PA", "large_parse_segment_2_2"
        )

        if should_add:
            packets.append(packet)
        seq_num += len(second_segment_2)
        print(f"[调试] 第二个Parse消息第 2 段丢失，大小: {len(second_segment_2)} 字节")

    # 第三个段（丢失）
    if len(segments_2) > 2:
        third_segment_2 = segments_2[2]
        packet, should_add = create_pgsql_packet_with_loss_control(
            controller, CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT,
            seq_num, ack_num, third_segment_2, "PA", "large_parse_segment_3_2"
        )

        if should_add:
            packets.append(packet)
        seq_num += len(third_segment_2)
        print(f"[调试] 第二个Parse消息第 3 段丢失，大小: {len(third_segment_2)} 字节")

    # 10. 发送第二个查询的D/B/E/S消息序列
    # 10.1 Describe消息
    describe_data_2 = b"S" + b"large_stmt_2\0"
    describe_msg_2 = b"D" + struct.pack("!I", len(describe_data_2) + 4) + describe_data_2
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, describe_msg_2)
    packets.append(c_packet)
    seq_num += len(describe_msg_2)
    print(f"[调试] 发送第二个Describe消息，大小: {len(describe_msg_2)} 字节")

    # 10.2 Bind消息
    portal_name_2 = b"large_portal_2\0"
    stmt_name_ref_2 = b"large_stmt_2\0"
    param_format_count_2 = struct.pack("!H", 12)
    param_formats_2 = struct.pack("!12H", *([0] * 12))
    param_value_count_2 = struct.pack("!H", 12)

    param_data_2 = [
        b"50.00", b"500.00", b"10", b"Electronics%", b"TechCorp", b"GadgetInc",
        b"DeviceSupply", b"ComponentCo", b"Warehouse_A", b"4.0", b"20", b"0"
    ]

    param_values_2 = b""
    for param in param_data_2:
        param_values_2 += struct.pack("!I", len(param)) + param

    result_format_count_2 = struct.pack("!H", 0)
    bind_data_2 = portal_name_2 + stmt_name_ref_2 + param_format_count_2 + param_formats_2 + param_value_count_2 + param_values_2 + result_format_count_2
    bind_msg_2 = b"B" + struct.pack("!I", len(bind_data_2) + 4) + bind_data_2
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind_msg_2)
    packets.append(c_packet)
    seq_num += len(bind_msg_2)
    print(f"[调试] 发送第二个Bind消息，大小: {len(bind_msg_2)} 字节")

    # 10.3 Execute消息
    execute_data_2 = b"large_portal_2\0" + struct.pack("!I", 0)
    execute_msg_2 = b"E" + struct.pack("!I", len(execute_data_2) + 4) + execute_data_2
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, execute_msg_2)
    packets.append(c_packet)
    seq_num += len(execute_msg_2)
    print(f"[调试] 发送第二个Execute消息，大小: {len(execute_msg_2)} 字节")

    # 10.4 Sync消息
    sync_msg_2 = b"S" + struct.pack("!I", 4)
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sync_msg_2)
    packets.append(c_packet)
    seq_num += len(sync_msg_2)
    print(f"[调试] 发送第二个Sync消息，大小: {len(sync_msg_2)} 字节")

    # 11. 第二个查询的服务器响应序列（1/t/T/2/D/C/Z）
    # 11.1 ParseComplete
    parse_complete_2 = b"1" + struct.pack("!I", 4)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, parse_complete_2)
    packets.append(s_packet)
    ack_num += len(parse_complete_2)
    print(f"[调试] 服务器发送第二个ParseComplete，大小: {len(parse_complete_2)} 字节")

    # 11.2 ParameterDescription
    param_desc_data_2 = struct.pack("!H", 12) + struct.pack("!12I", 1700, 1700, 23, 25, 25, 25, 25, 25, 25, 1700, 23, 23)
    param_desc_2 = b"t" + struct.pack("!I", len(param_desc_data_2) + 4) + param_desc_data_2
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, param_desc_2)
    packets.append(s_packet)
    ack_num += len(param_desc_2)
    print(f"[调试] 服务器发送第二个ParameterDescription，大小: {len(param_desc_2)} 字节")

    # 11.3 RowDescription
    row_desc_data_2 = struct.pack("!H", 4)  # 4个字段
    row_desc_data_2 += b"product_id\0" + struct.pack("!IHIhih", 0, 0, 23, 4, -1, 0)
    row_desc_data_2 += b"product_name\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    row_desc_data_2 += b"price\0" + struct.pack("!IHIhih", 0, 0, 1700, -1, -1, 0)
    row_desc_data_2 += b"avg_rating\0" + struct.pack("!IHIhih", 0, 0, 1700, -1, -1, 0)

    row_desc_2 = b"T" + struct.pack("!I", len(row_desc_data_2) + 4) + row_desc_data_2
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, row_desc_2)
    packets.append(s_packet)
    ack_num += len(row_desc_2)
    print(f"[调试] 服务器发送第二个RowDescription，大小: {len(row_desc_2)} 字节")

    # 11.4 BindComplete
    bind_complete_2 = b"2" + struct.pack("!I", 4)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, bind_complete_2)
    packets.append(s_packet)
    ack_num += len(bind_complete_2)
    print(f"[调试] 服务器发送第二个BindComplete，大小: {len(bind_complete_2)} 字节")

    # 11.5 DataRow (两行数据)
    # 第一行
    data_row2_1_data = struct.pack("!H", 4)  # 4个字段
    fields_2_1 = [b"3001", b"Wireless Headphones", b"299.99", b"4.5"]
    for field in fields_2_1:
        data_row2_1_data += struct.pack("!I", len(field)) + field

    data_row2_1 = b"D" + struct.pack("!I", len(data_row2_1_data) + 4) + data_row2_1_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, data_row2_1)
    packets.append(s_packet)
    ack_num += len(data_row2_1)
    print(f"[调试] 服务器发送第二个查询DataRow 1，大小: {len(data_row2_1)} 字节")

    # 第二行
    data_row2_2_data = struct.pack("!H", 4)  # 4个字段
    fields_2_2 = [b"3002", b"Gaming Mouse", b"89.99", b"4.8"]
    for field in fields_2_2:
        data_row2_2_data += struct.pack("!I", len(field)) + field

    data_row2_2 = b"D" + struct.pack("!I", len(data_row2_2_data) + 4) + data_row2_2_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, data_row2_2)
    packets.append(s_packet)
    ack_num += len(data_row2_2)
    print(f"[调试] 服务器发送第二个查询DataRow 2，大小: {len(data_row2_2)} 字节")

    # 11.6 CommandComplete
    cmd_complete_2_text = b"SELECT 2\0"
    cmd_complete_2 = b"C" + struct.pack("!I", len(cmd_complete_2_text) + 4) + cmd_complete_2_text
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, cmd_complete_2)
    packets.append(s_packet)
    ack_num += len(cmd_complete_2)
    print(f"[调试] 服务器发送第二个CommandComplete，大小: {len(cmd_complete_2)} 字节")

    # 11.7 ReadyForQuery
    ready_2 = b"Z" + struct.pack("!IB", 5, 73)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, ready_2)
    packets.append(s_packet)
    ack_num += len(ready_2)
    print(f"[调试] 服务器发送第二个ReadyForQuery，大小: {len(ready_2)} 字节")

    print(f"[调试] 第二个扩展查询完成")

    # 12. 连接关闭
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_lost_pcap(packets, "pgsql_extended_query_multiple_large_parse_segment_lost.pcap")
    print(f"[完成] 扩展查询协议多个大Parse消息TCP分段丢失场景生成完成")

def generate_simple_query_large_message_segment_lost_scenario(lost_segments=None):
    """生成简单查询协议大型Query消息TCP分段丢失场景的数据包

    测试目的：模拟简单查询协议中包含大型SQL查询的Query消息在TCP分段传输中指定分段丢失的情况
    预期结果：Query消息数据不完整，但服务器实际收到完整请求并正常响应结果集
    网络表现：大Query消息被分成3个TCP段，指定的段丢失，但服务器正常处理
    协议特性：简单查询协议 Q(Query) -> 服务器响应 T/D/C/Z 序列
    丢包类型：抓包丢包（非真实网络丢包），服务器实际收到完整请求

    Args:
        lost_segments (list): 要丢失的分段编号列表，如[1], [2], [3], [1,2], [2,3], [1,2,3]
                             默认为[3]（丢失第3段，保持向后兼容）

    与扩展查询协议的区别：
    - 使用简单查询协议（Single 'Q' message）而不是扩展查询协议（P/D/B/E/S sequence）
    - 直接发送SQL查询文本，无需预处理语句和参数绑定
    - 服务器响应更简单：T -> D -> C -> Z
    """
    if lost_segments is None:
        lost_segments = [3]  # 默认丢失第3段，保持向后兼容
    lost_segments_str = "_".join(map(str, sorted(lost_segments)))
    print(f"\n=== 生成简单查询协议大型Query消息TCP分段丢失场景 (丢失第{lost_segments_str}段) ===")

    packets = []
    segment_controller = create_tcp_segment_loss_controller(lost_segments)

    global seq_num, ack_num
    reset_global_counters()

    # 1. 建立连接和认证（简化）
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    startup_data = b"user\0postgres\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(c_packet)
    seq_num += len(startup_msg)

    auth_resp = b"R" + struct.pack("!II", 8, 0) + b"Z" + struct.pack("!IB", 5, 73)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_resp)
    packets.append(s_packet)
    ack_num += len(auth_resp)

    print(f"[调试] 连接建立完成，开始构造大型简单查询")

    # 2. 构造大型SQL查询（约2000字节，使用简单查询协议）
    # 创建一个复杂的SELECT查询，包含大量字段、表连接和条件
    large_sql_parts = [
        "SELECT u.user_id, u.username, u.email, u.first_name, u.last_name, u.phone_number,",
        "       u.created_at, u.updated_at, u.last_login_at, u.is_active, u.email_verified,",
        "       p.profile_id, p.bio, p.avatar_url, p.birth_date, p.gender, p.location,",
        "       p.website, p.social_media_links, p.preferences, p.privacy_settings,",
        "       a.address_id, a.street_address, a.city, a.state, a.postal_code, a.country,",
        "       a.address_type, a.is_primary, a.latitude, a.longitude,",
        "       o.order_id, o.order_number, o.order_date, o.total_amount, o.currency,",
        "       o.order_status, o.payment_status, o.shipping_method, o.tracking_number,",
        "       oi.item_id, oi.product_name, oi.product_sku, oi.quantity, oi.unit_price,",
        "       oi.total_price, oi.discount_amount, oi.tax_amount,",
        "       c.category_id, c.category_name, c.parent_category_id, c.category_level,",
        "       c.description as category_description, c.image_url as category_image,",
        "       r.review_id, r.rating, r.review_title, r.review_text, r.review_date,",
        "       r.helpful_votes, r.verified_purchase, r.reviewer_name",
        "FROM users u",
        "LEFT JOIN user_profiles p ON u.user_id = p.user_id",
        "LEFT JOIN user_addresses a ON u.user_id = a.user_id AND a.is_primary = true",
        "LEFT JOIN orders o ON u.user_id = o.user_id",
        "LEFT JOIN order_items oi ON o.order_id = oi.order_id",
        "LEFT JOIN product_categories c ON oi.category_id = c.category_id",
        "LEFT JOIN product_reviews r ON oi.product_id = r.product_id AND u.user_id = r.user_id",
        "WHERE u.created_at >= '2023-01-01 00:00:00' AND u.created_at <= '2023-12-31 23:59:59'",
        "  AND u.is_active = true AND u.email_verified = true",
        "  AND (u.email LIKE '%@company.com' OR u.email LIKE '%@enterprise.org')",
        "  AND p.privacy_settings->>'public_profile' = 'true'",
        "  AND a.country IN ('USA', 'Canada', 'UK', 'Germany', 'France', 'Japan')",
        "  AND o.order_status IN ('completed', 'shipped', 'delivered')",
        "  AND o.total_amount BETWEEN 100.00 AND 5000.00",
        "  AND oi.quantity > 0 AND oi.unit_price > 0",
        "  AND c.category_level <= 3",
        "  AND r.rating >= 4 AND r.verified_purchase = true",
        "ORDER BY u.created_at DESC, o.order_date DESC, r.rating DESC, oi.total_price DESC",
        "LIMIT 100 OFFSET 0"
    ]

    large_sql = " ".join(large_sql_parts) + ";\0"
    large_sql_bytes = large_sql.encode('utf-8')

    # 确保查询足够大（如果不够大，添加注释）
    while len(large_sql_bytes) < 2000:
        comment = f" /* 简单查询协议测试注释，用于增加查询大小以确保需要多个TCP段传输。注释编号：{len(large_sql_bytes)} */ "
        large_sql = large_sql[:-2] + comment + large_sql[-2:]  # 在\0前插入注释
        large_sql_bytes = large_sql.encode('utf-8')

    print(f"[调试] 大型SQL查询大小: {len(large_sql_bytes)} 字节")

    # 3. 构造简单查询协议的Query消息
    query_msg = b"Q" + struct.pack("!I", len(large_sql_bytes) + 4) + large_sql_bytes

    print(f"[调试] Query消息总大小: {len(query_msg)} 字节")

    # 4. 使用新的统一TCP分段丢失控制器发送Query消息
    query_packets, seq_num = create_tcp_segmented_packets_with_loss(
        segment_controller, CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT,
        seq_num, ack_num, query_msg, "Query"
    )
    packets.extend(query_packets)

    # 5. 服务器响应序列（正常响应，因为服务器实际收到了完整请求）
    # 虽然抓包时Query消息的最后分段丢失，但服务器端实际收到了完整的请求
    # 这种情况模拟的是网络监控点的丢包，而不是真实的端到端丢包

    # 5.1 RowDescription
    row_desc_data = struct.pack("!H", 6)  # 6个字段

    # 字段1: user_id (integer)
    row_desc_data += b"user_id\0" + struct.pack("!IHIhih", 0, 0, 23, 4, -1, 0)
    # 字段2: username (text)
    row_desc_data += b"username\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    # 字段3: email (text)
    row_desc_data += b"email\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    # 字段4: total_orders (bigint)
    row_desc_data += b"total_orders\0" + struct.pack("!IHIhih", 0, 0, 20, 8, -1, 0)
    # 字段5: total_amount (numeric)
    row_desc_data += b"total_amount\0" + struct.pack("!IHIhih", 0, 0, 1700, -1, -1, 0)
    # 字段6: avg_rating (numeric)
    row_desc_data += b"avg_rating\0" + struct.pack("!IHIhih", 0, 0, 1700, -1, -1, 0)

    row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, row_desc)
    packets.append(s_packet)
    ack_num += len(row_desc)
    print(f"[调试] 服务器发送RowDescription，大小: {len(row_desc)} 字节")

    # 5.2 DataRow (模拟查询结果数据)
    # 第一行数据
    data_row1_data = struct.pack("!H", 6)  # 6个字段
    # user_id: 1001
    field1 = b"1001"
    data_row1_data += struct.pack("!I", len(field1)) + field1
    # username: john_enterprise
    field2 = b"john_enterprise"
    data_row1_data += struct.pack("!I", len(field2)) + field2
    # email: <EMAIL>
    field3 = b"<EMAIL>"
    data_row1_data += struct.pack("!I", len(field3)) + field3
    # total_orders: 25
    field4 = b"25"
    data_row1_data += struct.pack("!I", len(field4)) + field4
    # total_amount: 12567.89
    field5 = b"12567.89"
    data_row1_data += struct.pack("!I", len(field5)) + field5
    # avg_rating: 4.7
    field6 = b"4.7"
    data_row1_data += struct.pack("!I", len(field6)) + field6

    data_row1 = b"D" + struct.pack("!I", len(data_row1_data) + 4) + data_row1_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, data_row1)
    packets.append(s_packet)
    ack_num += len(data_row1)
    print(f"[调试] 服务器发送DataRow 1，大小: {len(data_row1)} 字节")

    # 第二行数据
    data_row2_data = struct.pack("!H", 6)  # 6个字段
    # user_id: 1002
    field1 = b"1002"
    data_row2_data += struct.pack("!I", len(field1)) + field1
    # username: alice_corp
    field2 = b"alice_corp"
    data_row2_data += struct.pack("!I", len(field2)) + field2
    # email: <EMAIL>
    field3 = b"<EMAIL>"
    data_row2_data += struct.pack("!I", len(field3)) + field3
    # total_orders: 18
    field4 = b"18"
    data_row2_data += struct.pack("!I", len(field4)) + field4
    # total_amount: 8934.56
    field5 = b"8934.56"
    data_row2_data += struct.pack("!I", len(field5)) + field5
    # avg_rating: 4.9
    field6 = b"4.9"
    data_row2_data += struct.pack("!I", len(field6)) + field6

    data_row2 = b"D" + struct.pack("!I", len(data_row2_data) + 4) + data_row2_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, data_row2)
    packets.append(s_packet)
    ack_num += len(data_row2)
    print(f"[调试] 服务器发送DataRow 2，大小: {len(data_row2)} 字节")

    # 第三行数据
    data_row3_data = struct.pack("!H", 6)  # 6个字段
    # user_id: 1003
    field1 = b"1003"
    data_row3_data += struct.pack("!I", len(field1)) + field1
    # username: bob_company
    field2 = b"bob_company"
    data_row3_data += struct.pack("!I", len(field2)) + field2
    # email: <EMAIL>
    field3 = b"<EMAIL>"
    data_row3_data += struct.pack("!I", len(field3)) + field3
    # total_orders: 32
    field4 = b"32"
    data_row3_data += struct.pack("!I", len(field4)) + field4
    # total_amount: 15678.23
    field5 = b"15678.23"
    data_row3_data += struct.pack("!I", len(field5)) + field5
    # avg_rating: 4.6
    field6 = b"4.6"
    data_row3_data += struct.pack("!I", len(field6)) + field6

    data_row3 = b"D" + struct.pack("!I", len(data_row3_data) + 4) + data_row3_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, data_row3)
    packets.append(s_packet)
    ack_num += len(data_row3)
    print(f"[调试] 服务器发送DataRow 3，大小: {len(data_row3)} 字节")

    # 5.3 CommandComplete
    cmd_complete_text = b"SELECT 3\0"  # 返回3行数据
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, cmd_complete)
    packets.append(s_packet)
    ack_num += len(cmd_complete)
    print(f"[调试] 服务器发送CommandComplete，大小: {len(cmd_complete)} 字节")

    # 5.4 ReadyForQuery
    ready = b"Z" + struct.pack("!IB", 5, 73)  # 73 = 'I' (空闲状态)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, ready)
    packets.append(s_packet)
    ack_num += len(ready)
    print(f"[调试] 服务器发送ReadyForQuery，大小: {len(ready)} 字节")

    # 6. 连接关闭
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    # 生成文件
    filename = f"pgsql_simple_query_large_message_segments_{lost_segments_str}_lost.pcap"
    write_lost_pcap(packets, filename)
    print(f"[完成] 简单查询协议大型Query消息TCP分段丢失场景生成完成 (丢失第{lost_segments_str}段)")

# ============================================================================
# 第三阶段：连接管理、错误处理、事务控制场景
# ============================================================================

def generate_cancel_request_lost_scenario():
    """生成CancelRequest丢失场景的数据包

    测试目的：模拟查询取消请求在网络传输中丢失的情况
    预期结果：无法取消正在执行的查询，查询继续运行
    网络表现：CancelRequest消息丢失，长时间查询无法被中断
    """
    print("\n=== 生成CancelRequest丢失场景 ===")

    packets = []
    controller = create_packet_loss_controller()
    controller['loss_positions'].add(PacketLossPosition.CANCEL_REQUEST)

    global seq_num, ack_num
    reset_global_counters()

    # 1. 建立连接和认证（简化）
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    startup_data = b"user\0postgres\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(c_packet)
    seq_num += len(startup_msg)

    # 包含BackendKeyData的认证响应
    auth_ok = b"R" + struct.pack("!II", 8, 0)
    backend_key_data = struct.pack("!II", 12345, 67890)  # process_id, secret_key
    backend_key = b"K" + struct.pack("!I", len(backend_key_data) + 4) + backend_key_data
    ready = b"Z" + struct.pack("!IB", 5, 73)
    auth_resp = auth_ok + backend_key + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_resp)
    packets.append(s_packet)
    ack_num += len(auth_resp)

    # 2. 客户端发送长时间查询
    long_query = b"SELECT pg_sleep(30);\0"  # 30秒的长查询
    query = b"Q" + struct.pack("!I", len(long_query) + 4) + long_query
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, query)
    packets.append(c_packet)
    seq_num += len(query)

    # 3. 客户端尝试取消查询（CancelRequest丢失）
    # CancelRequest使用新的TCP连接
    cancel_port = client_port + 1
    cancel_handshake, cancel_seq, cancel_ack = create_tcp_handshake(CLIENT_IP, SERVER_IP, cancel_port, PGSQL_PORT)
    packets.extend(cancel_handshake)

    # CancelRequest消息格式：长度(4) + 取消码(4) + 进程ID(4) + 密钥(4)
    cancel_request = struct.pack("!IIII", 16, 80877102, 12345, 67890)

    packet, should_add = create_pgsql_packet_with_loss_control(
        controller, CLIENT_IP, SERVER_IP, cancel_port, PGSQL_PORT,
        cancel_seq, cancel_ack, cancel_request, "PA", PacketLossPosition.CANCEL_REQUEST
    )

    if should_add:
        packets.append(packet)

    # 取消连接立即关闭
    cancel_teardown = create_tcp_teardown(CLIENT_IP, SERVER_IP, cancel_port, PGSQL_PORT, cancel_seq + len(cancel_request), cancel_ack)
    packets.extend(cancel_teardown)

    # 4. 原查询继续执行（因为取消失败）
    # 在真实场景中，查询会继续运行30秒

    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_lost_pcap(packets, "pgsql_cancel_request_lost.pcap")

def generate_terminate_lost_scenario():
    """生成Terminate消息丢失场景的数据包

    测试目的：模拟连接终止消息在网络传输中丢失的情况，但保留完整的query-result流程
    预期结果：Terminate消息丢失，但包含完整的查询响应流程，最后连接异常关闭
    网络表现：完整流程后Terminate消息丢失，导致连接无法正常关闭
    协议影响：服务器无法收到正常的连接终止信号，可能导致资源泄露
    """
    print("\n=== 生成Terminate消息丢失场景 ===")

    # 获取完整流程
    flow = create_complete_pgsql_flow()
    packets = []

    # 添加完整的PostgreSQL流程
    packets.extend(flow['handshake'])
    packets.append(flow['startup_message'])
    packets.append(flow['auth_request'])
    packets.append(flow['password_response'])
    packets.append(flow['auth_ok'])
    packets.append(flow['backend_key'])
    packets.append(flow['ready_for_query'])
    packets.append(flow['query_message'])
    packets.append(flow['row_description'])
    packets.append(flow['data_row'])
    packets.append(flow['command_complete'])
    packets.append(flow['ready_for_query_final'])

    # 对于Terminate消息丢失场景，我们添加正常的TCP关闭序列
    # 但不包含PostgreSQL的Terminate消息
    # 这模拟了客户端直接关闭TCP连接而没有发送Terminate消息的情况
    # 在实际场景中，这会导致服务器端无法进行优雅的资源清理
    packets.extend(flow['teardown'])

    write_lost_pcap(packets, "pgsql_terminate_lost.pcap")

def generate_error_response_lost_scenario():
    """生成ErrorResponse丢失场景的数据包

    测试目的：模拟服务器错误响应消息在网络传输中丢失的情况
    预期结果：客户端无法获知查询错误，可能一直等待响应
    网络表现：错误查询执行后，ErrorResponse消息丢失
    """
    print("\n=== 生成ErrorResponse丢失场景 ===")

    packets = []
    controller = create_packet_loss_controller()
    controller['loss_positions'].add(PacketLossPosition.ERROR_RESPONSE)

    global seq_num, ack_num
    reset_global_counters()

    # 1. 建立连接和认证（简化）
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    startup_data = b"user\0postgres\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(c_packet)
    seq_num += len(startup_msg)

    auth_resp = b"R" + struct.pack("!II", 8, 0) + b"Z" + struct.pack("!IB", 5, 73)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_resp)
    packets.append(s_packet)
    ack_num += len(auth_resp)

    # 2. 客户端发送错误的查询
    error_query = b"SELECT * FROM nonexistent_table;\0"
    query = b"Q" + struct.pack("!I", len(error_query) + 4) + error_query
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, query)
    packets.append(c_packet)
    seq_num += len(error_query)

    # 3. 服务器发送ErrorResponse（丢失）
    error_content = b"SERROR\0C42P01\0Mrelation \"nonexistent_table\" does not exist\0Fparse_relation.c\0L1180\0Rparserparse\0\0"
    error_resp = b"E" + struct.pack("!I", len(error_content) + 4) + error_content

    packet, should_add = create_pgsql_packet_with_loss_control(
        controller, SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port,
        ack_num, seq_num, error_resp, "PA", PacketLossPosition.ERROR_RESPONSE
    )

    if should_add:
        packets.append(packet)
    ack_num += len(error_resp)

    # 4. ReadyForQuery（正常发送）
    ready = b"Z" + struct.pack("!IB", 5, 73)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, ready)
    packets.append(s_packet)
    ack_num += len(ready)

    # 5. 客户端可能超时或发送新查询
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_lost_pcap(packets, "pgsql_error_response_lost.pcap")

def generate_transaction_begin_lost_scenario():
    """生成事务BEGIN响应丢失场景的数据包

    测试目的：模拟事务开始响应消息在网络传输中丢失的情况，但保留完整的query-result流程
    预期结果：CommandComplete丢失，但包含完整的查询响应流程用于对比分析
    网络表现：完整流程中CommandComplete消息丢失
    """
    print("\n=== 生成事务BEGIN响应丢失场景 ===")

    # 获取完整流程
    flow = create_complete_pgsql_flow()
    packets = []

    # 添加所有消息包，除了command_complete
    packets.extend(flow['handshake'])
    packets.append(flow['startup_message'])
    packets.append(flow['auth_request'])
    packets.append(flow['password_response'])
    packets.append(flow['auth_ok'])
    packets.append(flow['backend_key'])
    packets.append(flow['ready_for_query'])
    packets.append(flow['query_message'])
    packets.append(flow['row_description'])
    packets.append(flow['data_row'])
    # 跳过 command_complete（丢失）
    packets.append(flow['ready_for_query_final'])
    packets.extend(flow['teardown'])

    write_lost_pcap(packets, "pgsql_transaction_begin_lost.pcap")

def generate_notification_lost_scenario():
    """生成通知消息丢失场景的数据包

    测试目的：模拟异步NotificationResponse消息在网络传输中丢失的情况
    预期结果：NotificationResponse丢失，TCP序列号出现跳跃，模拟真实的网络丢包
    网络表现：在查询响应过程中穿插异步通知，但NotificationResponse消息丢失
    协议特性：模拟LISTEN/NOTIFY机制中异步通知消息的丢失，包含正确的TCP序列号处理
    """
    print("\n=== 生成通知消息丢失场景 ===")

    packets = []
    controller = create_packet_loss_controller()
    controller['loss_positions'].add(PacketLossPosition.NOTIFICATION)

    global seq_num, ack_num
    reset_global_counters()

    # 1. TCP三次握手和认证流程
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    # 完整的认证流程
    startup_data = b"user\0postgres\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    startup_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(startup_packet)
    seq_num += len(startup_msg)

    auth_req = b"R" + struct.pack("!II", 12, 5) + b"salt"
    auth_req_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_req)
    packets.append(auth_req_packet)
    ack_num += len(auth_req)

    password_data = b"md50123456789abcdef0123456789abcdef"
    password_resp = b"p" + struct.pack("!I", len(password_data) + 5) + password_data + b"\0"
    password_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, password_resp)
    packets.append(password_packet)
    seq_num += len(password_resp)

    auth_ok = b"R" + struct.pack("!II", 8, 0)
    backend_key_data = struct.pack("!II", 12345, 67890)
    backend_key = b"K" + struct.pack("!I", len(backend_key_data) + 4) + backend_key_data
    ready = b"Z" + struct.pack("!IB", 5, 73)
    auth_complete = auth_ok + backend_key + ready
    auth_complete_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_complete)
    packets.append(auth_complete_packet)
    ack_num += len(auth_complete)

    # 2. 客户端发送LISTEN命令
    listen_query = b"LISTEN test_channel;\0"
    listen_msg = b"Q" + struct.pack("!I", len(listen_query) + 4) + listen_query
    listen_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, listen_msg)
    packets.append(listen_packet)
    seq_num += len(listen_msg)

    # 3. 服务器响应LISTEN命令
    cmd_complete_text = b"LISTEN\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    ready = b"Z" + struct.pack("!IB", 5, 73)
    listen_resp = cmd_complete + ready
    listen_resp_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, listen_resp)
    packets.append(listen_resp_packet)
    ack_num += len(listen_resp)

    # 4. 客户端发送查询
    query_str = b"SELECT id, name FROM users WHERE id = 1;\0"
    query = b"Q" + struct.pack("!I", len(query_str) + 4) + query_str
    query_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, query)
    packets.append(query_packet)
    seq_num += len(query)

    # 5. 服务器发送RowDescription
    row_desc_data = struct.pack("!H", 2)  # 2个字段
    row_desc_data += b"id\0" + struct.pack("!IHIhih", 0, 0, 23, 4, 4, 0)
    row_desc_data += b"name\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data
    row_desc_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, row_desc)
    packets.append(row_desc_packet)
    ack_num += len(row_desc)

    # 6. 异步NotificationResponse（丢失）- 关键：这里模拟异步通知穿插在查询响应中
    notification_data = struct.pack("!I", 12345) + b"test_channel\0" + b"test_payload\0"
    notification = b"A" + struct.pack("!I", len(notification_data) + 4) + notification_data

    packet, should_add = create_pgsql_packet_with_loss_control(
        controller, SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port,
        ack_num, seq_num, notification, "PA", PacketLossPosition.NOTIFICATION
    )

    if should_add:
        packets.append(packet)
    # 关键：即使消息丢失，TCP序列号仍然推进，模拟真实的网络丢包
    ack_num += len(notification)

    # 7. 继续发送DataRow（客户端会发现TCP序列号跳跃）
    data_row_data = struct.pack("!HI", 2, 1) + b"1" + struct.pack("!I", 5) + b"Alice"
    data_row = b"D" + struct.pack("!I", len(data_row_data) + 4) + data_row_data
    data_row_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, data_row)
    packets.append(data_row_packet)
    ack_num += len(data_row)

    # 8. CommandComplete和ReadyForQuery
    cmd_complete_text = b"SELECT 1\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    ready_final = b"Z" + struct.pack("!IB", 5, 73)
    final_resp = cmd_complete + ready_final
    final_resp_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, final_resp)
    packets.append(final_resp_packet)
    ack_num += len(final_resp)

    # 9. 连接关闭
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    write_lost_pcap(packets, "pgsql_notification_lost.pcap")

# ============================================================================
# 响应数据丢失测试场景
# ============================================================================

def generate_row_description_lost_scenario():
    """生成RowDescription丢失场景的数据包

    测试目的：模拟查询结果的行描述信息丢失的情况，但保留完整的query-result流程
    预期结果：RowDescription丢失，但包含完整的查询响应流程用于对比分析
    网络表现：完整流程中RowDescription消息丢失
    """
    print("\n=== 生成RowDescription丢失场景 ===")

    # 获取完整流程
    flow = create_complete_pgsql_flow()
    packets = []

    # 添加所有消息包，除了row_description
    packets.extend(flow['handshake'])
    packets.append(flow['startup_message'])
    packets.append(flow['auth_request'])
    packets.append(flow['password_response'])
    packets.append(flow['auth_ok'])
    packets.append(flow['backend_key'])
    packets.append(flow['ready_for_query'])
    packets.append(flow['query_message'])
    # 跳过 row_description（丢失）
    packets.append(flow['data_row'])
    packets.append(flow['command_complete'])
    packets.append(flow['ready_for_query_final'])
    packets.extend(flow['teardown'])

    write_lost_pcap(packets, "pgsql_row_description_lost.pcap")

def generate_data_row_lost_scenario():
    """生成DataRow部分丢失场景的数据包

    测试目的：模拟查询结果中部分数据行丢失的情况，但保留完整的query-result流程
    预期结果：DataRow丢失，但包含完整的查询响应流程用于对比分析
    网络表现：完整流程中DataRow消息丢失
    """
    print("\n=== 生成DataRow部分丢失场景 ===")

    # 获取完整流程
    flow = create_complete_pgsql_flow()
    packets = []

    # 添加所有消息包，除了data_row
    packets.extend(flow['handshake'])
    packets.append(flow['startup_message'])
    packets.append(flow['auth_request'])
    packets.append(flow['password_response'])
    packets.append(flow['auth_ok'])
    packets.append(flow['backend_key'])
    packets.append(flow['ready_for_query'])
    packets.append(flow['query_message'])
    packets.append(flow['row_description'])
    # 跳过 data_row（丢失）
    packets.append(flow['command_complete'])
    packets.append(flow['ready_for_query_final'])
    packets.extend(flow['teardown'])

    write_lost_pcap(packets, "pgsql_data_row_lost.pcap")

def generate_command_complete_lost_scenario():
    """生成CommandComplete消息丢失场景的数据包

    测试目的：模拟查询完成消息在网络传输中丢失的情况，但保留完整的query-result流程
    预期结果：CommandComplete丢失，但包含完整的查询响应流程用于对比分析
    网络表现：完整流程中CommandComplete消息丢失，客户端无法确认查询执行状态
    协议影响：客户端可能无法确定查询是否成功完成，影响事务状态判断
    """
    print("\n=== 生成CommandComplete消息丢失场景 ===")

    # 获取完整流程
    flow = create_complete_pgsql_flow()
    packets = []

    # 添加所有消息包，除了command_complete
    packets.extend(flow['handshake'])
    packets.append(flow['startup_message'])
    packets.append(flow['auth_request'])
    packets.append(flow['password_response'])
    packets.append(flow['auth_ok'])
    packets.append(flow['backend_key'])
    packets.append(flow['ready_for_query'])
    packets.append(flow['query_message'])
    packets.append(flow['row_description'])
    packets.append(flow['data_row'])
    # 跳过 command_complete（丢失）
    packets.append(flow['ready_for_query_final'])
    packets.extend(flow['teardown'])

    write_lost_pcap(packets, "pgsql_command_complete_lost.pcap")

def generate_large_datarow_result_set_segment_lost_scenario(lost_segments=None):
    """生成大型DataRow结果集TCP分段丢失场景的数据包

    测试目的：模拟包含大量数据的DataRow消息在TCP分段传输中指定分段丢失的情况
    预期结果：DataRow消息数据不完整，客户端无法正确解析结果行数据
    网络表现：大DataRow消息被分成3个TCP段，指定的段丢失
    协议特性：简单查询协议 Q(Query) -> T(RowDescription) -> D(DataRow) -> C(CommandComplete) -> Z(ReadyForQuery)
    分段策略：将单个大DataRow消息分为三个TCP段，丢失指定的段
    数据内容：生成包含大量文本数据的单行结果，确保单个DataRow消息超过TCP MSS大小

    Args:
        lost_segments (list): 要丢失的分段编号列表，如[1], [2], [3], [1,2], [2,3], [1,2,3]
                             默认为[2]（丢失第2段，保持向后兼容）
    """
    if lost_segments is None:
        lost_segments = [2]  # 默认丢失第2段，保持向后兼容
    lost_segments_str = "_".join(map(str, sorted(lost_segments)))
    print(f"\n=== 生成大型DataRow结果集TCP分段丢失场景 (丢失第{lost_segments_str}段) ===")

    packets = []
    segment_controller = create_tcp_segment_loss_controller(lost_segments)

    global seq_num, ack_num
    reset_global_counters()

    # 1. 建立连接和认证（简化）
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    startup_data = b"user\0postgres\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(c_packet)
    seq_num += len(startup_msg)

    auth_resp = b"R" + struct.pack("!II", 8, 0) + b"Z" + struct.pack("!IB", 5, 73)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_resp)
    packets.append(s_packet)
    ack_num += len(auth_resp)

    print(f"[调试] 连接建立完成，开始构造大型DataRow结果集查询")

    # 2. 客户端发送查询（查询包含大量数据的表）
    query_str = b"SELECT id, title, content, metadata FROM large_documents WHERE id = 1;\0"
    query = b"Q" + struct.pack("!I", len(query_str) + 4) + query_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, query)
    packets.append(c_packet)
    seq_num += len(query)
    print(f"[调试] 客户端发送查询，大小: {len(query)} 字节")

    # 3. 服务器发送RowDescription（正常）
    row_desc_data = struct.pack("!H", 4)  # 4个字段

    # 字段1: id (integer)
    row_desc_data += b"id\0" + struct.pack("!IHIhih", 0, 0, 23, 4, -1, 0)
    # 字段2: title (text)
    row_desc_data += b"title\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    # 字段3: content (text) - 大字段
    row_desc_data += b"content\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    # 字段4: metadata (jsonb)
    row_desc_data += b"metadata\0" + struct.pack("!IHIhih", 0, 0, 3802, -1, -1, 0)

    row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, row_desc)
    packets.append(s_packet)
    ack_num += len(row_desc)
    print(f"[调试] 服务器发送RowDescription，大小: {len(row_desc)} 字节")

    # 4. 构造大型DataRow消息（包含大量数据）
    # 创建一个包含大量文本内容的数据行，确保需要TCP分段

    # 字段1: id
    field1_data = b"1"

    # 字段2: title
    field2_data = b"PostgreSQL Protocol Testing Document with Very Long Title for TCP Segmentation Testing"

    # 字段3: content - 大字段（约2000字节的文本内容）
    content_parts = [
        "This is a comprehensive PostgreSQL protocol testing document designed to test TCP segmentation scenarios.",
        "The document contains extensive technical details about PostgreSQL wire protocol implementation,",
        "including message formats, data types, authentication mechanisms, and query execution flows.",
        "This content is specifically crafted to be large enough to require TCP segmentation when transmitted",
        "as part of a DataRow message in the PostgreSQL protocol. The purpose is to simulate real-world",
        "scenarios where large result sets or documents are returned from database queries.",
        "PostgreSQL supports various data types including integers, text, bytea, json, jsonb, arrays,",
        "and custom types. The wire protocol efficiently handles these through a binary format that",
        "includes type information and length prefixes for variable-length data.",
        "When dealing with large datasets, PostgreSQL can return substantial amounts of data in DataRow",
        "messages. Each DataRow message contains field count followed by field data with length prefixes.",
        "In network environments with limited MTU sizes, these large messages may be fragmented across",
        "multiple TCP segments. This test scenario specifically validates protocol parser behavior",
        "when TCP segments are lost during transmission, which can occur in unreliable network conditions.",
        "The PostgreSQL protocol is designed to be robust against such network issues, but parsers",
        "must correctly handle incomplete messages and implement appropriate error recovery mechanisms.",
        "This test data includes various character encodings, special characters, and formatting to",
        "ensure comprehensive coverage of potential parsing edge cases that might arise in production.",
        "Additional padding text follows to reach the target size for reliable TCP segmentation testing.",
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut",
        "labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco",
        "laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in",
        "voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat",
        "non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
    ]

    field3_data = " ".join(content_parts).encode('utf-8')

    # 确保content字段足够大
    while len(field3_data) < 2000:
        additional_text = f" [Padding text block {len(field3_data)}] This is additional content to ensure the DataRow message is large enough for TCP segmentation testing."
        field3_data += additional_text.encode('utf-8')

    # 字段4: metadata (JSON格式)
    metadata_obj = {
        "document_type": "test_document",
        "version": "1.0",
        "created_by": "postgresql_protocol_tester",
        "tags": ["tcp_segmentation", "protocol_testing", "datarow_testing", "network_simulation"],
        "properties": {
            "size_category": "large",
            "content_type": "text/plain",
            "encoding": "utf-8",
            "test_scenario": "large_datarow_segment_lost"
        },
        "statistics": {
            "character_count": len(field3_data),
            "word_count": len(field3_data.decode('utf-8').split()),
            "segment_count": 3,
            "lost_segment": 2
        }
    }

    import json
    field4_data = json.dumps(metadata_obj, ensure_ascii=False).encode('utf-8')

    print(f"[调试] 构造DataRow字段数据:")
    print(f"  - 字段1 (id): {len(field1_data)} 字节")
    print(f"  - 字段2 (title): {len(field2_data)} 字节")
    print(f"  - 字段3 (content): {len(field3_data)} 字节")
    print(f"  - 字段4 (metadata): {len(field4_data)} 字节")

    # 构造完整的DataRow消息
    data_row_data = struct.pack("!H", 4)  # 4个字段

    # 添加字段1 (id)
    data_row_data += struct.pack("!I", len(field1_data)) + field1_data

    # 添加字段2 (title)
    data_row_data += struct.pack("!I", len(field2_data)) + field2_data

    # 添加字段3 (content)
    data_row_data += struct.pack("!I", len(field3_data)) + field3_data

    # 添加字段4 (metadata)
    data_row_data += struct.pack("!I", len(field4_data)) + field4_data

    # 创建完整的DataRow消息
    data_row_msg = b"D" + struct.pack("!I", len(data_row_data) + 4) + data_row_data

    print(f"[调试] 完整DataRow消息大小: {len(data_row_msg)} 字节")

    # 5. 使用新的统一TCP分段丢失控制器发送DataRow消息
    datarow_packets, ack_num = create_tcp_segmented_packets_with_loss(
        segment_controller, SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port,
        ack_num, seq_num, data_row_msg, "DataRow"
    )
    packets.extend(datarow_packets)

    # 6. 服务器发送CommandComplete和ReadyForQuery（正常）
    cmd_complete_text = b"SELECT 1\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    ready = b"Z" + struct.pack("!IB", 5, 73)
    final_resp = cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, final_resp)
    packets.append(s_packet)
    ack_num += len(final_resp)
    print(f"[调试] 服务器发送CommandComplete + ReadyForQuery，大小: {len(final_resp)} 字节")

    # 7. 连接关闭
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    # 生成文件
    filename = f"pgsql_large_datarow_result_set_segments_{lost_segments_str}_lost.pcap"
    write_lost_pcap(packets, filename)
    print(f"[完成] 大型DataRow结果集TCP分段丢失场景生成完成 (丢失第{lost_segments_str}段)")

def generate_large_row_description_segment_lost_scenario(lost_segments=None):
    """生成大型RowDescription消息TCP分段丢失场景的数据包

    测试目的：模拟包含大量列的RowDescription消息在TCP分段传输中指定分段丢失的情况
    预期结果：RowDescription消息数据不完整，客户端无法正确解析列信息，后续DataRow解析失败
    网络表现：大RowDescription消息被分成3个TCP段，指定的段丢失
    协议特性：简单查询协议 Q(Query) -> T(RowDescription) -> D(DataRow) -> C(CommandComplete) -> Z(ReadyForQuery)
    分段策略：将单个大RowDescription消息分为三个TCP段，丢失指定的段
    数据内容：生成包含大量列（50-100列）的RowDescription，确保消息超过TCP MSS大小

    Args:
        lost_segments (list): 要丢失的分段编号列表，如[1], [2], [3], [1,2], [2,3], [1,2,3]
                             默认为[2]（丢失第2段，保持向后兼容）
    """
    if lost_segments is None:
        lost_segments = [2]  # 默认丢失第2段，保持向后兼容

    lost_segments_str = "_".join(map(str, sorted(lost_segments)))
    print(f"\n=== 生成大型RowDescription消息TCP分段丢失场景 (丢失第{lost_segments_str}段) ===")

    packets = []
    segment_controller = create_tcp_segment_loss_controller(lost_segments)

    global seq_num, ack_num
    reset_global_counters()

    # 1. 建立连接和认证（简化）
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    startup_data = b"user\0postgres\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(c_packet)
    seq_num += len(startup_msg)

    auth_resp = b"R" + struct.pack("!II", 8, 0) + b"Z" + struct.pack("!IB", 5, 73)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_resp)
    packets.append(s_packet)
    ack_num += len(auth_resp)

    print(f"[调试] 连接建立完成，开始构造大型RowDescription查询")

    # 2. 客户端发送查询（查询包含大量列的表）
    query_str = b"SELECT * FROM large_table_with_many_columns;\0"
    query = b"Q" + struct.pack("!I", len(query_str) + 4) + query_str
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, query)
    packets.append(c_packet)
    seq_num += len(query)
    print(f"[调试] 客户端发送查询，大小: {len(query)} 字节")

    # 3. 构造大型RowDescription消息
    row_desc_msg = _generate_large_row_description_message()
    print(f"[调试] 完整RowDescription消息大小: {len(row_desc_msg)} 字节")

    # 4. 使用新的统一TCP分段丢失控制器发送RowDescription消息
    rowdesc_packets, ack_num = create_tcp_segmented_packets_with_loss(
        segment_controller, SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port,
        ack_num, seq_num, row_desc_msg, "RowDescription"
    )
    packets.extend(rowdesc_packets)

    # 5. 发送DataRow（如果RowDescription没有完全丢失）
    if len(lost_segments) < 3:
        _add_large_table_datarows(packets, ack_num, seq_num)

    # 6. 发送CommandComplete和ReadyForQuery
    if len(lost_segments) < 3:
        _add_query_completion(packets, ack_num, seq_num)

    # 7. 连接关闭
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    # 8. 生成文件
    filename = f"pgsql_large_row_description_segments_{lost_segments_str}_lost.pcap"
    write_lost_pcap(packets, filename)
    print(f"[完成] 大型RowDescription消息TCP分段丢失场景生成完成 (丢失第{lost_segments_str}段)")

# ============================================================================
# 通用TCP分段丢失场景生成器
# ============================================================================

def generate_tcp_segment_loss_scenario(scenario_type, lost_segments, scenario_suffix=""):
    """通用TCP分段丢失场景生成器

    Args:
        scenario_type (str): 场景类型 ('simple_query', 'extended_parse', 'datarow')
        lost_segments (list): 要丢失的分段编号列表，如[1], [2], [3], [1,2], [2,3], [1,2,3]
        scenario_suffix (str): 场景后缀，用于区分不同的丢失模式
    """
    if not lost_segments:
        print("错误：必须指定要丢失的分段")
        return

    # 生成场景描述
    lost_segments_str = "_".join(map(str, sorted(lost_segments)))
    if not scenario_suffix:
        scenario_suffix = f"segments_{lost_segments_str}_lost"

    print(f"\n=== 生成{scenario_type}场景TCP分段丢失测试 (丢失第{lost_segments_str}段) ===")

    packets = []
    segment_controller = create_tcp_segment_loss_controller(lost_segments)

    global seq_num, ack_num
    reset_global_counters()

    # 1. 建立连接和认证（简化）
    handshake_packets, seq_num, ack_num = create_tcp_handshake(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT)
    packets.extend(handshake_packets)

    startup_data = b"user\0postgres\0database\0testdb\0\0"
    startup_msg = struct.pack("!II", len(startup_data) + 8, 196608) + startup_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, startup_msg)
    packets.append(c_packet)
    seq_num += len(startup_msg)

    auth_resp = b"R" + struct.pack("!II", 8, 0) + b"Z" + struct.pack("!IB", 5, 73)
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, auth_resp)
    packets.append(s_packet)
    ack_num += len(auth_resp)

    # 2. 根据场景类型生成相应的大消息
    if scenario_type == "simple_query":
        # 生成大型简单查询
        large_sql = _generate_large_sql_query()
        query_msg = b"Q" + struct.pack("!I", len(large_sql) + 4) + large_sql

        # 发送分段查询消息
        query_packets, seq_num = create_tcp_segmented_packets_with_loss(
            segment_controller, CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT,
            seq_num, ack_num, query_msg, "Query"
        )
        packets.extend(query_packets)

        # 服务器响应（如果不是全部丢失）
        if len(lost_segments) < 3:
            _add_simple_query_response(packets, ack_num, seq_num)

    elif scenario_type == "extended_parse":
        # 生成大型Parse消息
        large_sql = _generate_large_sql_query()
        stmt_name = b"large_stmt\0"
        param_count = struct.pack("!H", 3)
        param_types = struct.pack("!3I", 25, 23, 1700)  # text, int, numeric
        parse_data = stmt_name + large_sql + param_count + param_types
        parse_msg = b"P" + struct.pack("!I", len(parse_data) + 4) + parse_data

        # 发送分段Parse消息
        parse_packets, seq_num = create_tcp_segmented_packets_with_loss(
            segment_controller, CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT,
            seq_num, ack_num, parse_msg, "Parse"
        )
        packets.extend(parse_packets)

        # 发送后续的扩展查询消息
        _add_extended_query_messages(packets, seq_num, ack_num)

        # 服务器响应
        if len(lost_segments) < 3:
            _add_extended_query_response(packets, ack_num, seq_num, lost_segments)

    elif scenario_type == "datarow":
        # 发送查询请求
        query_str = b"SELECT id, title, content, metadata FROM large_documents WHERE id = 1;\0"
        query = b"Q" + struct.pack("!I", len(query_str) + 4) + query_str
        c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, query)
        packets.append(c_packet)
        seq_num += len(query)

        # 服务器发送RowDescription
        row_desc = _generate_large_datarow_description()
        s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, row_desc)
        packets.append(s_packet)
        ack_num += len(row_desc)

        # 生成大型DataRow消息
        data_row_msg = _generate_large_datarow_message()

        # 发送分段DataRow消息
        datarow_packets, ack_num = create_tcp_segmented_packets_with_loss(
            segment_controller, SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port,
            ack_num, seq_num, data_row_msg, "DataRow"
        )
        packets.extend(datarow_packets)

        # 发送CommandComplete和ReadyForQuery
        if len(lost_segments) < 3:
            _add_query_completion(packets, ack_num, seq_num)

    elif scenario_type == "rowdesc":
        # 发送查询请求
        query_str = b"SELECT * FROM large_table_with_many_columns;\0"
        query = b"Q" + struct.pack("!I", len(query_str) + 4) + query_str
        c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, query)
        packets.append(c_packet)
        seq_num += len(query)

        # 生成大型RowDescription消息
        row_desc_msg = _generate_large_row_description_message()

        # 发送分段RowDescription消息
        rowdesc_packets, ack_num = create_tcp_segmented_packets_with_loss(
            segment_controller, SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port,
            ack_num, seq_num, row_desc_msg, "RowDescription"
        )
        packets.extend(rowdesc_packets)

        # 发送DataRow（如果RowDescription没有完全丢失）
        if len(lost_segments) < 3:
            _add_large_table_datarows(packets, ack_num, seq_num)

        # 发送CommandComplete和ReadyForQuery
        if len(lost_segments) < 3:
            _add_query_completion(packets, ack_num, seq_num)

    elif scenario_type == "parse":
        # 生成大型Parse消息
        parse_msg = _generate_large_parse_message()

        # 发送分段Parse消息
        parse_packets, seq_num = create_tcp_segmented_packets_with_loss(
            segment_controller, CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT,
            seq_num, ack_num, parse_msg, "Parse"
        )
        packets.extend(parse_packets)

        # 发送后续的扩展查询消息（如果Parse没有完全丢失）
        if len(lost_segments) < 3:
            _add_extended_query_messages(packets, seq_num, ack_num)

        # 服务器响应
        if len(lost_segments) < 3:
            _add_extended_query_response(packets, ack_num, seq_num, lost_segments)

    # 3. 连接关闭
    teardown_packets = create_tcp_teardown(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num)
    packets.extend(teardown_packets)

    # 4. 生成文件
    filename = f"pgsql_{scenario_type}_{scenario_suffix}.pcap"
    write_lost_pcap(packets, filename)
    print(f"[完成] {scenario_type}场景TCP分段丢失测试生成完成 (丢失第{lost_segments_str}段)")

# ============================================================================
# 辅助函数：生成大型消息内容
# ============================================================================

def _generate_large_sql_query():
    """生成大型SQL查询（约2000字节）"""
    large_sql_parts = [
        "SELECT u.user_id, u.username, u.email, u.first_name, u.last_name, u.phone_number,",
        "       u.created_at, u.updated_at, u.last_login_at, u.is_active, u.email_verified,",
        "       p.profile_id, p.bio, p.avatar_url, p.birth_date, p.gender, p.location,",
        "       p.website, p.social_media_links, p.preferences, p.privacy_settings,",
        "       a.address_id, a.street_address, a.city, a.state, a.postal_code, a.country,",
        "       a.address_type, a.is_primary, a.latitude, a.longitude,",
        "       o.order_id, o.order_number, o.order_date, o.total_amount, o.currency,",
        "       o.order_status, o.payment_status, o.shipping_method, o.tracking_number,",
        "       oi.item_id, oi.product_name, oi.product_sku, oi.quantity, oi.unit_price,",
        "       oi.total_price, oi.discount_amount, oi.tax_amount,",
        "       c.category_id, c.category_name, c.parent_category_id, c.category_level,",
        "       c.description as category_description, c.image_url as category_image,",
        "       r.review_id, r.rating, r.review_title, r.review_text, r.review_date,",
        "       r.helpful_votes, r.verified_purchase, r.reviewer_name",
        "FROM users u",
        "LEFT JOIN user_profiles p ON u.user_id = p.user_id",
        "LEFT JOIN user_addresses a ON u.user_id = a.user_id AND a.is_primary = true",
        "LEFT JOIN orders o ON u.user_id = o.user_id",
        "LEFT JOIN order_items oi ON o.order_id = oi.order_id",
        "LEFT JOIN product_categories c ON oi.category_id = c.category_id",
        "LEFT JOIN product_reviews r ON oi.product_id = r.product_id AND u.user_id = r.user_id",
        "WHERE u.created_at >= '2023-01-01 00:00:00' AND u.created_at <= '2023-12-31 23:59:59'",
        "  AND u.is_active = true AND u.email_verified = true",
        "  AND (u.email LIKE '%@company.com' OR u.email LIKE '%@enterprise.org')",
        "  AND p.privacy_settings->>'public_profile' = 'true'",
        "  AND a.country IN ('USA', 'Canada', 'UK', 'Germany', 'France', 'Japan')",
        "  AND o.order_status IN ('completed', 'shipped', 'delivered')",
        "  AND o.total_amount BETWEEN 100.00 AND 5000.00",
        "  AND oi.quantity > 0 AND oi.unit_price > 0",
        "  AND c.category_level <= 3",
        "  AND r.rating >= 4 AND r.verified_purchase = true",
        "ORDER BY u.created_at DESC, o.order_date DESC, r.rating DESC, oi.total_price DESC",
        "LIMIT 100 OFFSET 0"
    ]

    large_sql = " ".join(large_sql_parts) + ";\0"
    large_sql_bytes = large_sql.encode('utf-8')

    # 确保查询足够大
    while len(large_sql_bytes) < 2000:
        comment = f" /* TCP分段测试注释，用于增加查询大小。注释编号：{len(large_sql_bytes)} */ "
        large_sql = large_sql[:-2] + comment + large_sql[-2:]
        large_sql_bytes = large_sql.encode('utf-8')

    return large_sql_bytes

def _generate_large_datarow_description():
    """生成大型DataRow的RowDescription"""
    row_desc_data = struct.pack("!H", 4)  # 4个字段

    # 字段1: id (integer)
    row_desc_data += b"id\0" + struct.pack("!IHIhih", 0, 0, 23, 4, -1, 0)
    # 字段2: title (text)
    row_desc_data += b"title\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    # 字段3: content (text) - 大字段
    row_desc_data += b"content\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    # 字段4: metadata (jsonb)
    row_desc_data += b"metadata\0" + struct.pack("!IHIhih", 0, 0, 3802, -1, -1, 0)

    return b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data

def _generate_large_datarow_message():
    """生成大型DataRow消息（约2500字节）"""
    # 字段1: id
    field1_data = b"1"

    # 字段2: title
    field2_data = b"PostgreSQL Protocol Testing Document with Very Long Title for TCP Segmentation Testing"

    # 字段3: content - 大字段（约2000字节的文本内容）
    content_parts = [
        "This is a comprehensive PostgreSQL protocol testing document designed to test TCP segmentation scenarios.",
        "The document contains extensive technical details about PostgreSQL wire protocol implementation,",
        "including message formats, data types, authentication mechanisms, and query execution flows.",
        "This content is specifically crafted to be large enough to require TCP segmentation when transmitted",
        "as part of a DataRow message in the PostgreSQL protocol. The purpose is to simulate real-world",
        "scenarios where large result sets or documents are returned from database queries.",
        "PostgreSQL supports various data types including integers, text, bytea, json, jsonb, arrays,",
        "and custom types. The wire protocol efficiently handles these through a binary format that",
        "includes type information and length prefixes for variable-length data.",
        "When dealing with large datasets, PostgreSQL can return substantial amounts of data in DataRow",
        "messages. Each DataRow message contains field count followed by field data with length prefixes.",
        "In network environments with limited MTU sizes, these large messages may be fragmented across",
        "multiple TCP segments. This test scenario specifically validates protocol parser behavior",
        "when TCP segments are lost during transmission, which can occur in unreliable network conditions.",
        "The PostgreSQL protocol is designed to be robust against such network issues, but parsers",
        "must correctly handle incomplete messages and implement appropriate error recovery mechanisms.",
        "This test data includes various character encodings, special characters, and formatting to",
        "ensure comprehensive coverage of potential parsing edge cases that might arise in production.",
        "Additional padding text follows to reach the target size for reliable TCP segmentation testing.",
        "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut",
        "labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco",
        "laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in",
        "voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat",
        "non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
    ]

    field3_data = " ".join(content_parts).encode('utf-8')

    # 确保content字段足够大
    while len(field3_data) < 2000:
        additional_text = f" [Padding text block {len(field3_data)}] This is additional content to ensure the DataRow message is large enough for TCP segmentation testing."
        field3_data += additional_text.encode('utf-8')

    # 字段4: metadata (JSON格式)
    metadata_obj = {
        "document_type": "test_document",
        "version": "1.0",
        "created_by": "postgresql_protocol_tester",
        "tags": ["tcp_segmentation", "protocol_testing", "datarow_testing", "network_simulation"],
        "properties": {
            "size_category": "large",
            "content_type": "text/plain",
            "encoding": "utf-8",
            "test_scenario": "tcp_segment_loss"
        },
        "statistics": {
            "character_count": len(field3_data),
            "word_count": len(field3_data.decode('utf-8').split()),
            "segment_count": 3
        }
    }

    import json
    field4_data = json.dumps(metadata_obj, ensure_ascii=False).encode('utf-8')

    # 构造完整的DataRow消息
    data_row_data = struct.pack("!H", 4)  # 4个字段
    data_row_data += struct.pack("!I", len(field1_data)) + field1_data
    data_row_data += struct.pack("!I", len(field2_data)) + field2_data
    data_row_data += struct.pack("!I", len(field3_data)) + field3_data
    data_row_data += struct.pack("!I", len(field4_data)) + field4_data

    return b"D" + struct.pack("!I", len(data_row_data) + 4) + data_row_data

def _add_simple_query_response(packets, ack_num, seq_num):
    """添加简单查询的服务器响应"""
    # RowDescription
    row_desc_data = struct.pack("!H", 3)  # 3个字段
    row_desc_data += b"user_id\0" + struct.pack("!IHIhih", 0, 0, 23, 4, -1, 0)
    row_desc_data += b"username\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    row_desc_data += b"email\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
    row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data

    # DataRow
    data_row_data = struct.pack("!H", 3)
    data_row_data += struct.pack("!I", 4) + b"1001"
    data_row_data += struct.pack("!I", 15) + b"john_enterprise"
    data_row_data += struct.pack("!I", 16) + b"<EMAIL>"
    data_row = b"D" + struct.pack("!I", len(data_row_data) + 4) + data_row_data

    # CommandComplete + ReadyForQuery
    cmd_complete_text = b"SELECT 1\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    ready = b"Z" + struct.pack("!IB", 5, 73)

    response = row_desc + data_row + cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, response)
    packets.append(s_packet)

def _add_extended_query_messages(packets, seq_num, ack_num):
    """添加扩展查询协议的后续消息（Bind, Execute, Sync）"""
    # Bind消息
    bind_data = b"portal1\0large_stmt\0" + struct.pack("!HHH", 0, 0, 0)
    bind_msg = b"B" + struct.pack("!I", len(bind_data) + 4) + bind_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, bind_msg)
    packets.append(c_packet)
    seq_num += len(bind_msg)

    # Execute消息
    execute_data = b"portal1\0" + struct.pack("!I", 0)
    execute_msg = b"E" + struct.pack("!I", len(execute_data) + 4) + execute_data
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, execute_msg)
    packets.append(c_packet)
    seq_num += len(execute_msg)

    # Sync消息
    sync_msg = b"S" + struct.pack("!I", 4)
    c_packet = create_pgsql_packet(CLIENT_IP, SERVER_IP, client_port, PGSQL_PORT, seq_num, ack_num, sync_msg)
    packets.append(c_packet)

def _add_extended_query_response(packets, ack_num, seq_num, lost_segments):
    """添加扩展查询的服务器响应"""
    if 1 in lost_segments:
        # Parse失败
        error_content = b"SERROR\0C42601\0Msyntax error\0\0"
        error_resp = b"E" + struct.pack("!I", len(error_content) + 4) + error_content
        ready = b"Z" + struct.pack("!IB", 5, 73)
        response = error_resp + ready
    else:
        # Parse成功
        parse_complete = b"1" + struct.pack("!I", 4)
        bind_complete = b"2" + struct.pack("!I", 4)

        # 简单的结果
        row_desc_data = struct.pack("!H", 1)
        row_desc_data += b"result\0" + struct.pack("!IHIhih", 0, 0, 25, -1, -1, 0)
        row_desc = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data

        data_row_data = struct.pack("!H", 1)
        data_row_data += struct.pack("!I", 7) + b"success"
        data_row = b"D" + struct.pack("!I", len(data_row_data) + 4) + data_row_data

        cmd_complete_text = b"SELECT 1\0"
        cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
        ready = b"Z" + struct.pack("!IB", 5, 73)

        response = parse_complete + bind_complete + row_desc + data_row + cmd_complete + ready

    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, response)
    packets.append(s_packet)

def _add_query_completion(packets, ack_num, seq_num):
    """添加查询完成响应"""
    cmd_complete_text = b"SELECT 1\0"
    cmd_complete = b"C" + struct.pack("!I", len(cmd_complete_text) + 4) + cmd_complete_text
    ready = b"Z" + struct.pack("!IB", 5, 73)
    final_resp = cmd_complete + ready
    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, final_resp)
    packets.append(s_packet)

def _generate_large_parse_message():
    """生成大型Parse消息（约2500字节）"""

    # 构造大型SQL查询（约2000字节，确保需要TCP分段）
    large_sql_parts = [
        "SELECT u.id, u.username, u.email, u.first_name, u.last_name, u.created_at, u.updated_at,",
        "       p.profile_id, p.bio, p.avatar_url, p.phone_number, p.address, p.city, p.country,",
        "       o.order_id, o.order_date, o.total_amount, o.status, o.shipping_address,",
        "       oi.item_id, oi.product_name, oi.quantity, oi.unit_price, oi.total_price,",
        "       c.category_id, c.category_name, c.description as category_desc,",
        "       r.review_id, r.rating, r.comment, r.review_date",
        "FROM users u",
        "LEFT JOIN user_profiles p ON u.id = p.user_id",
        "LEFT JOIN orders o ON u.id = o.user_id",
        "LEFT JOIN order_items oi ON o.order_id = oi.order_id",
        "LEFT JOIN categories c ON oi.category_id = c.category_id",
        "LEFT JOIN reviews r ON oi.item_id = r.item_id AND u.id = r.user_id",
        "WHERE u.created_at >= $1 AND u.created_at <= $2",
        "  AND (u.email LIKE $3 OR u.username LIKE $4)",
        "  AND p.country IN ($5, $6, $7, $8, $9)",
        "  AND o.status IN ('pending', 'processing', 'shipped', 'delivered')",
        "  AND oi.unit_price BETWEEN $10 AND $11",
        "  AND r.rating >= $12",
        "ORDER BY u.created_at DESC, o.order_date DESC, r.rating DESC",
        "LIMIT $13 OFFSET $14"
    ]

    large_sql = " ".join(large_sql_parts) + ";\0"
    large_sql_bytes = large_sql.encode('utf-8')

    # 确保查询足够大（如果不够大，添加注释）
    while len(large_sql_bytes) < 2000:
        comment = f" /* 这是一个用于测试TCP分段的长注释，用于增加查询大小以确保需要多个TCP段传输。注释编号：{len(large_sql_bytes)} */ "
        large_sql = large_sql[:-2] + comment + large_sql[-2:]  # 在\0前插入注释
        large_sql_bytes = large_sql.encode('utf-8')

    print(f"[调试] 大SQL查询大小: {len(large_sql_bytes)} 字节")

    # 构造Parse消息
    stmt_name = b"large_stmt\0"
    param_count = struct.pack("!H", 14)  # 14个参数
    # 参数类型OID：timestamp, timestamp, text, text, text, text, text, text, text, numeric, numeric, integer, integer, integer
    param_types = struct.pack("!14I", 1114, 1114, 25, 25, 25, 25, 25, 25, 25, 1700, 1700, 23, 23, 23)

    parse_data = stmt_name + large_sql_bytes + param_count + param_types
    parse_msg = b"P" + struct.pack("!I", len(parse_data) + 4) + parse_data

    print(f"[调试] Parse消息总大小: {len(parse_msg)} 字节")

    return parse_msg

def _generate_large_row_description_message():
    """生成大型RowDescription消息（包含大量列，约2500字节）"""

    # PostgreSQL常见数据类型OID
    data_types = [
        (23, 4, -1),      # int4 (integer)
        (20, 8, -1),      # int8 (bigint)
        (25, -1, -1),     # text
        (1043, -1, 104),  # varchar(100)
        (1700, -1, 655364), # numeric(10,2)
        (1114, 8, -1),    # timestamp
        (1184, 8, -1),    # timestamptz
        (16, 1, -1),      # bool
        (700, 4, -1),     # float4
        (701, 8, -1),     # float8
        (1082, 4, -1),    # date
        (1083, 8, -1),    # time
        (114, -1, -1),    # json
        (3802, -1, -1),   # jsonb
        (17, -1, -1),     # bytea
        (2950, -1, -1),   # uuid
        (1000, -1, -1),   # _bool (boolean array)
        (1007, -1, -1),   # _int4 (integer array)
        (1009, -1, -1),   # _text (text array)
        (142, -1, -1),    # xml
    ]

    # 生成大量列（75列）
    column_count = 75
    print(f"[调试] 生成包含 {column_count} 列的RowDescription")

    # 开始构造RowDescription数据
    row_desc_data = struct.pack("!H", column_count)  # 列数量

    for i in range(column_count):
        # 生成长列名（确保每个列名都很长）
        base_name = f"very_long_column_name_for_tcp_segmentation_testing_purpose_column_{i:03d}"

        # 根据列索引选择不同的数据类型
        type_info = data_types[i % len(data_types)]
        type_oid, type_size, type_modifier = type_info

        # 为不同类型添加特定的后缀
        type_suffixes = {
            23: "_integer_field",
            20: "_bigint_field",
            25: "_text_field",
            1043: "_varchar_field",
            1700: "_numeric_field",
            1114: "_timestamp_field",
            1184: "_timestamptz_field",
            16: "_boolean_field",
            700: "_float4_field",
            701: "_float8_field",
            1082: "_date_field",
            1083: "_time_field",
            114: "_json_field",
            3802: "_jsonb_field",
            17: "_bytea_field",
            2950: "_uuid_field",
            1000: "_bool_array_field",
            1007: "_int_array_field",
            1009: "_text_array_field",
            142: "_xml_field"
        }

        suffix = type_suffixes.get(type_oid, "_unknown_field")
        column_name = base_name + suffix + "\0"

        # 添加列信息到RowDescription
        # 格式：列名(String) + 表OID(Int32) + 列属性号(Int16) + 数据类型OID(Int32) + 数据类型大小(Int16) + 类型修饰符(Int32) + 格式代码(Int16)
        row_desc_data += column_name.encode('utf-8')  # 列名
        row_desc_data += struct.pack("!I", 16384 + (i % 100))  # 表OID（模拟不同表）
        row_desc_data += struct.pack("!H", i + 1)  # 列属性号（1-based）
        row_desc_data += struct.pack("!I", type_oid)  # 数据类型OID
        row_desc_data += struct.pack("!h", type_size)  # 数据类型大小（有符号，-1表示变长）
        row_desc_data += struct.pack("!i", type_modifier)  # 类型修饰符（有符号）
        row_desc_data += struct.pack("!H", 0)  # 格式代码（0=文本，1=二进制）

    # 创建完整的RowDescription消息
    row_desc_msg = b"T" + struct.pack("!I", len(row_desc_data) + 4) + row_desc_data

    print(f"[调试] RowDescription消息详情:")
    print(f"  - 列数量: {column_count}")
    print(f"  - 消息体大小: {len(row_desc_data)} 字节")
    print(f"  - 完整消息大小: {len(row_desc_msg)} 字节")
    print(f"  - 平均每列大小: {len(row_desc_data) // column_count} 字节")

    return row_desc_msg

def _add_large_table_datarows(packets, ack_num, seq_num):
    """添加大表的DataRow数据（简化版本，只发送一行数据）"""

    # 构造一行示例数据（对应75列）
    data_row_data = struct.pack("!H", 75)  # 75个字段

    # 为每个字段添加示例数据
    sample_values = [
        b"1001",                    # int4
        b"9223372036854775807",     # int8
        b"Sample text data for TCP segmentation testing",  # text
        b"VARCHAR data",            # varchar
        b"12345.67",               # numeric
        b"2023-12-25 10:30:00",    # timestamp
        b"2023-12-25 10:30:00+00", # timestamptz
        b"t",                      # bool
        b"3.14159",                # float4
        b"2.718281828459045",      # float8
        b"2023-12-25",             # date
        b"10:30:00",               # time
        b'{"key": "value", "number": 42}',  # json
        b'{"enhanced": true, "data": [1,2,3]}',  # jsonb
        b"\\x48656c6c6f20576f726c64",  # bytea (hex encoded "Hello World")
        b"550e8400-e29b-41d4-a716-************",  # uuid
        b"{true,false,true}",       # bool array
        b"{1,2,3,4,5}",            # int array
        b'{"text1","text2","text3"}',  # text array
        b"<xml><data>test</data></xml>"  # xml
    ]

    # 为75列生成数据（循环使用示例值）
    for i in range(75):
        sample_value = sample_values[i % len(sample_values)]
        # 为每列添加不同的后缀以区分
        if i >= len(sample_values):
            sample_value = sample_value + f"_col_{i}".encode('utf-8')

        data_row_data += struct.pack("!I", len(sample_value)) + sample_value

    # 创建DataRow消息
    data_row = b"D" + struct.pack("!I", len(data_row_data) + 4) + data_row_data

    s_packet = create_pgsql_packet(SERVER_IP, CLIENT_IP, PGSQL_PORT, client_port, ack_num, seq_num, data_row)
    packets.append(s_packet)

    print(f"[调试] 发送DataRow，大小: {len(data_row)} 字节")

# ============================================================================
# 场景管理和主函数
# ============================================================================

# ============================================================================
# 新增：统一TCP分段丢失场景生成器
# ============================================================================

def generate_all_tcp_segment_loss_scenarios():
    """生成所有TCP分段丢失场景的变体"""
    print("\n=== 生成所有TCP分段丢失场景变体 ===")

    # 定义所有可能的分段丢失组合
    loss_combinations = [
        [1],        # 丢失第1段
        [2],        # 丢失第2段
        [3],        # 丢失第3段
        [1, 2],     # 丢失第1、2段
        [2, 3],     # 丢失第2、3段
        [1, 3],     # 丢失第1、3段
        [1, 2, 3]   # 丢失全部段
    ]

    scenario_types = ['simple_query', 'datarow', 'rowdesc', 'parse']  # 包含已重构的场景

    for scenario_type in scenario_types:
        print(f"\n--- 生成 {scenario_type} 场景的所有分段丢失变体 ---")
        for lost_segments in loss_combinations:
            try:
                generate_tcp_segment_loss_scenario(scenario_type, lost_segments)
            except Exception as e:
                print(f"生成 {scenario_type} 场景 (丢失段{lost_segments}) 时出错: {e}")

    print("\n=== TCP分段丢失场景变体生成完成 ===")

def generate_enhanced_simple_query_scenarios():
    """生成增强的简单查询TCP分段丢失场景"""
    print("\n=== 生成增强的简单查询TCP分段丢失场景 ===")

    loss_combinations = [
        ([1], "first_segment_lost"),
        ([2], "second_segment_lost"),
        ([3], "third_segment_lost"),
        ([1, 2], "first_second_segments_lost"),
        ([2, 3], "second_third_segments_lost"),
        ([1, 3], "first_third_segments_lost"),
        ([1, 2, 3], "all_segments_lost")
    ]

    for lost_segments, description in loss_combinations:
        print(f"\n--- 生成简单查询场景: {description} ---")
        try:
            generate_simple_query_large_message_segment_lost_scenario(lost_segments)
        except Exception as e:
            print(f"生成简单查询场景 {description} 时出错: {e}")

def generate_enhanced_datarow_scenarios():
    """生成增强的DataRow TCP分段丢失场景"""
    print("\n=== 生成增强的DataRow TCP分段丢失场景 ===")

    loss_combinations = [
        ([1], "first_segment_lost"),
        ([2], "second_segment_lost"),
        ([3], "third_segment_lost"),
        ([1, 2], "first_second_segments_lost"),
        ([2, 3], "second_third_segments_lost"),
        ([1, 3], "first_third_segments_lost"),
        ([1, 2, 3], "all_segments_lost")
    ]

    for lost_segments, description in loss_combinations:
        print(f"\n--- 生成DataRow场景: {description} ---")
        try:
            generate_large_datarow_result_set_segment_lost_scenario(lost_segments)
        except Exception as e:
            print(f"生成DataRow场景 {description} 时出错: {e}")

def generate_enhanced_rowdesc_scenarios():
    """生成增强的RowDescription TCP分段丢失场景"""
    print("\n=== 生成增强的RowDescription TCP分段丢失场景 ===")

    loss_combinations = [
        ([1], "first_segment_lost"),
        ([2], "second_segment_lost"),
        ([3], "third_segment_lost"),
        ([1, 2], "first_second_segments_lost"),
        ([2, 3], "second_third_segments_lost"),
        ([1, 3], "first_third_segments_lost"),
        ([1, 2, 3], "all_segments_lost")
    ]

    for lost_segments, description in loss_combinations:
        print(f"\n--- 生成RowDescription场景: {description} ---")
        try:
            generate_large_row_description_segment_lost_scenario(lost_segments)
        except Exception as e:
            print(f"生成RowDescription场景 {description} 时出错: {e}")

def generate_enhanced_parse_scenarios():
    """生成增强的扩展查询Parse TCP分段丢失场景"""
    print("\n=== 生成增强的扩展查询Parse TCP分段丢失场景 ===")

    loss_combinations = [
        ([1], "first_segment_lost"),
        ([2], "second_segment_lost"),
        ([3], "third_segment_lost"),
        ([1, 2], "first_second_segments_lost"),
        ([2, 3], "second_third_segments_lost"),
        ([1, 3], "first_third_segments_lost"),
        ([1, 2, 3], "all_segments_lost")
    ]

    for lost_segments, description in loss_combinations:
        print(f"\n--- 生成扩展查询Parse场景: {description} ---")
        try:
            generate_extended_query_large_parse_segment_lost_scenario(lost_segments)
        except Exception as e:
            print(f"生成扩展查询Parse场景 {description} 时出错: {e}")

def generate_all_loss_scenarios():
    """生成所有丢包测试场景"""
    print("开始生成PostgreSQL协议TCP网络丢包异常测试场景...")
    print("=" * 60)

    # 认证阶段丢包场景
    print("\n【第一阶段：认证阶段丢包场景】")
    generate_startup_message_lost_scenario()
    generate_auth_request_lost_scenario()
    generate_password_response_lost_scenario()
    generate_auth_ok_lost_scenario()
    generate_backend_key_lost_scenario()
    generate_ready_for_query_lost_scenario()

    # 查询执行阶段丢包场景
    print("\n【第二阶段：查询执行和扩展协议丢包场景】")
    generate_query_message_lost_scenario()
    generate_parse_message_lost_scenario()
    generate_bind_message_lost_scenario()
    generate_execute_message_lost_scenario()
    generate_sync_message_lost_scenario()
    generate_large_query_segment_lost_scenario()
    generate_extended_query_large_parse_segment_lost_scenario()  # 新增的测试场景
    generate_extended_query_multiple_large_parse_segment_lost_scenario()  # 新增的多查询测试场景
    generate_simple_query_large_message_segment_lost_scenario()  # 新增的简单查询测试场景
    generate_ssl_request_lost_scenario()
    generate_copy_data_lost_scenario()

    # 响应数据丢包场景
    print("\n【第三阶段：响应数据丢包场景】")
    generate_row_description_lost_scenario()
    generate_data_row_lost_scenario()
    generate_command_complete_lost_scenario()  # 新增的CommandComplete消息丢失场景
    generate_large_datarow_result_set_segment_lost_scenario()  # 新增的大型DataRow结果集TCP分段丢失场景
    generate_large_row_description_segment_lost_scenario()  # 新增的大型RowDescription消息TCP分段丢失场景

    # 连接管理、错误处理、事务控制场景
    print("\n【第四阶段：连接管理、错误处理、事务控制场景】")
    generate_cancel_request_lost_scenario()
    generate_terminate_lost_scenario()
    generate_error_response_lost_scenario()
    generate_transaction_begin_lost_scenario()
    generate_notification_lost_scenario()

    print("\n" + "=" * 60)
    print("所有丢包测试场景生成完成！")
    print(f"输出目录: {LOST_OUTPUT_DIR}")
    print("\n生成的测试文件：")

    # 列出生成的文件
    if os.path.exists(LOST_OUTPUT_DIR):
        files = [f for f in os.listdir(LOST_OUTPUT_DIR) if f.endswith('.pcap')]
        for i, file in enumerate(sorted(files), 1):
            print(f"  {i}. {file}")

    print("\n使用说明：")
    print("1. 使用Wireshark打开pcap文件查看网络异常")
    print("2. 查看对应的_loss_report.txt文件了解丢包详情")
    print("3. 验证PostgreSQL协议解析器的异常检测能力")
    print("4. 测试网络监控工具的故障识别功能")

def generate_specific_scenario(scenario_name):
    """生成特定的丢包测试场景

    Args:
        scenario_name (str): 场景名称
    """
    scenarios = {
        # 认证阶段
        'startup_lost': generate_startup_message_lost_scenario,
        'auth_request_lost': generate_auth_request_lost_scenario,
        'password_lost': generate_password_response_lost_scenario,
        'auth_ok_lost': generate_auth_ok_lost_scenario,
        'backend_key_lost': generate_backend_key_lost_scenario,
        'ready_for_query_lost': generate_ready_for_query_lost_scenario,
        # 查询执行阶段
        'query_lost': generate_query_message_lost_scenario,
        'parse_lost': generate_parse_message_lost_scenario,
        'bind_lost': generate_bind_message_lost_scenario,
        'execute_lost': generate_execute_message_lost_scenario,
        'sync_lost': generate_sync_message_lost_scenario,
        # 响应数据阶段
        'row_desc_lost': generate_row_description_lost_scenario,
        'data_row_lost': generate_data_row_lost_scenario,
        'command_complete_lost': generate_command_complete_lost_scenario,
        'large_datarow_result_set_segment_lost': generate_large_datarow_result_set_segment_lost_scenario,
        'large_row_description_segment_lost': generate_large_row_description_segment_lost_scenario,
        # 大数据传输和高级功能
        'large_query_segment_lost': generate_large_query_segment_lost_scenario,
        'extended_query_large_parse_segment_lost': generate_extended_query_large_parse_segment_lost_scenario,
        'extended_query_multiple_large_parse_segment_lost': generate_extended_query_multiple_large_parse_segment_lost_scenario,
        'simple_query_large_message_segment_lost': generate_simple_query_large_message_segment_lost_scenario,
        'ssl_request_lost': generate_ssl_request_lost_scenario,
        'copy_data_lost': generate_copy_data_lost_scenario,
        # 连接管理
        'cancel_request_lost': generate_cancel_request_lost_scenario,
        'terminate_lost': generate_terminate_lost_scenario,
        # 错误处理
        'error_response_lost': generate_error_response_lost_scenario,
        # 事务控制
        'transaction_begin_lost': generate_transaction_begin_lost_scenario,
        # 通知系统
        'notification_lost': generate_notification_lost_scenario,

        # 新增：增强的TCP分段丢失场景
        'enhanced_simple_query_scenarios': generate_enhanced_simple_query_scenarios,
        'enhanced_datarow_scenarios': generate_enhanced_datarow_scenarios,
        'enhanced_rowdesc_scenarios': generate_enhanced_rowdesc_scenarios,
        'enhanced_parse_scenarios': generate_enhanced_parse_scenarios,
        'all_tcp_segment_loss_scenarios': generate_all_tcp_segment_loss_scenarios,
    }

    if scenario_name in scenarios:
        print(f"生成场景: {scenario_name}")
        scenarios[scenario_name]()
        print(f"场景 {scenario_name} 生成完成")
    else:
        print(f"未知场景: {scenario_name}")
        print("可用场景:")
        for name in scenarios.keys():
            print(f"  - {name}")

def print_usage():
    """打印使用说明"""
    print("PostgreSQL协议TCP网络丢包异常测试场景生成器")
    print("=" * 50)
    print("用法:")
    print("  python3 generate_pgsql_lost_pcap.py [选项]")
    print("")
    print("选项:")
    print("  --all                生成所有丢包测试场景")
    print("  --scenario <name>    生成特定场景")
    print("  --list              列出所有可用场景")
    print("  --help              显示此帮助信息")
    print("")
    print("可用场景:")
    print("  【认证阶段】")
    print("  startup_lost        StartupMessage丢失")
    print("  auth_request_lost   AuthenticationMD5Password丢失")
    print("  password_lost       PasswordMessage丢失")
    print("  auth_ok_lost        AuthenticationOk丢失")
    print("  backend_key_lost    BackendKeyData丢失")
    print("  ready_for_query_lost ReadyForQuery丢失")
    print("  【查询执行阶段】")
    print("  query_lost          Query消息丢失")
    print("  parse_lost          Parse消息丢失")
    print("  bind_lost           Bind消息丢失")
    print("  execute_lost        Execute消息丢失")
    print("  sync_lost           Sync消息丢失")
    print("  【响应数据阶段】")
    print("  row_desc_lost       RowDescription丢失")
    print("  data_row_lost       DataRow部分丢失")
    print("  large_datarow_result_set_segment_lost  大型DataRow结果集TCP分段丢失")
    print("  large_row_description_segment_lost     大型RowDescription消息TCP分段丢失")
    print("  【大数据传输和高级功能】")
    print("  large_query_segment_lost  大查询TCP分段丢失")
    print("  extended_query_large_parse_segment_lost  扩展查询大Parse消息分段丢失")
    print("  extended_query_multiple_large_parse_segment_lost  多个扩展查询大Parse消息分段丢失")
    print("  simple_query_large_message_segment_lost  简单查询大Query消息分段丢失")
    print("  ssl_request_lost    SSL请求丢失")
    print("  copy_data_lost      COPY数据丢失")
    print("  【连接管理、错误处理、事务控制】")
    print("  cancel_request_lost CancelRequest丢失")
    print("  terminate_lost      Terminate消息丢失")
    print("  error_response_lost ErrorResponse丢失")
    print("  transaction_begin_lost 事务BEGIN响应丢失")
    print("  notification_lost   通知消息丢失")
    print("  【增强的TCP分段丢失场景】")
    print("  enhanced_simple_query_scenarios  增强的简单查询TCP分段丢失场景（所有分段组合）")
    print("  enhanced_datarow_scenarios       增强的DataRow TCP分段丢失场景（所有分段组合）")
    print("  enhanced_rowdesc_scenarios       增强的RowDescription TCP分段丢失场景（所有分段组合）")
    print("  enhanced_parse_scenarios         增强的扩展查询Parse TCP分段丢失场景（所有分段组合）")
    print("  all_tcp_segment_loss_scenarios   所有TCP分段丢失场景变体")
    print("")
    print("示例:")
    print("  python3 generate_pgsql_lost_pcap.py --all")
    print("  python3 generate_pgsql_lost_pcap.py --scenario startup_lost")
    print("  python3 generate_pgsql_lost_pcap.py --scenario enhanced_simple_query_scenarios")
    print("  python3 generate_pgsql_lost_pcap.py --scenario enhanced_datarow_scenarios")
    print("  python3 generate_pgsql_lost_pcap.py --scenario enhanced_rowdesc_scenarios")
    print("  python3 generate_pgsql_lost_pcap.py --scenario enhanced_parse_scenarios")

if __name__ == "__main__":
    import sys

    if len(sys.argv) == 1 or "--help" in sys.argv:
        print_usage()
    elif "--all" in sys.argv:
        generate_all_loss_scenarios()
    elif "--list" in sys.argv:
        scenarios = [
            # 认证阶段
            'startup_lost', 'auth_request_lost', 'password_lost', 'auth_ok_lost',
            'backend_key_lost', 'ready_for_query_lost',
            # 查询执行阶段
            'query_lost', 'parse_lost', 'bind_lost', 'execute_lost', 'sync_lost',
            # 响应数据阶段
            'row_desc_lost', 'data_row_lost', 'command_complete_lost', 'large_datarow_result_set_segment_lost', 'large_row_description_segment_lost',
            # 大数据传输和高级功能
            'large_query_segment_lost', 'extended_query_large_parse_segment_lost',
            'extended_query_multiple_large_parse_segment_lost', 'simple_query_large_message_segment_lost',
            'ssl_request_lost', 'copy_data_lost',
            # 连接管理、错误处理、事务控制
            'cancel_request_lost', 'terminate_lost', 'error_response_lost',
            'transaction_begin_lost', 'notification_lost',
            # 增强的TCP分段丢失场景
            'enhanced_simple_query_scenarios', 'enhanced_datarow_scenarios', 'enhanced_rowdesc_scenarios', 'enhanced_parse_scenarios', 'all_tcp_segment_loss_scenarios'
        ]
        print("可用的丢包测试场景:")
        for i, scenario in enumerate(scenarios, 1):
            print(f"  {i}. {scenario}")
    elif "--scenario" in sys.argv:
        try:
            idx = sys.argv.index("--scenario")
            if idx + 1 < len(sys.argv):
                scenario_name = sys.argv[idx + 1]
                generate_specific_scenario(scenario_name)
            else:
                print("错误: --scenario 选项需要指定场景名称")
                print_usage()
        except ValueError:
            print("错误: 无效的命令行参数")
            print_usage()
    else:
        print("错误: 无效的命令行参数")
        print_usage()
