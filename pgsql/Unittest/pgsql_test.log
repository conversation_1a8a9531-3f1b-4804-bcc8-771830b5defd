2025-07-31 12:01:09,852 - INFO - 执行文件扫描...
2025-07-31 12:01:09,853 - INFO - 开始扫描pcap文件...
2025-07-31 12:01:09,853 - INFO - 在 pgsql/pgsql_pcaps 中找到 37 个pcap文件
2025-07-31 12:01:09,854 - INFO - 在 pgsql/pgsql_abnormal_pcaps 中找到 11 个pcap文件
2025-07-31 12:01:09,854 - INFO - 总共找到 48 个pcap文件
2025-07-31 12:01:59,640 - INFO - 测试单个文件: pgsql_simple_query.pcap
2025-07-31 12:01:59,640 - INFO - 启动测试: pgsql_simple_query.pcap
2025-07-31 12:01:59,648 - ERROR - 启动测试请求失败: 502 Server Error: Bad Gateway for url: http://localhost:8000/api/v1/test/pcap-replay
2025-07-31 14:17:26,499 - INFO - 测试单个文件: pgsql_simple_query.pcap
2025-07-31 14:17:26,500 - INFO - 启动测试: pgsql_simple_query.pcap
2025-07-31 14:17:26,525 - INFO - 测试任务已启动，任务ID: fb0f1889-b8f9-49b3-86aa-4579b2a08487
2025-07-31 14:17:37,531 - INFO - 任务完成: fb0f1889-b8f9-49b3-86aa-4579b2a08487
2025-07-31 14:19:58,700 - INFO - 测试单个文件: pgsql_authentication.pcap
2025-07-31 14:19:58,702 - INFO - 启动测试: pgsql_authentication.pcap
2025-07-31 14:19:58,730 - INFO - 测试任务已启动，任务ID: 60b66519-694a-46ba-93c7-5e588a3668cb
2025-07-31 14:20:09,706 - INFO - 任务完成: 60b66519-694a-46ba-93c7-5e588a3668cb
2025-07-31 14:20:34,285 - INFO - 测试单个文件: pgsql_cleartext_authentication.pcap
2025-07-31 14:20:34,285 - INFO - 启动测试: pgsql_cleartext_authentication.pcap
2025-07-31 14:20:34,304 - INFO - 测试任务已启动，任务ID: e24ca474-00c9-40fc-96d7-c8cd06605c1b
2025-07-31 14:20:45,378 - INFO - 任务完成: e24ca474-00c9-40fc-96d7-c8cd06605c1b
2025-07-31 14:20:53,917 - INFO - 测试单个文件: pgsql_sasl_authentication.pcap
2025-07-31 14:20:53,917 - INFO - 启动测试: pgsql_sasl_authentication.pcap
2025-07-31 14:20:53,942 - INFO - 测试任务已启动，任务ID: 3955cb47-def4-4ad1-a4a0-9c356a9559bc
2025-07-31 14:21:04,891 - INFO - 任务完成: 3955cb47-def4-4ad1-a4a0-9c356a9559bc
2025-07-31 14:21:20,291 - INFO - 测试单个文件: pgsql_ssl_request_supported.pcap
2025-07-31 14:21:20,291 - INFO - 启动测试: pgsql_ssl_request_supported.pcap
2025-07-31 14:21:20,309 - INFO - 测试任务已启动，任务ID: 33d2c9de-7852-48fb-9ef3-06ee7eda79b0
2025-07-31 14:21:31,292 - INFO - 任务完成: 33d2c9de-7852-48fb-9ef3-06ee7eda79b0
2025-07-31 14:24:59,227 - INFO - 测试单个文件: pgsql_ssl_request_not_supported.pcap
2025-07-31 14:24:59,228 - INFO - 启动测试: pgsql_ssl_request_not_supported.pcap
2025-07-31 14:24:59,268 - INFO - 测试任务已启动，任务ID: abb92f63-2328-4bb4-addf-cb670b19765a
2025-07-31 14:25:10,279 - INFO - 任务完成: abb92f63-2328-4bb4-addf-cb670b19765a
2025-07-31 14:25:32,446 - INFO - 测试单个文件: pgsql_multi_query.pcap
2025-07-31 14:25:32,447 - INFO - 启动测试: pgsql_multi_query.pcap
2025-07-31 14:25:32,471 - INFO - 测试任务已启动，任务ID: cccebe43-f76f-460a-8d6b-f61bdfa9b43a
2025-07-31 14:25:43,491 - INFO - 任务完成: cccebe43-f76f-460a-8d6b-f61bdfa9b43a
2025-07-31 14:25:51,880 - INFO - 测试单个文件: pgsql_notification.pcap
2025-07-31 14:25:51,880 - INFO - 启动测试: pgsql_notification.pcap
2025-07-31 14:25:51,944 - INFO - 测试任务已启动，任务ID: e40e7168-e731-4c4e-bf92-11d1894b24e7
2025-07-31 14:26:02,960 - INFO - 任务完成: e40e7168-e731-4c4e-bf92-11d1894b24e7
2025-07-31 14:26:12,152 - INFO - 执行文件扫描...
2025-07-31 14:26:12,152 - INFO - 开始扫描pcap文件...
2025-07-31 14:26:12,152 - WARNING - 目录不存在: pgsql/pgsql_pcaps
2025-07-31 14:26:12,152 - WARNING - 目录不存在: pgsql/pgsql_abnormal_pcaps
2025-07-31 14:26:12,152 - INFO - 总共找到 0 个pcap文件
2025-07-31 14:26:35,390 - INFO - 执行文件扫描...
2025-07-31 14:26:35,390 - INFO - 开始扫描pcap文件...
2025-07-31 14:26:35,391 - INFO - 在 ../pgsql_pcaps 中找到 37 个pcap文件
2025-07-31 14:26:35,392 - INFO - 在 ../pgsql_abnormal_pcaps 中找到 11 个pcap文件
2025-07-31 14:26:35,392 - INFO - 总共找到 48 个pcap文件
2025-07-31 14:29:07,602 - INFO - 测试单个文件: pgsql_describe_statement.pcap
2025-07-31 14:29:07,602 - INFO - 启动测试: pgsql_describe_statement.pcap
2025-07-31 14:29:07,637 - INFO - 测试任务已启动，任务ID: 14c28548-0133-4843-816d-1cbbe8b1f879
2025-07-31 14:29:18,614 - INFO - 任务完成: 14c28548-0133-4843-816d-1cbbe8b1f879
2025-07-31 14:29:20,868 - INFO - 测试单个文件: pgsql_describe_portal.pcap
2025-07-31 14:29:20,869 - INFO - 启动测试: pgsql_describe_portal.pcap
2025-07-31 14:29:20,883 - INFO - 测试任务已启动，任务ID: 0aa5659b-3cf3-4c79-a48c-5c33c06f276c
2025-07-31 14:29:31,852 - INFO - 任务完成: 0aa5659b-3cf3-4c79-a48c-5c33c06f276c
2025-07-31 14:29:34,095 - INFO - 测试单个文件: pgsql_function_call.pcap
2025-07-31 14:29:34,096 - INFO - 启动测试: pgsql_function_call.pcap
2025-07-31 14:29:34,112 - INFO - 测试任务已启动，任务ID: c12581c0-8669-47e2-88c3-a206abdee5f0
2025-07-31 14:29:45,147 - INFO - 任务完成: c12581c0-8669-47e2-88c3-a206abdee5f0
2025-07-31 14:29:47,396 - INFO - 测试单个文件: pgsql_batch_operations.pcap
2025-07-31 14:29:47,396 - INFO - 启动测试: pgsql_batch_operations.pcap
2025-07-31 14:29:47,416 - INFO - 测试任务已启动，任务ID: 8309a598-9235-435c-aec6-28e5e60864eb
2025-07-31 14:29:58,488 - INFO - 任务完成: 8309a598-9235-435c-aec6-28e5e60864eb
2025-07-31 14:30:00,742 - INFO - 测试单个文件: pgsql_cancel_query.pcap
2025-07-31 14:30:00,742 - INFO - 启动测试: pgsql_cancel_query.pcap
2025-07-31 14:30:00,766 - INFO - 测试任务已启动，任务ID: d9950ea1-588b-4e7c-aed8-3c758c36b83b
2025-07-31 14:30:11,893 - INFO - 任务完成: d9950ea1-588b-4e7c-aed8-3c758c36b83b
2025-07-31 14:30:14,131 - INFO - 测试单个文件: pgsql_multiple_data_types.pcap
2025-07-31 14:30:14,131 - INFO - 启动测试: pgsql_multiple_data_types.pcap
2025-07-31 14:30:14,146 - INFO - 测试任务已启动，任务ID: 051b414a-f312-4a22-86d5-359936c2d24b
2025-07-31 14:30:25,203 - INFO - 任务完成: 051b414a-f312-4a22-86d5-359936c2d24b
2025-07-31 14:30:27,450 - INFO - 测试单个文件: pgsql_binary_copy.pcap
2025-07-31 14:30:27,451 - INFO - 启动测试: pgsql_binary_copy.pcap
2025-07-31 14:30:27,472 - INFO - 测试任务已启动，任务ID: b7648cbb-1c0e-4ccd-8dad-c60cbfa6d08f
2025-07-31 14:30:38,702 - INFO - 任务完成: b7648cbb-1c0e-4ccd-8dad-c60cbfa6d08f
2025-07-31 14:30:40,948 - INFO - 测试单个文件: pgsql_copy_to_stdout.pcap
2025-07-31 14:30:40,949 - INFO - 启动测试: pgsql_copy_to_stdout.pcap
2025-07-31 14:30:40,966 - INFO - 测试任务已启动，任务ID: c5a02d49-e25c-403c-8e6d-6889739e0f64
2025-07-31 14:30:52,022 - INFO - 任务完成: c5a02d49-e25c-403c-8e6d-6889739e0f64
2025-07-31 14:31:32,957 - INFO - 测试单个文件: pgsql_describe_statement.pcap
2025-07-31 14:31:32,957 - INFO - 启动测试: pgsql_describe_statement.pcap
2025-07-31 14:31:32,995 - INFO - 测试任务已启动，任务ID: fd38410c-7461-4fb0-9388-4f91418acc0c
2025-07-31 14:31:43,995 - INFO - 任务完成: fd38410c-7461-4fb0-9388-4f91418acc0c
2025-07-31 14:31:46,244 - INFO - 测试单个文件: pgsql_describe_portal.pcap
2025-07-31 14:31:46,244 - INFO - 启动测试: pgsql_describe_portal.pcap
2025-07-31 14:31:46,268 - INFO - 测试任务已启动，任务ID: ff660773-2f3e-4478-be13-98834ea8a84b
2025-07-31 14:31:57,251 - INFO - 任务完成: ff660773-2f3e-4478-be13-98834ea8a84b
2025-07-31 14:31:59,482 - INFO - 测试单个文件: pgsql_function_call.pcap
2025-07-31 14:31:59,482 - INFO - 启动测试: pgsql_function_call.pcap
2025-07-31 14:31:59,497 - INFO - 测试任务已启动，任务ID: 6d61c433-3efe-4f71-a8c9-f6ebcb1b641e
2025-07-31 14:32:10,471 - INFO - 任务完成: 6d61c433-3efe-4f71-a8c9-f6ebcb1b641e
2025-07-31 14:32:12,740 - INFO - 测试单个文件: pgsql_batch_operations.pcap
2025-07-31 14:32:12,741 - INFO - 启动测试: pgsql_batch_operations.pcap
2025-07-31 14:32:12,761 - INFO - 测试任务已启动，任务ID: 493704f7-5009-4f82-8961-1984bc209299
2025-07-31 14:32:23,744 - INFO - 任务完成: 493704f7-5009-4f82-8961-1984bc209299
2025-07-31 14:32:26,029 - INFO - 测试单个文件: pgsql_cancel_query.pcap
2025-07-31 14:32:26,029 - INFO - 启动测试: pgsql_cancel_query.pcap
2025-07-31 14:32:26,048 - INFO - 测试任务已启动，任务ID: 49d04816-9044-48ea-940b-4639cac7585e
2025-07-31 14:32:37,087 - INFO - 任务完成: 49d04816-9044-48ea-940b-4639cac7585e
2025-07-31 14:32:39,352 - INFO - 测试单个文件: pgsql_multiple_data_types.pcap
2025-07-31 14:32:39,352 - INFO - 启动测试: pgsql_multiple_data_types.pcap
2025-07-31 14:32:39,373 - INFO - 测试任务已启动，任务ID: 1b3dfc4a-50f8-4d52-9e65-8ef19d9b8fc3
2025-07-31 14:32:50,338 - INFO - 任务完成: 1b3dfc4a-50f8-4d52-9e65-8ef19d9b8fc3
2025-07-31 14:32:52,815 - INFO - 测试单个文件: pgsql_binary_copy.pcap
2025-07-31 14:32:52,815 - INFO - 启动测试: pgsql_binary_copy.pcap
2025-07-31 14:32:52,836 - INFO - 测试任务已启动，任务ID: e470e2d3-b650-4adf-81a1-0a864350331d
2025-07-31 14:33:03,818 - INFO - 任务完成: e470e2d3-b650-4adf-81a1-0a864350331d
2025-07-31 14:33:06,072 - INFO - 测试单个文件: pgsql_copy_to_stdout.pcap
2025-07-31 14:33:06,073 - INFO - 启动测试: pgsql_copy_to_stdout.pcap
2025-07-31 14:33:06,107 - INFO - 测试任务已启动，任务ID: 839057de-c8d3-4129-b49b-e4c9883df846
2025-07-31 14:33:17,212 - INFO - 任务完成: 839057de-c8d3-4129-b49b-e4c9883df846
2025-07-31 14:33:17,426 - INFO - 测试单个文件: pgsql_close_statement_portal.pcap
2025-07-31 14:33:17,426 - INFO - 启动测试: pgsql_close_statement_portal.pcap
2025-07-31 14:33:17,458 - INFO - 测试任务已启动，任务ID: 6b99826f-eb18-4884-b9e6-19971126d591
2025-07-31 14:33:28,399 - INFO - 任务完成: 6b99826f-eb18-4884-b9e6-19971126d591
2025-07-31 14:33:30,646 - INFO - 测试单个文件: pgsql_flush.pcap
2025-07-31 14:33:30,646 - INFO - 启动测试: pgsql_flush.pcap
2025-07-31 14:33:30,665 - INFO - 测试任务已启动，任务ID: c038b6cd-6df3-49d8-aa43-d4c0ed0513e9
2025-07-31 14:33:41,654 - INFO - 任务完成: c038b6cd-6df3-49d8-aa43-d4c0ed0513e9
2025-07-31 14:33:43,987 - INFO - 测试单个文件: pgsql_character_encoding.pcap
2025-07-31 14:33:43,987 - INFO - 启动测试: pgsql_character_encoding.pcap
2025-07-31 14:33:44,010 - INFO - 测试任务已启动，任务ID: b3d3d644-8aa5-4b16-8243-c61ecf951c8e
2025-07-31 14:33:54,941 - INFO - 任务完成: b3d3d644-8aa5-4b16-8243-c61ecf951c8e
2025-07-31 14:33:57,205 - INFO - 测试单个文件: pgsql_multi_parameter.pcap
2025-07-31 14:33:57,205 - INFO - 启动测试: pgsql_multi_parameter.pcap
2025-07-31 14:33:57,225 - INFO - 测试任务已启动，任务ID: 0b668158-649d-4447-99ed-4fb7f7506c72
2025-07-31 14:34:08,203 - INFO - 任务完成: 0b668158-649d-4447-99ed-4fb7f7506c72
2025-07-31 14:34:10,425 - INFO - 测试单个文件: pgsql_pipelined_queries.pcap
2025-07-31 14:34:10,425 - INFO - 启动测试: pgsql_pipelined_queries.pcap
2025-07-31 14:34:10,438 - INFO - 测试任务已启动，任务ID: 02cc338d-aedb-471b-8a03-497e74592b72
2025-07-31 14:34:21,414 - INFO - 任务完成: 02cc338d-aedb-471b-8a03-497e74592b72
2025-07-31 14:34:23,642 - INFO - 测试单个文件: pgsql_nodata.pcap
2025-07-31 14:34:23,643 - INFO - 启动测试: pgsql_nodata.pcap
2025-07-31 14:34:23,680 - INFO - 测试任务已启动，任务ID: 678ff324-57b7-4650-9914-9e551f29b7e4
2025-07-31 14:34:34,631 - INFO - 任务完成: 678ff324-57b7-4650-9914-9e551f29b7e4
2025-07-31 14:34:36,855 - INFO - 测试单个文件: pgsql_notice_response.pcap
2025-07-31 14:34:36,855 - INFO - 启动测试: pgsql_notice_response.pcap
2025-07-31 14:34:36,875 - INFO - 测试任务已启动，任务ID: 67bff236-fc27-4c93-b225-e7f7c60c921d
2025-07-31 14:34:47,841 - INFO - 任务完成: 67bff236-fc27-4c93-b225-e7f7c60c921d
2025-07-31 14:34:50,061 - INFO - 测试单个文件: pgsql_auth_with_copy.pcap
2025-07-31 14:34:50,061 - INFO - 启动测试: pgsql_auth_with_copy.pcap
2025-07-31 14:34:50,102 - INFO - 测试任务已启动，任务ID: 7ec94c8a-9d4b-4611-8495-19cf645f0cff
2025-07-31 14:35:01,022 - INFO - 任务完成: 7ec94c8a-9d4b-4611-8495-19cf645f0cff
2025-07-31 14:35:01,274 - INFO - 测试单个文件: pgsql_constraint_violation.pcap
2025-07-31 14:35:01,274 - INFO - 启动测试: pgsql_constraint_violation.pcap
2025-07-31 14:35:01,300 - INFO - 测试任务已启动，任务ID: 621a2b6d-813a-4b6f-9acc-93fb8183f12e
2025-07-31 14:35:12,418 - INFO - 任务完成: 621a2b6d-813a-4b6f-9acc-93fb8183f12e
2025-07-31 14:35:14,666 - INFO - 测试单个文件: pgsql_copy_fail.pcap
2025-07-31 14:35:14,666 - INFO - 启动测试: pgsql_copy_fail.pcap
2025-07-31 14:35:14,691 - INFO - 测试任务已启动，任务ID: a6b16b21-b682-4912-8a2d-73faf7935f60
2025-07-31 14:35:25,632 - INFO - 任务完成: a6b16b21-b682-4912-8a2d-73faf7935f60
2025-07-31 14:35:27,897 - INFO - 测试单个文件: pgsql_fatal_error.pcap
2025-07-31 14:35:27,897 - INFO - 启动测试: pgsql_fatal_error.pcap
2025-07-31 14:35:27,910 - INFO - 测试任务已启动，任务ID: c1790b61-d0ff-4dac-a46f-68dc185c0b13
2025-07-31 14:35:38,834 - INFO - 任务完成: c1790b61-d0ff-4dac-a46f-68dc185c0b13
2025-07-31 14:35:41,089 - INFO - 测试单个文件: pgsql_function_call_error.pcap
2025-07-31 14:35:41,089 - INFO - 启动测试: pgsql_function_call_error.pcap
2025-07-31 14:35:41,110 - INFO - 测试任务已启动，任务ID: 46c479df-b381-4eff-bf2f-ad70172d4ec9
2025-07-31 14:35:52,152 - INFO - 任务完成: 46c479df-b381-4eff-bf2f-ad70172d4ec9
2025-07-31 14:35:54,405 - INFO - 测试单个文件: pgsql_panic_error.pcap
2025-07-31 14:35:54,405 - INFO - 启动测试: pgsql_panic_error.pcap
2025-07-31 14:35:54,424 - INFO - 测试任务已启动，任务ID: f8b2d72c-56f7-4b2d-ba16-89716a85b38b
2025-07-31 14:36:05,396 - INFO - 任务完成: f8b2d72c-56f7-4b2d-ba16-89716a85b38b
2025-07-31 14:36:07,666 - INFO - 测试单个文件: pgsql_pipelined_error_handling.pcap
2025-07-31 14:36:07,666 - INFO - 启动测试: pgsql_pipelined_error_handling.pcap
2025-07-31 14:36:07,682 - INFO - 测试任务已启动，任务ID: ebe4e29d-286a-443f-b9ce-91bdcc2213c5
2025-07-31 14:36:18,668 - INFO - 任务完成: ebe4e29d-286a-443f-b9ce-91bdcc2213c5
2025-07-31 14:36:20,919 - INFO - 测试单个文件: pgsql_syntax_error.pcap
2025-07-31 14:36:20,919 - INFO - 启动测试: pgsql_syntax_error.pcap
2025-07-31 14:36:20,933 - INFO - 测试任务已启动，任务ID: 7fdbcbcb-acb8-42ab-bd95-5ee6b3e4106c
2025-07-31 14:36:31,858 - INFO - 任务完成: 7fdbcbcb-acb8-42ab-bd95-5ee6b3e4106c
2025-07-31 14:36:34,096 - INFO - 测试单个文件: pgsql_type_error.pcap
2025-07-31 14:36:34,096 - INFO - 启动测试: pgsql_type_error.pcap
2025-07-31 14:36:34,117 - INFO - 测试任务已启动，任务ID: 6e334bfe-d7ae-4d2a-a20f-672f1c010495
2025-07-31 14:36:45,099 - INFO - 任务完成: 6e334bfe-d7ae-4d2a-a20f-672f1c010495
2025-07-31 14:36:47,341 - INFO - 测试单个文件: pgsql_user_not_exist.pcap
2025-07-31 14:36:47,341 - INFO - 启动测试: pgsql_user_not_exist.pcap
2025-07-31 14:36:47,357 - INFO - 测试任务已启动，任务ID: ee2faeb1-fec7-4b46-b281-a53ae9e6fa8c
2025-07-31 14:36:58,300 - INFO - 任务完成: ee2faeb1-fec7-4b46-b281-a53ae9e6fa8c
2025-07-31 14:52:46,874 - INFO - 执行文件扫描...
2025-07-31 14:52:46,874 - INFO - 开始扫描pcap文件...
2025-07-31 14:52:46,876 - INFO - 在 ../pgsql_pcaps 中找到 37 个pcap文件
2025-07-31 14:52:46,878 - INFO - 在 ../pgsql_abnormal_pcaps 中找到 11 个pcap文件
2025-07-31 14:52:46,878 - INFO - 总共找到 48 个pcap文件
2025-07-31 14:53:29,652 - INFO - 测试单个文件: pgsql_max_message_length.pcap
2025-07-31 14:53:29,652 - INFO - 启动测试: pgsql_max_message_length.pcap
2025-07-31 14:53:29,681 - INFO - 测试任务已启动，任务ID: ec7f7ac6-eaf4-4696-a6e3-3df2e72bfdd3
2025-07-31 14:53:40,658 - INFO - 任务完成: ec7f7ac6-eaf4-4696-a6e3-3df2e72bfdd3
2025-07-31 14:53:42,887 - INFO - 测试单个文件: pgsql_max_parameters.pcap
2025-07-31 14:53:42,887 - INFO - 启动测试: pgsql_max_parameters.pcap
2025-07-31 14:53:42,905 - INFO - 测试任务已启动，任务ID: 703db1f9-9e40-4fac-ba00-063c397d4e2b
2025-07-31 14:53:53,894 - INFO - 任务完成: 703db1f9-9e40-4fac-ba00-063c397d4e2b
2025-07-31 14:53:56,097 - INFO - 测试单个文件: pgsql_max_string_length.pcap
2025-07-31 14:53:56,097 - INFO - 启动测试: pgsql_max_string_length.pcap
2025-07-31 14:53:56,119 - INFO - 测试任务已启动，任务ID: 95af93a2-6dca-485b-ac40-03771fcc5ef5
2025-07-31 14:54:07,090 - INFO - 任务完成: 95af93a2-6dca-485b-ac40-03771fcc5ef5
2025-07-31 14:54:09,340 - INFO - 测试单个文件: pgsql_empty_data_handling.pcap
2025-07-31 14:54:09,340 - INFO - 启动测试: pgsql_empty_data_handling.pcap
2025-07-31 14:54:09,357 - INFO - 测试任务已启动，任务ID: d3799855-73eb-44df-86ed-242215879dd4
2025-07-31 14:54:20,326 - INFO - 任务完成: d3799855-73eb-44df-86ed-242215879dd4
2025-07-31 14:54:22,590 - INFO - 测试单个文件: pgsql_start_replication.pcap
2025-07-31 14:54:22,590 - INFO - 启动测试: pgsql_start_replication.pcap
2025-07-31 14:54:22,624 - INFO - 测试任务已启动，任务ID: d9966b4e-b13f-4161-9062-6e83ac2e710b
2025-07-31 14:54:33,578 - INFO - 任务完成: d9966b4e-b13f-4161-9062-6e83ac2e710b
2025-07-31 14:54:35,988 - INFO - 测试单个文件: pgsql_wal_streaming.pcap
2025-07-31 14:54:35,988 - INFO - 启动测试: pgsql_wal_streaming.pcap
2025-07-31 14:54:36,010 - INFO - 测试任务已启动，任务ID: 488263f8-795d-4be8-8963-698f6521daf0
2025-07-31 14:54:46,909 - INFO - 任务完成: 488263f8-795d-4be8-8963-698f6521daf0
2025-07-31 14:54:49,137 - INFO - 测试单个文件: pgsql_replication_feedback.pcap
2025-07-31 14:54:49,137 - INFO - 启动测试: pgsql_replication_feedback.pcap
2025-07-31 14:54:49,157 - INFO - 测试任务已启动，任务ID: 2147d59e-b9a9-46da-90a9-507e56ff06cc
2025-07-31 14:55:00,100 - INFO - 任务完成: 2147d59e-b9a9-46da-90a9-507e56ff06cc
2025-07-31 14:55:02,293 - INFO - 测试单个文件: pgsql_sync_separated_transactions.pcap
2025-07-31 14:55:02,293 - INFO - 启动测试: pgsql_sync_separated_transactions.pcap
2025-07-31 14:55:02,309 - INFO - 测试任务已启动，任务ID: 54159847-2740-4cb7-9b1d-e3667f89a1f1
2025-07-31 14:55:13,214 - INFO - 任务完成: 54159847-2740-4cb7-9b1d-e3667f89a1f1
2025-08-01 17:51:17,700 - INFO - 重新测试12个无JSON事件文件...
2025-08-01 17:51:17,701 - INFO - 开始测试指定的12个文件...
2025-08-01 17:51:17,701 - INFO - 测试进度: 1/12 - pgsql_max_message_length.pcap
2025-08-01 17:51:17,701 - INFO - 启动测试: pgsql_max_message_length.pcap
2025-08-01 17:51:17,704 - DEBUG - Starting new HTTP connection (1): 127.0.0.1:7890
2025-08-01 17:51:17,727 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 260
2025-08-01 17:51:17,728 - INFO - 测试任务已启动，任务ID: bb430c6f-c5ed-4e6b-b56e-93de9b50d7fc
2025-08-01 17:51:17,729 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:51:18,776 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/bb430c6f-c5ed-4e6b-b56e-93de9b50d7fc HTTP/1.1" 200 228
2025-08-01 17:51:18,776 - DEBUG - 任务进行中: bb430c6f-c5ed-4e6b-b56e-93de9b50d7fc, 状态: running
2025-08-01 17:51:23,783 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:51:23,796 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/bb430c6f-c5ed-4e6b-b56e-93de9b50d7fc HTTP/1.1" 200 228
2025-08-01 17:51:23,798 - DEBUG - 任务进行中: bb430c6f-c5ed-4e6b-b56e-93de9b50d7fc, 状态: running
2025-08-01 17:51:28,802 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:51:28,815 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/bb430c6f-c5ed-4e6b-b56e-93de9b50d7fc HTTP/1.1" 200 228
2025-08-01 17:51:28,817 - DEBUG - 任务进行中: bb430c6f-c5ed-4e6b-b56e-93de9b50d7fc, 状态: running
2025-08-01 17:51:33,822 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:51:33,838 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/bb430c6f-c5ed-4e6b-b56e-93de9b50d7fc HTTP/1.1" 200 1116
2025-08-01 17:51:33,839 - INFO - 任务完成: bb430c6f-c5ed-4e6b-b56e-93de9b50d7fc
2025-08-01 17:51:33,839 - INFO -   ❌ pgsql_max_message_length.pcap: NO_JSON_EVENTS
2025-08-01 17:51:34,844 - INFO - 测试进度: 2/12 - pgsql_max_parameters.pcap
2025-08-01 17:51:34,844 - INFO - 启动测试: pgsql_max_parameters.pcap
2025-08-01 17:51:34,846 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:51:34,860 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 256
2025-08-01 17:51:34,861 - INFO - 测试任务已启动，任务ID: 3360bee5-a377-4e1b-a184-2b458dad13c9
2025-08-01 17:51:34,862 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:51:35,849 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/3360bee5-a377-4e1b-a184-2b458dad13c9 HTTP/1.1" 200 228
2025-08-01 17:51:35,851 - DEBUG - 任务进行中: 3360bee5-a377-4e1b-a184-2b458dad13c9, 状态: running
2025-08-01 17:51:40,857 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:51:40,869 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/3360bee5-a377-4e1b-a184-2b458dad13c9 HTTP/1.1" 200 228
2025-08-01 17:51:40,869 - DEBUG - 任务进行中: 3360bee5-a377-4e1b-a184-2b458dad13c9, 状态: running
2025-08-01 17:51:45,875 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:51:45,908 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/3360bee5-a377-4e1b-a184-2b458dad13c9 HTTP/1.1" 200 228
2025-08-01 17:51:45,909 - DEBUG - 任务进行中: 3360bee5-a377-4e1b-a184-2b458dad13c9, 状态: running
2025-08-01 17:51:50,915 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:51:50,931 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/3360bee5-a377-4e1b-a184-2b458dad13c9 HTTP/1.1" 200 1208
2025-08-01 17:51:50,932 - INFO - 任务完成: 3360bee5-a377-4e1b-a184-2b458dad13c9
2025-08-01 17:51:50,932 - INFO -   ❌ pgsql_max_parameters.pcap: NO_JSON_EVENTS
2025-08-01 17:51:51,933 - INFO - 测试进度: 3/12 - pgsql_max_string_length.pcap
2025-08-01 17:51:51,934 - INFO - 启动测试: pgsql_max_string_length.pcap
2025-08-01 17:51:51,935 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:51:51,959 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 259
2025-08-01 17:51:51,959 - INFO - 测试任务已启动，任务ID: 933582c4-1449-4546-8b66-2847580fdba6
2025-08-01 17:51:51,961 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:51:52,970 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/933582c4-1449-4546-8b66-2847580fdba6 HTTP/1.1" 200 228
2025-08-01 17:51:52,971 - DEBUG - 任务进行中: 933582c4-1449-4546-8b66-2847580fdba6, 状态: running
2025-08-01 17:51:57,976 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:51:58,005 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/933582c4-1449-4546-8b66-2847580fdba6 HTTP/1.1" 200 228
2025-08-01 17:51:58,005 - DEBUG - 任务进行中: 933582c4-1449-4546-8b66-2847580fdba6, 状态: running
2025-08-01 17:52:03,010 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:52:03,027 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/933582c4-1449-4546-8b66-2847580fdba6 HTTP/1.1" 200 228
2025-08-01 17:52:03,028 - DEBUG - 任务进行中: 933582c4-1449-4546-8b66-2847580fdba6, 状态: running
2025-08-01 17:52:08,032 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:52:08,072 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/933582c4-1449-4546-8b66-2847580fdba6 HTTP/1.1" 200 1208
2025-08-01 17:52:08,073 - INFO - 任务完成: 933582c4-1449-4546-8b66-2847580fdba6
2025-08-01 17:52:08,073 - INFO -   ❌ pgsql_max_string_length.pcap: NO_JSON_EVENTS
2025-08-01 17:52:09,075 - INFO - 测试进度: 4/12 - pgsql_empty_data_handling.pcap
2025-08-01 17:52:09,075 - INFO - 启动测试: pgsql_empty_data_handling.pcap
2025-08-01 17:52:09,077 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:52:09,168 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 261
2025-08-01 17:52:09,170 - INFO - 测试任务已启动，任务ID: e8fb2f0b-731e-4fd2-85d3-ac03469d4551
2025-08-01 17:52:09,172 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:52:10,272 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/e8fb2f0b-731e-4fd2-85d3-ac03469d4551 HTTP/1.1" 200 228
2025-08-01 17:52:10,273 - DEBUG - 任务进行中: e8fb2f0b-731e-4fd2-85d3-ac03469d4551, 状态: running
2025-08-01 17:52:15,280 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:52:15,298 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/e8fb2f0b-731e-4fd2-85d3-ac03469d4551 HTTP/1.1" 200 228
2025-08-01 17:52:15,299 - DEBUG - 任务进行中: e8fb2f0b-731e-4fd2-85d3-ac03469d4551, 状态: running
2025-08-01 17:52:20,304 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:52:20,333 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/e8fb2f0b-731e-4fd2-85d3-ac03469d4551 HTTP/1.1" 200 228
2025-08-01 17:52:20,334 - DEBUG - 任务进行中: e8fb2f0b-731e-4fd2-85d3-ac03469d4551, 状态: running
2025-08-01 17:52:25,339 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:52:25,369 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/e8fb2f0b-731e-4fd2-85d3-ac03469d4551 HTTP/1.1" 200 1912
2025-08-01 17:52:25,370 - INFO - 任务完成: e8fb2f0b-731e-4fd2-85d3-ac03469d4551
2025-08-01 17:52:25,370 - INFO -   ✅ pgsql_empty_data_handling.pcap: SUCCESS
2025-08-01 17:52:26,375 - INFO - 测试进度: 5/12 - pgsql_start_replication.pcap
2025-08-01 17:52:26,375 - INFO - 启动测试: pgsql_start_replication.pcap
2025-08-01 17:52:26,377 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:52:26,405 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 259
2025-08-01 17:52:26,406 - INFO - 测试任务已启动，任务ID: 319b27ad-2155-4231-a7e0-872c231b01b9
2025-08-01 17:52:26,408 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:52:27,477 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/319b27ad-2155-4231-a7e0-872c231b01b9 HTTP/1.1" 200 228
2025-08-01 17:52:27,478 - DEBUG - 任务进行中: 319b27ad-2155-4231-a7e0-872c231b01b9, 状态: running
2025-08-01 17:52:32,484 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:52:32,621 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/319b27ad-2155-4231-a7e0-872c231b01b9 HTTP/1.1" 200 228
2025-08-01 17:52:32,622 - DEBUG - 任务进行中: 319b27ad-2155-4231-a7e0-872c231b01b9, 状态: running
2025-08-01 17:52:37,625 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:52:37,658 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/319b27ad-2155-4231-a7e0-872c231b01b9 HTTP/1.1" 200 1042
2025-08-01 17:52:37,659 - INFO - 任务完成: 319b27ad-2155-4231-a7e0-872c231b01b9
2025-08-01 17:52:37,659 - INFO -   ❌ pgsql_start_replication.pcap: NO_JSON_EVENTS
2025-08-01 17:52:38,662 - INFO - 测试进度: 6/12 - pgsql_wal_streaming.pcap
2025-08-01 17:52:38,663 - INFO - 启动测试: pgsql_wal_streaming.pcap
2025-08-01 17:52:38,664 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:52:38,697 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 255
2025-08-01 17:52:38,697 - INFO - 测试任务已启动，任务ID: a2f81c97-f728-4053-91cf-3f5c36188ef8
2025-08-01 17:52:38,699 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:52:39,761 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/a2f81c97-f728-4053-91cf-3f5c36188ef8 HTTP/1.1" 200 228
2025-08-01 17:52:39,762 - DEBUG - 任务进行中: a2f81c97-f728-4053-91cf-3f5c36188ef8, 状态: running
2025-08-01 17:52:44,765 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:52:44,786 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/a2f81c97-f728-4053-91cf-3f5c36188ef8 HTTP/1.1" 200 228
2025-08-01 17:52:44,787 - DEBUG - 任务进行中: a2f81c97-f728-4053-91cf-3f5c36188ef8, 状态: running
2025-08-01 17:52:49,793 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:52:49,810 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/a2f81c97-f728-4053-91cf-3f5c36188ef8 HTTP/1.1" 200 1042
2025-08-01 17:52:49,810 - INFO - 任务完成: a2f81c97-f728-4053-91cf-3f5c36188ef8
2025-08-01 17:52:49,810 - INFO -   ❌ pgsql_wal_streaming.pcap: NO_JSON_EVENTS
2025-08-01 17:52:50,814 - INFO - 测试进度: 7/12 - pgsql_replication_feedback.pcap
2025-08-01 17:52:50,814 - INFO - 启动测试: pgsql_replication_feedback.pcap
2025-08-01 17:52:50,816 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:52:50,830 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 262
2025-08-01 17:52:50,831 - INFO - 测试任务已启动，任务ID: 3b131251-8c34-4774-8854-8998e15c3e9b
2025-08-01 17:52:50,833 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:52:51,843 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/3b131251-8c34-4774-8854-8998e15c3e9b HTTP/1.1" 200 228
2025-08-01 17:52:51,844 - DEBUG - 任务进行中: 3b131251-8c34-4774-8854-8998e15c3e9b, 状态: running
2025-08-01 17:52:56,851 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:52:56,868 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/3b131251-8c34-4774-8854-8998e15c3e9b HTTP/1.1" 200 228
2025-08-01 17:52:56,868 - DEBUG - 任务进行中: 3b131251-8c34-4774-8854-8998e15c3e9b, 状态: running
2025-08-01 17:53:01,872 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:53:01,970 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/3b131251-8c34-4774-8854-8998e15c3e9b HTTP/1.1" 200 228
2025-08-01 17:53:01,971 - DEBUG - 任务进行中: 3b131251-8c34-4774-8854-8998e15c3e9b, 状态: running
2025-08-01 17:53:06,979 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:53:07,027 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/3b131251-8c34-4774-8854-8998e15c3e9b HTTP/1.1" 200 1208
2025-08-01 17:53:07,028 - INFO - 任务完成: 3b131251-8c34-4774-8854-8998e15c3e9b
2025-08-01 17:53:07,028 - INFO -   ❌ pgsql_replication_feedback.pcap: NO_JSON_EVENTS
2025-08-01 17:53:08,030 - INFO - 测试进度: 8/12 - pgsql_ssl_request_supported.pcap
2025-08-01 17:53:08,030 - INFO - 启动测试: pgsql_ssl_request_supported.pcap
2025-08-01 17:53:08,032 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:53:08,046 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 263
2025-08-01 17:53:08,047 - INFO - 测试任务已启动，任务ID: 4f759e34-cc8f-4212-8ffc-f509f81b62bc
2025-08-01 17:53:08,049 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:53:09,160 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/4f759e34-cc8f-4212-8ffc-f509f81b62bc HTTP/1.1" 200 228
2025-08-01 17:53:09,160 - DEBUG - 任务进行中: 4f759e34-cc8f-4212-8ffc-f509f81b62bc, 状态: running
2025-08-01 17:53:14,168 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:53:14,194 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/4f759e34-cc8f-4212-8ffc-f509f81b62bc HTTP/1.1" 200 228
2025-08-01 17:53:14,195 - DEBUG - 任务进行中: 4f759e34-cc8f-4212-8ffc-f509f81b62bc, 状态: running
2025-08-01 17:53:19,200 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:53:19,213 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/4f759e34-cc8f-4212-8ffc-f509f81b62bc HTTP/1.1" 200 228
2025-08-01 17:53:19,213 - DEBUG - 任务进行中: 4f759e34-cc8f-4212-8ffc-f509f81b62bc, 状态: running
2025-08-01 17:53:24,219 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:53:24,239 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/4f759e34-cc8f-4212-8ffc-f509f81b62bc HTTP/1.1" 200 1042
2025-08-01 17:53:24,239 - INFO - 任务完成: 4f759e34-cc8f-4212-8ffc-f509f81b62bc
2025-08-01 17:53:24,239 - INFO -   ❌ pgsql_ssl_request_supported.pcap: NO_JSON_EVENTS
2025-08-01 17:53:25,241 - INFO - 测试进度: 9/12 - pgsql_describe_statement.pcap
2025-08-01 17:53:25,242 - INFO - 启动测试: pgsql_describe_statement.pcap
2025-08-01 17:53:25,243 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:53:25,594 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 260
2025-08-01 17:53:25,595 - INFO - 测试任务已启动，任务ID: 1064a6b4-06c4-418a-b2a6-c57b16f29e62
2025-08-01 17:53:25,596 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:53:26,664 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/1064a6b4-06c4-418a-b2a6-c57b16f29e62 HTTP/1.1" 200 228
2025-08-01 17:53:26,666 - DEBUG - 任务进行中: 1064a6b4-06c4-418a-b2a6-c57b16f29e62, 状态: running
2025-08-01 17:53:31,673 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:53:31,699 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/1064a6b4-06c4-418a-b2a6-c57b16f29e62 HTTP/1.1" 200 228
2025-08-01 17:53:31,700 - DEBUG - 任务进行中: 1064a6b4-06c4-418a-b2a6-c57b16f29e62, 状态: running
2025-08-01 17:53:36,706 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:53:36,721 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/1064a6b4-06c4-418a-b2a6-c57b16f29e62 HTTP/1.1" 200 1042
2025-08-01 17:53:36,721 - INFO - 任务完成: 1064a6b4-06c4-418a-b2a6-c57b16f29e62
2025-08-01 17:53:36,721 - INFO -   ❌ pgsql_describe_statement.pcap: NO_JSON_EVENTS
2025-08-01 17:53:37,725 - INFO - 测试进度: 10/12 - pgsql_copy_to_stdout.pcap
2025-08-01 17:53:37,725 - INFO - 启动测试: pgsql_copy_to_stdout.pcap
2025-08-01 17:53:37,726 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:53:37,744 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 256
2025-08-01 17:53:37,745 - INFO - 测试任务已启动，任务ID: 1b8b8de5-0e58-4796-9d38-0ae8dbbf2f1a
2025-08-01 17:53:37,747 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:53:38,854 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/1b8b8de5-0e58-4796-9d38-0ae8dbbf2f1a HTTP/1.1" 200 228
2025-08-01 17:53:38,855 - DEBUG - 任务进行中: 1b8b8de5-0e58-4796-9d38-0ae8dbbf2f1a, 状态: running
2025-08-01 17:53:43,859 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:53:43,877 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/1b8b8de5-0e58-4796-9d38-0ae8dbbf2f1a HTTP/1.1" 200 228
2025-08-01 17:53:43,878 - DEBUG - 任务进行中: 1b8b8de5-0e58-4796-9d38-0ae8dbbf2f1a, 状态: running
2025-08-01 17:53:48,883 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:53:48,898 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/1b8b8de5-0e58-4796-9d38-0ae8dbbf2f1a HTTP/1.1" 200 1042
2025-08-01 17:53:48,898 - INFO - 任务完成: 1b8b8de5-0e58-4796-9d38-0ae8dbbf2f1a
2025-08-01 17:53:48,899 - INFO -   ❌ pgsql_copy_to_stdout.pcap: NO_JSON_EVENTS
2025-08-01 17:53:49,901 - INFO - 测试进度: 11/12 - pgsql_close_statement_portal.pcap
2025-08-01 17:53:49,902 - INFO - 启动测试: pgsql_close_statement_portal.pcap
2025-08-01 17:53:49,903 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:53:49,919 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 264
2025-08-01 17:53:49,921 - INFO - 测试任务已启动，任务ID: 50a4d393-b120-4c59-8913-45fc723d5e36
2025-08-01 17:53:49,923 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:53:50,913 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/50a4d393-b120-4c59-8913-45fc723d5e36 HTTP/1.1" 200 228
2025-08-01 17:53:50,914 - DEBUG - 任务进行中: 50a4d393-b120-4c59-8913-45fc723d5e36, 状态: running
2025-08-01 17:53:55,916 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:53:55,935 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/50a4d393-b120-4c59-8913-45fc723d5e36 HTTP/1.1" 200 228
2025-08-01 17:53:55,936 - DEBUG - 任务进行中: 50a4d393-b120-4c59-8913-45fc723d5e36, 状态: running
2025-08-01 17:54:00,943 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:54:00,979 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/50a4d393-b120-4c59-8913-45fc723d5e36 HTTP/1.1" 200 1208
2025-08-01 17:54:00,980 - INFO - 任务完成: 50a4d393-b120-4c59-8913-45fc723d5e36
2025-08-01 17:54:00,980 - INFO -   ❌ pgsql_close_statement_portal.pcap: NO_JSON_EVENTS
2025-08-01 17:54:01,983 - INFO - 测试进度: 12/12 - pgsql_nodata.pcap
2025-08-01 17:54:01,983 - INFO - 启动测试: pgsql_nodata.pcap
2025-08-01 17:54:01,984 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:54:02,037 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 248
2025-08-01 17:54:02,037 - INFO - 测试任务已启动，任务ID: 1bab2268-91d7-445a-8a28-14f7b22b3588
2025-08-01 17:54:02,039 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:54:03,055 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/1bab2268-91d7-445a-8a28-14f7b22b3588 HTTP/1.1" 200 228
2025-08-01 17:54:03,056 - DEBUG - 任务进行中: 1bab2268-91d7-445a-8a28-14f7b22b3588, 状态: running
2025-08-01 17:54:08,059 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:54:08,074 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/1bab2268-91d7-445a-8a28-14f7b22b3588 HTTP/1.1" 200 228
2025-08-01 17:54:08,075 - DEBUG - 任务进行中: 1bab2268-91d7-445a-8a28-14f7b22b3588, 状态: running
2025-08-01 17:54:13,083 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:54:13,176 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/1bab2268-91d7-445a-8a28-14f7b22b3588 HTTP/1.1" 200 228
2025-08-01 17:54:13,177 - DEBUG - 任务进行中: 1bab2268-91d7-445a-8a28-14f7b22b3588, 状态: running
2025-08-01 17:54:18,183 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:54:18,203 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/1bab2268-91d7-445a-8a28-14f7b22b3588 HTTP/1.1" 200 1042
2025-08-01 17:54:18,203 - INFO - 任务完成: 1bab2268-91d7-445a-8a28-14f7b22b3588
2025-08-01 17:54:18,204 - INFO -   ❌ pgsql_nodata.pcap: NO_JSON_EVENTS
2025-08-01 17:54:19,205 - INFO - 指定文件测试完成
2025-08-01 17:54:19,205 - INFO - 生成测试报告: no_json_events_retest.md
2025-08-01 17:54:19,206 - INFO - 测试报告已生成: no_json_events_retest.md
2025-08-01 17:54:19,206 - INFO - 测试统计: 总数=12, 成功=1, 失败=11, 成功率=8.3%
2025-08-01 17:59:43,136 - INFO - 重新测试12个无JSON事件文件...
2025-08-01 17:59:43,137 - INFO - 开始测试指定的12个文件...
2025-08-01 17:59:43,137 - INFO - 测试进度: 1/12 - pgsql_max_message_length.pcap
2025-08-01 17:59:43,137 - INFO - 启动测试: pgsql_max_message_length.pcap
2025-08-01 17:59:43,139 - DEBUG - Starting new HTTP connection (1): 127.0.0.1:7890
2025-08-01 17:59:43,176 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 260
2025-08-01 17:59:43,177 - INFO - 测试任务已启动，任务ID: a9f8f76e-e777-4cf0-b89d-22055fa16a0d
2025-08-01 17:59:43,179 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:59:44,233 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/a9f8f76e-e777-4cf0-b89d-22055fa16a0d HTTP/1.1" 200 228
2025-08-01 17:59:44,235 - DEBUG - 任务进行中: a9f8f76e-e777-4cf0-b89d-22055fa16a0d, 状态: running
2025-08-01 17:59:49,243 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:59:49,257 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/a9f8f76e-e777-4cf0-b89d-22055fa16a0d HTTP/1.1" 200 228
2025-08-01 17:59:49,258 - DEBUG - 任务进行中: a9f8f76e-e777-4cf0-b89d-22055fa16a0d, 状态: running
2025-08-01 17:59:54,266 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:59:54,283 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/a9f8f76e-e777-4cf0-b89d-22055fa16a0d HTTP/1.1" 200 22149
2025-08-01 17:59:54,287 - INFO - 任务完成: a9f8f76e-e777-4cf0-b89d-22055fa16a0d
2025-08-01 17:59:54,287 - INFO -   ✅ pgsql_max_message_length.pcap: SUCCESS
2025-08-01 17:59:55,289 - INFO - 测试进度: 2/12 - pgsql_max_parameters.pcap
2025-08-01 17:59:55,290 - INFO - 启动测试: pgsql_max_parameters.pcap
2025-08-01 17:59:55,292 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:59:55,308 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 256
2025-08-01 17:59:55,311 - INFO - 测试任务已启动，任务ID: 8ca662a7-d6a3-4681-84ed-c191eac59a3a
2025-08-01 17:59:55,316 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 17:59:56,419 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/8ca662a7-d6a3-4681-84ed-c191eac59a3a HTTP/1.1" 200 228
2025-08-01 17:59:56,420 - DEBUG - 任务进行中: 8ca662a7-d6a3-4681-84ed-c191eac59a3a, 状态: running
2025-08-01 18:00:01,427 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:00:01,450 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/8ca662a7-d6a3-4681-84ed-c191eac59a3a HTTP/1.1" 200 228
2025-08-01 18:00:01,451 - DEBUG - 任务进行中: 8ca662a7-d6a3-4681-84ed-c191eac59a3a, 状态: running
2025-08-01 18:00:06,457 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:00:06,479 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/8ca662a7-d6a3-4681-84ed-c191eac59a3a HTTP/1.1" 200 5666
2025-08-01 18:00:06,480 - INFO - 任务完成: 8ca662a7-d6a3-4681-84ed-c191eac59a3a
2025-08-01 18:00:06,480 - INFO -   ✅ pgsql_max_parameters.pcap: SUCCESS
2025-08-01 18:00:07,485 - INFO - 测试进度: 3/12 - pgsql_max_string_length.pcap
2025-08-01 18:00:07,486 - INFO - 启动测试: pgsql_max_string_length.pcap
2025-08-01 18:00:07,490 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:00:07,513 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 259
2025-08-01 18:00:07,514 - INFO - 测试任务已启动，任务ID: 84444ae7-4781-487d-9c7d-5782c65484f2
2025-08-01 18:00:07,516 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:00:08,605 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/84444ae7-4781-487d-9c7d-5782c65484f2 HTTP/1.1" 200 228
2025-08-01 18:00:08,606 - DEBUG - 任务进行中: 84444ae7-4781-487d-9c7d-5782c65484f2, 状态: running
2025-08-01 18:00:13,613 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:00:13,629 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/84444ae7-4781-487d-9c7d-5782c65484f2 HTTP/1.1" 200 228
2025-08-01 18:00:13,631 - DEBUG - 任务进行中: 84444ae7-4781-487d-9c7d-5782c65484f2, 状态: running
2025-08-01 18:00:18,640 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:00:18,657 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/84444ae7-4781-487d-9c7d-5782c65484f2 HTTP/1.1" 200 13861
2025-08-01 18:00:18,660 - INFO - 任务完成: 84444ae7-4781-487d-9c7d-5782c65484f2
2025-08-01 18:00:18,662 - INFO -   ✅ pgsql_max_string_length.pcap: SUCCESS
2025-08-01 18:00:19,666 - INFO - 测试进度: 4/12 - pgsql_empty_data_handling.pcap
2025-08-01 18:00:19,666 - INFO - 启动测试: pgsql_empty_data_handling.pcap
2025-08-01 18:00:19,669 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:00:19,687 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 261
2025-08-01 18:00:19,688 - INFO - 测试任务已启动，任务ID: 8bd6e443-7817-4dca-b198-18b7755a37a5
2025-08-01 18:00:19,690 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:00:20,757 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/8bd6e443-7817-4dca-b198-18b7755a37a5 HTTP/1.1" 200 228
2025-08-01 18:00:20,758 - DEBUG - 任务进行中: 8bd6e443-7817-4dca-b198-18b7755a37a5, 状态: running
2025-08-01 18:00:25,762 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:00:25,779 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/8bd6e443-7817-4dca-b198-18b7755a37a5 HTTP/1.1" 200 228
2025-08-01 18:00:25,780 - DEBUG - 任务进行中: 8bd6e443-7817-4dca-b198-18b7755a37a5, 状态: running
2025-08-01 18:00:30,790 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:00:30,806 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/8bd6e443-7817-4dca-b198-18b7755a37a5 HTTP/1.1" 200 228
2025-08-01 18:00:30,807 - DEBUG - 任务进行中: 8bd6e443-7817-4dca-b198-18b7755a37a5, 状态: running
2025-08-01 18:00:35,812 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:00:35,828 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/8bd6e443-7817-4dca-b198-18b7755a37a5 HTTP/1.1" 200 1912
2025-08-01 18:00:35,829 - INFO - 任务完成: 8bd6e443-7817-4dca-b198-18b7755a37a5
2025-08-01 18:00:35,829 - INFO -   ✅ pgsql_empty_data_handling.pcap: SUCCESS
2025-08-01 18:00:36,833 - INFO - 测试进度: 5/12 - pgsql_start_replication.pcap
2025-08-01 18:00:36,833 - INFO - 启动测试: pgsql_start_replication.pcap
2025-08-01 18:00:36,836 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:00:36,851 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 259
2025-08-01 18:00:36,853 - INFO - 测试任务已启动，任务ID: 608ac4aa-30ef-4d5c-982a-ccea09f43ac1
2025-08-01 18:00:36,855 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:00:37,992 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/608ac4aa-30ef-4d5c-982a-ccea09f43ac1 HTTP/1.1" 200 228
2025-08-01 18:00:37,993 - DEBUG - 任务进行中: 608ac4aa-30ef-4d5c-982a-ccea09f43ac1, 状态: running
2025-08-01 18:00:42,999 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:00:43,014 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/608ac4aa-30ef-4d5c-982a-ccea09f43ac1 HTTP/1.1" 200 228
2025-08-01 18:00:43,015 - DEBUG - 任务进行中: 608ac4aa-30ef-4d5c-982a-ccea09f43ac1, 状态: running
2025-08-01 18:00:48,021 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:00:48,111 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/608ac4aa-30ef-4d5c-982a-ccea09f43ac1 HTTP/1.1" 200 1556
2025-08-01 18:00:48,113 - INFO - 任务完成: 608ac4aa-30ef-4d5c-982a-ccea09f43ac1
2025-08-01 18:00:48,113 - INFO -   ✅ pgsql_start_replication.pcap: SUCCESS
2025-08-01 18:00:49,118 - INFO - 测试进度: 6/12 - pgsql_wal_streaming.pcap
2025-08-01 18:00:49,118 - INFO - 启动测试: pgsql_wal_streaming.pcap
2025-08-01 18:00:49,120 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:00:49,137 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 255
2025-08-01 18:00:49,137 - INFO - 测试任务已启动，任务ID: a2e5109c-e4d1-4bfa-95cf-7cd74b2b5ae7
2025-08-01 18:00:49,139 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:00:50,278 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/a2e5109c-e4d1-4bfa-95cf-7cd74b2b5ae7 HTTP/1.1" 200 228
2025-08-01 18:00:50,280 - DEBUG - 任务进行中: a2e5109c-e4d1-4bfa-95cf-7cd74b2b5ae7, 状态: running
2025-08-01 18:00:55,287 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:00:55,304 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/a2e5109c-e4d1-4bfa-95cf-7cd74b2b5ae7 HTTP/1.1" 200 228
2025-08-01 18:00:55,306 - DEBUG - 任务进行中: a2e5109c-e4d1-4bfa-95cf-7cd74b2b5ae7, 状态: running
2025-08-01 18:01:00,310 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:01:00,324 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/a2e5109c-e4d1-4bfa-95cf-7cd74b2b5ae7 HTTP/1.1" 200 1208
2025-08-01 18:01:00,325 - INFO - 任务完成: a2e5109c-e4d1-4bfa-95cf-7cd74b2b5ae7
2025-08-01 18:01:00,325 - INFO -   ❌ pgsql_wal_streaming.pcap: NO_JSON_EVENTS
2025-08-01 18:01:01,330 - INFO - 测试进度: 7/12 - pgsql_replication_feedback.pcap
2025-08-01 18:01:01,332 - INFO - 启动测试: pgsql_replication_feedback.pcap
2025-08-01 18:01:01,336 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:01:01,354 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 262
2025-08-01 18:01:01,355 - INFO - 测试任务已启动，任务ID: 2dd185a2-2f7c-4ba6-b255-17fcc6dd5d08
2025-08-01 18:01:01,358 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:01:01,968 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/2dd185a2-2f7c-4ba6-b255-17fcc6dd5d08 HTTP/1.1" 200 228
2025-08-01 18:01:01,969 - DEBUG - 任务进行中: 2dd185a2-2f7c-4ba6-b255-17fcc6dd5d08, 状态: running
2025-08-01 18:01:06,979 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:01:06,998 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/2dd185a2-2f7c-4ba6-b255-17fcc6dd5d08 HTTP/1.1" 200 228
2025-08-01 18:01:07,000 - DEBUG - 任务进行中: 2dd185a2-2f7c-4ba6-b255-17fcc6dd5d08, 状态: running
2025-08-01 18:01:12,004 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:01:12,502 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/2dd185a2-2f7c-4ba6-b255-17fcc6dd5d08 HTTP/1.1" 200 228
2025-08-01 18:01:12,503 - DEBUG - 任务进行中: 2dd185a2-2f7c-4ba6-b255-17fcc6dd5d08, 状态: running
2025-08-01 18:01:17,508 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:01:17,525 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/2dd185a2-2f7c-4ba6-b255-17fcc6dd5d08 HTTP/1.1" 200 1042
2025-08-01 18:01:17,526 - INFO - 任务完成: 2dd185a2-2f7c-4ba6-b255-17fcc6dd5d08
2025-08-01 18:01:17,526 - INFO -   ❌ pgsql_replication_feedback.pcap: NO_JSON_EVENTS
2025-08-01 18:01:18,530 - INFO - 测试进度: 8/12 - pgsql_ssl_request_supported.pcap
2025-08-01 18:01:18,531 - INFO - 启动测试: pgsql_ssl_request_supported.pcap
2025-08-01 18:01:18,533 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:01:18,559 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 263
2025-08-01 18:01:18,561 - INFO - 测试任务已启动，任务ID: 80ec363c-25f1-458f-869a-7a2d18dc0bcf
2025-08-01 18:01:18,564 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:01:19,665 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/80ec363c-25f1-458f-869a-7a2d18dc0bcf HTTP/1.1" 200 228
2025-08-01 18:01:19,666 - DEBUG - 任务进行中: 80ec363c-25f1-458f-869a-7a2d18dc0bcf, 状态: running
2025-08-01 18:01:24,672 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:01:26,158 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/80ec363c-25f1-458f-869a-7a2d18dc0bcf HTTP/1.1" 200 228
2025-08-01 18:01:26,229 - DEBUG - 任务进行中: 80ec363c-25f1-458f-869a-7a2d18dc0bcf, 状态: running
2025-08-01 18:01:31,232 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:01:31,250 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/80ec363c-25f1-458f-869a-7a2d18dc0bcf HTTP/1.1" 200 1042
2025-08-01 18:01:31,251 - INFO - 任务完成: 80ec363c-25f1-458f-869a-7a2d18dc0bcf
2025-08-01 18:01:31,251 - INFO -   ❌ pgsql_ssl_request_supported.pcap: NO_JSON_EVENTS
2025-08-01 18:01:32,252 - INFO - 测试进度: 9/12 - pgsql_describe_statement.pcap
2025-08-01 18:01:32,253 - INFO - 启动测试: pgsql_describe_statement.pcap
2025-08-01 18:01:32,256 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:01:32,270 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 260
2025-08-01 18:01:32,271 - INFO - 测试任务已启动，任务ID: fdc11455-0cac-4471-a4fa-59ed1c080cf4
2025-08-01 18:01:32,273 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:01:33,305 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/fdc11455-0cac-4471-a4fa-59ed1c080cf4 HTTP/1.1" 200 228
2025-08-01 18:01:33,307 - DEBUG - 任务进行中: fdc11455-0cac-4471-a4fa-59ed1c080cf4, 状态: running
2025-08-01 18:01:38,313 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:01:38,327 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/fdc11455-0cac-4471-a4fa-59ed1c080cf4 HTTP/1.1" 200 228
2025-08-01 18:01:38,328 - DEBUG - 任务进行中: fdc11455-0cac-4471-a4fa-59ed1c080cf4, 状态: running
2025-08-01 18:01:43,332 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:01:43,348 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/fdc11455-0cac-4471-a4fa-59ed1c080cf4 HTTP/1.1" 200 1042
2025-08-01 18:01:43,349 - INFO - 任务完成: fdc11455-0cac-4471-a4fa-59ed1c080cf4
2025-08-01 18:01:43,350 - INFO -   ❌ pgsql_describe_statement.pcap: NO_JSON_EVENTS
2025-08-01 18:01:44,353 - INFO - 测试进度: 10/12 - pgsql_copy_to_stdout.pcap
2025-08-01 18:01:44,354 - INFO - 启动测试: pgsql_copy_to_stdout.pcap
2025-08-01 18:01:44,357 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:01:44,374 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 256
2025-08-01 18:01:44,375 - INFO - 测试任务已启动，任务ID: 39057383-c7a1-4d0e-bc99-a8dd6d63d8b9
2025-08-01 18:01:44,377 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:01:45,416 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/39057383-c7a1-4d0e-bc99-a8dd6d63d8b9 HTTP/1.1" 200 228
2025-08-01 18:01:45,417 - DEBUG - 任务进行中: 39057383-c7a1-4d0e-bc99-a8dd6d63d8b9, 状态: running
2025-08-01 18:01:50,424 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:01:50,440 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/39057383-c7a1-4d0e-bc99-a8dd6d63d8b9 HTTP/1.1" 200 228
2025-08-01 18:01:50,441 - DEBUG - 任务进行中: 39057383-c7a1-4d0e-bc99-a8dd6d63d8b9, 状态: running
2025-08-01 18:01:55,449 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:01:55,473 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/39057383-c7a1-4d0e-bc99-a8dd6d63d8b9 HTTP/1.1" 200 228
2025-08-01 18:01:55,474 - DEBUG - 任务进行中: 39057383-c7a1-4d0e-bc99-a8dd6d63d8b9, 状态: running
2025-08-01 18:02:00,507 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:02:00,526 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/39057383-c7a1-4d0e-bc99-a8dd6d63d8b9 HTTP/1.1" 200 1042
2025-08-01 18:02:00,527 - INFO - 任务完成: 39057383-c7a1-4d0e-bc99-a8dd6d63d8b9
2025-08-01 18:02:00,528 - INFO -   ❌ pgsql_copy_to_stdout.pcap: NO_JSON_EVENTS
2025-08-01 18:02:01,532 - INFO - 测试进度: 11/12 - pgsql_close_statement_portal.pcap
2025-08-01 18:02:01,532 - INFO - 启动测试: pgsql_close_statement_portal.pcap
2025-08-01 18:02:01,536 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:02:01,559 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 264
2025-08-01 18:02:01,560 - INFO - 测试任务已启动，任务ID: be9d75fa-c959-4cd4-9758-2d7b3812b5d8
2025-08-01 18:02:01,563 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:02:02,547 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/be9d75fa-c959-4cd4-9758-2d7b3812b5d8 HTTP/1.1" 200 228
2025-08-01 18:02:02,549 - DEBUG - 任务进行中: be9d75fa-c959-4cd4-9758-2d7b3812b5d8, 状态: running
2025-08-01 18:02:07,556 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:02:07,573 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/be9d75fa-c959-4cd4-9758-2d7b3812b5d8 HTTP/1.1" 200 228
2025-08-01 18:02:07,574 - DEBUG - 任务进行中: be9d75fa-c959-4cd4-9758-2d7b3812b5d8, 状态: running
2025-08-01 18:02:12,583 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:02:12,600 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/be9d75fa-c959-4cd4-9758-2d7b3812b5d8 HTTP/1.1" 200 1208
2025-08-01 18:02:12,601 - INFO - 任务完成: be9d75fa-c959-4cd4-9758-2d7b3812b5d8
2025-08-01 18:02:12,601 - INFO -   ❌ pgsql_close_statement_portal.pcap: NO_JSON_EVENTS
2025-08-01 18:02:13,606 - INFO - 测试进度: 12/12 - pgsql_nodata.pcap
2025-08-01 18:02:13,607 - INFO - 启动测试: pgsql_nodata.pcap
2025-08-01 18:02:13,610 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:02:13,631 - DEBUG - http://127.0.0.1:7890 "POST http://**************:8000/api/v1/test/pcap-replay HTTP/1.1" 200 248
2025-08-01 18:02:13,634 - INFO - 测试任务已启动，任务ID: 7c8d91cb-a193-4e1a-998a-8b98c2695d03
2025-08-01 18:02:13,637 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:02:14,720 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/7c8d91cb-a193-4e1a-998a-8b98c2695d03 HTTP/1.1" 200 228
2025-08-01 18:02:14,721 - DEBUG - 任务进行中: 7c8d91cb-a193-4e1a-998a-8b98c2695d03, 状态: running
2025-08-01 18:02:19,731 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:02:19,764 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/7c8d91cb-a193-4e1a-998a-8b98c2695d03 HTTP/1.1" 200 228
2025-08-01 18:02:19,766 - DEBUG - 任务进行中: 7c8d91cb-a193-4e1a-998a-8b98c2695d03, 状态: running
2025-08-01 18:02:24,769 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:02:24,786 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/7c8d91cb-a193-4e1a-998a-8b98c2695d03 HTTP/1.1" 200 228
2025-08-01 18:02:24,787 - DEBUG - 任务进行中: 7c8d91cb-a193-4e1a-998a-8b98c2695d03, 状态: running
2025-08-01 18:02:29,792 - DEBUG - Resetting dropped connection: 127.0.0.1
2025-08-01 18:02:29,838 - DEBUG - http://127.0.0.1:7890 "GET http://**************:8000/api/v1/tasks/7c8d91cb-a193-4e1a-998a-8b98c2695d03 HTTP/1.1" 200 1042
2025-08-01 18:02:29,840 - INFO - 任务完成: 7c8d91cb-a193-4e1a-998a-8b98c2695d03
2025-08-01 18:02:29,841 - INFO -   ❌ pgsql_nodata.pcap: NO_JSON_EVENTS
2025-08-01 18:02:30,845 - INFO - 指定文件测试完成
2025-08-01 18:02:30,846 - INFO - 生成测试报告: no_json_events_retest.md
2025-08-01 18:02:30,847 - INFO - 测试报告已生成: no_json_events_retest.md
2025-08-01 18:02:30,848 - INFO - 测试统计: 总数=12, 成功=5, 失败=7, 成功率=41.7%
2025-08-04 16:23:28,409 - INFO - 开始执行完整的批量测试...
2025-08-04 16:23:28,410 - INFO - 开始批量测试...
2025-08-04 16:23:28,411 - INFO - 开始扫描pcap文件...
2025-08-04 16:23:28,412 - INFO - 在 ../pgsql_pcaps 中找到 36 个pcap文件
2025-08-04 16:23:28,413 - INFO - 在 ../pgsql_abnormal_pcaps 中找到 11 个pcap文件
2025-08-04 16:23:28,413 - INFO - 总共找到 47 个pcap文件
2025-08-04 16:23:28,413 - INFO - 开始测试目录: ../pgsql_pcaps
2025-08-04 16:23:28,413 - INFO - 测试进度: 1/47 - pgsql_auth_with_copy.pcap
2025-08-04 16:23:28,413 - INFO - 启动测试: pgsql_auth_with_copy.pcap
2025-08-04 16:23:28,455 - INFO - 测试任务已启动，任务ID: 53f4ce64-208e-49cd-a279-d8bedc886ed5
2025-08-04 16:23:45,659 - INFO - 任务完成: 53f4ce64-208e-49cd-a279-d8bedc886ed5
2025-08-04 16:23:45,659 - INFO -   ✅ pgsql_auth_with_copy.pcap: SUCCESS
2025-08-04 16:23:46,667 - INFO - 测试进度: 2/47 - pgsql_authentication.pcap
2025-08-04 16:23:46,668 - INFO - 启动测试: pgsql_authentication.pcap
2025-08-04 16:23:46,687 - INFO - 测试任务已启动，任务ID: 9544d7b7-1023-4a77-80d4-c12c1bbee933
2025-08-04 16:24:03,822 - INFO - 任务完成: 9544d7b7-1023-4a77-80d4-c12c1bbee933
2025-08-04 16:24:03,823 - INFO -   ✅ pgsql_authentication.pcap: SUCCESS
2025-08-04 16:24:04,827 - INFO - 测试进度: 3/47 - pgsql_batch_operations.pcap
2025-08-04 16:24:04,828 - INFO - 启动测试: pgsql_batch_operations.pcap
2025-08-04 16:24:04,849 - INFO - 测试任务已启动，任务ID: 61cb9175-b950-4361-9adb-d12c24f22b1d
2025-08-04 16:24:22,186 - INFO - 任务完成: 61cb9175-b950-4361-9adb-d12c24f22b1d
2025-08-04 16:24:22,186 - INFO -   ✅ pgsql_batch_operations.pcap: SUCCESS
2025-08-04 16:24:23,188 - INFO - 测试进度: 4/47 - pgsql_binary_copy.pcap
2025-08-04 16:24:23,189 - INFO - 启动测试: pgsql_binary_copy.pcap
2025-08-04 16:24:23,266 - INFO - 测试任务已启动，任务ID: c78aa892-adfe-4b1c-8e43-8948ffa6352c
2025-08-04 16:24:40,586 - INFO - 任务完成: c78aa892-adfe-4b1c-8e43-8948ffa6352c
2025-08-04 16:24:40,586 - INFO -   ✅ pgsql_binary_copy.pcap: SUCCESS
2025-08-04 16:24:41,590 - INFO - 测试进度: 5/47 - pgsql_binary_extended_query.pcap
2025-08-04 16:24:41,591 - INFO - 启动测试: pgsql_binary_extended_query.pcap
2025-08-04 16:24:41,623 - INFO - 测试任务已启动，任务ID: cb59b7b3-d53d-434e-9e30-c0acbe73e428
2025-08-04 16:24:58,961 - INFO - 任务完成: cb59b7b3-d53d-434e-9e30-c0acbe73e428
2025-08-04 16:24:58,962 - INFO -   ✅ pgsql_binary_extended_query.pcap: SUCCESS
2025-08-04 16:24:59,965 - INFO - 测试进度: 6/47 - pgsql_cancel_query.pcap
2025-08-04 16:24:59,965 - INFO - 启动测试: pgsql_cancel_query.pcap
2025-08-04 16:24:59,997 - INFO - 测试任务已启动，任务ID: 8ab54dfa-d578-48e8-bf43-8c51f76a5a77
2025-08-04 16:25:17,284 - INFO - 任务完成: 8ab54dfa-d578-48e8-bf43-8c51f76a5a77
2025-08-04 16:25:17,284 - INFO -   ✅ pgsql_cancel_query.pcap: SUCCESS
2025-08-04 16:25:18,290 - INFO - 测试进度: 7/47 - pgsql_character_encoding.pcap
2025-08-04 16:25:18,291 - INFO - 启动测试: pgsql_character_encoding.pcap
2025-08-04 16:25:18,311 - INFO - 测试任务已启动，任务ID: 9c275e5e-1532-4d3b-b26b-933468253ae5
2025-08-04 16:25:35,426 - INFO - 任务完成: 9c275e5e-1532-4d3b-b26b-933468253ae5
2025-08-04 16:25:35,426 - INFO -   ✅ pgsql_character_encoding.pcap: SUCCESS
2025-08-04 16:25:36,429 - INFO - 测试进度: 8/47 - pgsql_cleartext_authentication.pcap
2025-08-04 16:25:36,429 - INFO - 启动测试: pgsql_cleartext_authentication.pcap
2025-08-04 16:25:36,446 - INFO - 测试任务已启动，任务ID: 227817f6-9c42-4284-a815-0159c0fda721
2025-08-04 16:25:53,776 - INFO - 任务完成: 227817f6-9c42-4284-a815-0159c0fda721
2025-08-04 16:25:53,776 - INFO -   ✅ pgsql_cleartext_authentication.pcap: SUCCESS
2025-08-04 16:25:54,782 - INFO - 测试进度: 9/47 - pgsql_close_statement_portal.pcap
2025-08-04 16:25:54,783 - INFO - 启动测试: pgsql_close_statement_portal.pcap
2025-08-04 16:25:54,805 - INFO - 测试任务已启动，任务ID: c84178b8-7d9b-4841-94bc-23996399fae9
2025-08-04 16:26:12,037 - INFO - 任务完成: c84178b8-7d9b-4841-94bc-23996399fae9
2025-08-04 16:26:12,037 - INFO -   ✅ pgsql_close_statement_portal.pcap: SUCCESS
2025-08-04 16:26:13,042 - INFO - 测试进度: 10/47 - pgsql_copy.pcap
2025-08-04 16:26:13,042 - INFO - 启动测试: pgsql_copy.pcap
2025-08-04 16:26:13,062 - INFO - 测试任务已启动，任务ID: 0e371b79-ace6-4499-8f86-c800b46a595e
2025-08-04 16:26:30,526 - INFO - 任务完成: 0e371b79-ace6-4499-8f86-c800b46a595e
2025-08-04 16:26:30,527 - INFO -   ✅ pgsql_copy.pcap: SUCCESS
2025-08-04 16:26:31,532 - INFO - 测试进度: 11/47 - pgsql_copy_to_stdout.pcap
2025-08-04 16:26:31,533 - INFO - 启动测试: pgsql_copy_to_stdout.pcap
2025-08-04 16:26:31,559 - INFO - 测试任务已启动，任务ID: fd886f2e-4f24-49b0-a522-aa233655ce96
2025-08-04 16:26:48,964 - INFO - 任务完成: fd886f2e-4f24-49b0-a522-aa233655ce96
2025-08-04 16:26:48,964 - INFO -   ❌ pgsql_copy_to_stdout.pcap: NO_JSON_EVENTS
2025-08-04 16:26:49,969 - INFO - 测试进度: 12/47 - pgsql_describe_portal.pcap
2025-08-04 16:26:49,970 - INFO - 启动测试: pgsql_describe_portal.pcap
2025-08-04 16:26:50,045 - INFO - 测试任务已启动，任务ID: 6ac548e5-8f3d-4ed4-bf78-d25ab1009758
2025-08-04 16:27:07,277 - INFO - 任务完成: 6ac548e5-8f3d-4ed4-bf78-d25ab1009758
2025-08-04 16:27:07,278 - INFO -   ✅ pgsql_describe_portal.pcap: SUCCESS
2025-08-04 16:27:08,282 - INFO - 测试进度: 13/47 - pgsql_describe_statement.pcap
2025-08-04 16:27:08,283 - INFO - 启动测试: pgsql_describe_statement.pcap
2025-08-04 16:27:08,303 - INFO - 测试任务已启动，任务ID: fde3c25c-4401-49e9-9474-383b562764b8
2025-08-04 16:27:25,388 - INFO - 任务完成: fde3c25c-4401-49e9-9474-383b562764b8
2025-08-04 16:27:25,389 - INFO -   ✅ pgsql_describe_statement.pcap: SUCCESS
2025-08-04 16:27:26,394 - INFO - 测试进度: 14/47 - pgsql_empty_data_handling.pcap
2025-08-04 16:27:26,395 - INFO - 启动测试: pgsql_empty_data_handling.pcap
2025-08-04 16:27:26,417 - INFO - 测试任务已启动，任务ID: 76ef026c-2513-4501-a676-9157852d9a03
2025-08-04 16:27:43,838 - INFO - 任务完成: 76ef026c-2513-4501-a676-9157852d9a03
2025-08-04 16:27:43,838 - INFO -   ✅ pgsql_empty_data_handling.pcap: SUCCESS
2025-08-04 16:27:44,839 - INFO - 测试进度: 15/47 - pgsql_flush.pcap
2025-08-04 16:27:44,840 - INFO - 启动测试: pgsql_flush.pcap
2025-08-04 16:27:44,858 - INFO - 测试任务已启动，任务ID: 72b76325-61ae-451d-b45a-2334d867df2c
2025-08-04 16:28:01,994 - INFO - 任务完成: 72b76325-61ae-451d-b45a-2334d867df2c
2025-08-04 16:28:01,994 - INFO -   ✅ pgsql_flush.pcap: SUCCESS
2025-08-04 16:28:02,995 - INFO - 测试进度: 16/47 - pgsql_function_call.pcap
2025-08-04 16:28:02,996 - INFO - 启动测试: pgsql_function_call.pcap
2025-08-04 16:28:03,031 - INFO - 测试任务已启动，任务ID: d5fa4bfc-9d19-462e-9563-2e39e70ba832
2025-08-04 16:28:20,042 - INFO - 任务完成: d5fa4bfc-9d19-462e-9563-2e39e70ba832
2025-08-04 16:28:20,042 - INFO -   ✅ pgsql_function_call.pcap: SUCCESS
2025-08-04 16:28:21,047 - INFO - 测试进度: 17/47 - pgsql_large_result.pcap
2025-08-04 16:28:21,048 - INFO - 启动测试: pgsql_large_result.pcap
2025-08-04 16:28:21,087 - INFO - 测试任务已启动，任务ID: 44ef8b4e-79bc-4b6e-ade6-114938004748
2025-08-04 16:28:37,940 - INFO - 任务完成: 44ef8b4e-79bc-4b6e-ade6-114938004748
2025-08-04 16:28:37,940 - INFO -   ✅ pgsql_large_result.pcap: SUCCESS
2025-08-04 16:28:38,944 - INFO - 测试进度: 18/47 - pgsql_max_message_length.pcap
2025-08-04 16:28:38,946 - INFO - 启动测试: pgsql_max_message_length.pcap
2025-08-04 16:28:39,022 - INFO - 测试任务已启动，任务ID: 2c35994f-21fe-4272-8d53-aaa006d3ba7a
2025-08-04 16:28:55,399 - INFO - 任务完成: 2c35994f-21fe-4272-8d53-aaa006d3ba7a
2025-08-04 16:28:55,400 - INFO -   ✅ pgsql_max_message_length.pcap: SUCCESS
2025-08-04 16:28:56,405 - INFO - 测试进度: 19/47 - pgsql_max_parameters.pcap
2025-08-04 16:28:56,405 - INFO - 启动测试: pgsql_max_parameters.pcap
2025-08-04 16:28:56,423 - INFO - 测试任务已启动，任务ID: 7d633da1-1be3-4989-b8fd-01fb8168c710
2025-08-04 16:29:12,923 - INFO - 任务完成: 7d633da1-1be3-4989-b8fd-01fb8168c710
2025-08-04 16:29:12,923 - INFO -   ✅ pgsql_max_parameters.pcap: SUCCESS
2025-08-04 16:29:13,927 - INFO - 测试进度: 20/47 - pgsql_max_string_length.pcap
2025-08-04 16:29:13,928 - INFO - 启动测试: pgsql_max_string_length.pcap
2025-08-04 16:29:13,972 - INFO - 测试任务已启动，任务ID: 84c18d21-56c8-486a-aac8-69b7b802b662
2025-08-04 16:29:31,340 - INFO - 任务完成: 84c18d21-56c8-486a-aac8-69b7b802b662
2025-08-04 16:29:31,341 - INFO -   ✅ pgsql_max_string_length.pcap: SUCCESS
2025-08-04 16:29:32,344 - INFO - 测试进度: 21/47 - pgsql_multi_parameter.pcap
2025-08-04 16:29:32,344 - INFO - 启动测试: pgsql_multi_parameter.pcap
2025-08-04 16:29:32,362 - INFO - 测试任务已启动，任务ID: c8b7d176-e12b-45ca-a273-ee2d263e29cb
2025-08-04 16:29:50,086 - INFO - 任务完成: c8b7d176-e12b-45ca-a273-ee2d263e29cb
2025-08-04 16:29:50,086 - INFO -   ✅ pgsql_multi_parameter.pcap: SUCCESS
2025-08-04 16:29:51,087 - INFO - 测试进度: 22/47 - pgsql_multi_query.pcap
2025-08-04 16:29:51,088 - INFO - 启动测试: pgsql_multi_query.pcap
2025-08-04 16:29:51,115 - INFO - 测试任务已启动，任务ID: 6067bdfe-b7d2-46a8-9357-26a5fd9cfd7b
2025-08-04 16:30:08,507 - INFO - 任务完成: 6067bdfe-b7d2-46a8-9357-26a5fd9cfd7b
2025-08-04 16:30:08,508 - INFO -   ✅ pgsql_multi_query.pcap: SUCCESS
2025-08-04 16:30:09,509 - INFO - 测试进度: 23/47 - pgsql_multiple_data_types.pcap
2025-08-04 16:30:09,510 - INFO - 启动测试: pgsql_multiple_data_types.pcap
2025-08-04 16:30:09,537 - INFO - 测试任务已启动，任务ID: e3ca27a0-9b96-45fd-ac1a-51e82a874aa9
2025-08-04 16:30:27,054 - INFO - 任务完成: e3ca27a0-9b96-45fd-ac1a-51e82a874aa9
2025-08-04 16:30:27,054 - INFO -   ✅ pgsql_multiple_data_types.pcap: SUCCESS
2025-08-04 16:30:28,059 - INFO - 测试进度: 24/47 - pgsql_nodata.pcap
2025-08-04 16:30:28,060 - INFO - 启动测试: pgsql_nodata.pcap
2025-08-04 16:30:28,082 - INFO - 测试任务已启动，任务ID: 4254d4ae-a2e0-4b08-85b4-a02fa8715277
2025-08-04 16:30:45,127 - INFO - 任务完成: 4254d4ae-a2e0-4b08-85b4-a02fa8715277
2025-08-04 16:30:45,129 - INFO -   ✅ pgsql_nodata.pcap: SUCCESS
2025-08-04 16:30:46,135 - INFO - 测试进度: 25/47 - pgsql_notice_response.pcap
2025-08-04 16:30:46,136 - INFO - 启动测试: pgsql_notice_response.pcap
2025-08-04 16:30:46,162 - INFO - 测试任务已启动，任务ID: f9624228-cfac-4aac-ac52-32417eaac437
2025-08-04 16:31:03,366 - INFO - 任务完成: f9624228-cfac-4aac-ac52-32417eaac437
2025-08-04 16:31:03,366 - INFO -   ✅ pgsql_notice_response.pcap: SUCCESS
2025-08-04 16:31:04,367 - INFO - 测试进度: 26/47 - pgsql_notification.pcap
2025-08-04 16:31:04,367 - INFO - 启动测试: pgsql_notification.pcap
2025-08-04 16:31:04,393 - INFO - 测试任务已启动，任务ID: 010a762d-aec3-46d5-8e6a-21055f42c6b1
2025-08-04 16:31:21,623 - INFO - 任务完成: 010a762d-aec3-46d5-8e6a-21055f42c6b1
2025-08-04 16:31:21,623 - INFO -   ✅ pgsql_notification.pcap: SUCCESS
2025-08-04 16:31:22,624 - INFO - 测试进度: 27/47 - pgsql_pipelined_queries.pcap
2025-08-04 16:31:22,624 - INFO - 启动测试: pgsql_pipelined_queries.pcap
2025-08-04 16:31:22,644 - INFO - 测试任务已启动，任务ID: 71a9b587-c429-4aa7-a7c0-c55666f088bd
2025-08-04 16:31:40,061 - INFO - 任务完成: 71a9b587-c429-4aa7-a7c0-c55666f088bd
2025-08-04 16:31:40,061 - INFO -   ✅ pgsql_pipelined_queries.pcap: SUCCESS
2025-08-04 16:31:41,067 - INFO - 测试进度: 28/47 - pgsql_prepared_statement.pcap
2025-08-04 16:31:41,068 - INFO - 启动测试: pgsql_prepared_statement.pcap
2025-08-04 16:31:41,088 - INFO - 测试任务已启动，任务ID: 14f39288-d847-4728-b675-dc6db202520f
2025-08-04 16:31:58,200 - INFO - 任务完成: 14f39288-d847-4728-b675-dc6db202520f
2025-08-04 16:31:58,200 - INFO -   ✅ pgsql_prepared_statement.pcap: SUCCESS
2025-08-04 16:31:59,205 - INFO - 测试进度: 29/47 - pgsql_replication_feedback.pcap
2025-08-04 16:31:59,206 - INFO - 启动测试: pgsql_replication_feedback.pcap
2025-08-04 16:31:59,230 - INFO - 测试任务已启动，任务ID: d06c0319-feea-4b34-b5b0-bde509ec7349
2025-08-04 16:32:16,502 - INFO - 任务完成: d06c0319-feea-4b34-b5b0-bde509ec7349
2025-08-04 16:32:16,503 - INFO -   ❌ pgsql_replication_feedback.pcap: NO_JSON_EVENTS
2025-08-04 16:32:17,505 - INFO - 测试进度: 30/47 - pgsql_sasl_authentication.pcap
2025-08-04 16:32:17,506 - INFO - 启动测试: pgsql_sasl_authentication.pcap
2025-08-04 16:32:17,530 - INFO - 测试任务已启动，任务ID: 6967b6f3-4f01-481f-8027-bc7d3656304d
2025-08-04 16:32:34,967 - INFO - 任务完成: 6967b6f3-4f01-481f-8027-bc7d3656304d
2025-08-04 16:32:34,967 - INFO -   ✅ pgsql_sasl_authentication.pcap: SUCCESS
2025-08-04 16:32:35,972 - INFO - 测试进度: 31/47 - pgsql_simple_query.pcap
2025-08-04 16:32:35,972 - INFO - 启动测试: pgsql_simple_query.pcap
2025-08-04 16:32:36,026 - INFO - 测试任务已启动，任务ID: adfddefc-3c0b-435c-ba6e-0da1278730ce
2025-08-04 16:32:53,477 - INFO - 任务完成: adfddefc-3c0b-435c-ba6e-0da1278730ce
2025-08-04 16:32:53,477 - INFO -   ✅ pgsql_simple_query.pcap: SUCCESS
2025-08-04 16:32:54,481 - INFO - 测试进度: 32/47 - pgsql_ssl_request_not_supported.pcap
2025-08-04 16:32:54,481 - INFO - 启动测试: pgsql_ssl_request_not_supported.pcap
2025-08-04 16:32:54,501 - INFO - 测试任务已启动，任务ID: 2871f41f-cdb3-4ad5-8353-589f5d99340a
2025-08-04 16:33:11,923 - INFO - 任务完成: 2871f41f-cdb3-4ad5-8353-589f5d99340a
2025-08-04 16:33:11,923 - INFO -   ✅ pgsql_ssl_request_not_supported.pcap: SUCCESS
2025-08-04 16:33:12,928 - INFO - 测试进度: 33/47 - pgsql_start_replication.pcap
2025-08-04 16:33:12,929 - INFO - 启动测试: pgsql_start_replication.pcap
2025-08-04 16:33:12,948 - INFO - 测试任务已启动，任务ID: a04482f9-fd18-4764-b0ba-358f9ed69937
2025-08-04 16:33:30,147 - INFO - 任务完成: a04482f9-fd18-4764-b0ba-358f9ed69937
2025-08-04 16:33:30,147 - INFO -   ✅ pgsql_start_replication.pcap: SUCCESS
2025-08-04 16:33:31,150 - INFO - 测试进度: 34/47 - pgsql_sync_separated_transactions.pcap
2025-08-04 16:33:31,151 - INFO - 启动测试: pgsql_sync_separated_transactions.pcap
2025-08-04 16:33:31,173 - INFO - 测试任务已启动，任务ID: 980811d8-6a79-4829-8af0-3c48d2eb17d8
2025-08-04 16:33:48,287 - INFO - 任务完成: 980811d8-6a79-4829-8af0-3c48d2eb17d8
2025-08-04 16:33:48,287 - INFO -   ✅ pgsql_sync_separated_transactions.pcap: SUCCESS
2025-08-04 16:33:49,293 - INFO - 测试进度: 35/47 - pgsql_transaction.pcap
2025-08-04 16:33:49,294 - INFO - 启动测试: pgsql_transaction.pcap
2025-08-04 16:33:49,317 - INFO - 测试任务已启动，任务ID: 144894cb-ebba-4497-83fe-096b8e7224b1
2025-08-04 16:34:07,145 - INFO - 任务完成: 144894cb-ebba-4497-83fe-096b8e7224b1
2025-08-04 16:34:07,145 - INFO -   ✅ pgsql_transaction.pcap: SUCCESS
2025-08-04 16:34:08,150 - INFO - 测试进度: 36/47 - pgsql_wal_streaming.pcap
2025-08-04 16:34:08,151 - INFO - 启动测试: pgsql_wal_streaming.pcap
2025-08-04 16:34:08,187 - INFO - 测试任务已启动，任务ID: 4714cd06-7b2b-4bd0-af08-fecd31fbd911
2025-08-04 16:34:25,421 - INFO - 任务完成: 4714cd06-7b2b-4bd0-af08-fecd31fbd911
2025-08-04 16:34:25,421 - INFO -   ❌ pgsql_wal_streaming.pcap: NO_JSON_EVENTS
2025-08-04 16:34:26,426 - INFO - 目录 ../pgsql_pcaps 测试完成
2025-08-04 16:34:26,427 - INFO - 开始测试目录: ../pgsql_abnormal_pcaps
2025-08-04 16:34:26,428 - INFO - 测试进度: 37/47 - pgsql_auth_failure.pcap
2025-08-04 16:34:26,428 - INFO - 启动测试: pgsql_auth_failure.pcap
2025-08-04 16:34:26,450 - INFO - 测试任务已启动，任务ID: eb78a12b-2de8-49df-bb9f-0e7419a0e8c3
2025-08-04 16:34:43,862 - INFO - 任务完成: eb78a12b-2de8-49df-bb9f-0e7419a0e8c3
2025-08-04 16:34:43,862 - INFO -   ✅ pgsql_auth_failure.pcap: SUCCESS
2025-08-04 16:34:44,863 - INFO - 测试进度: 38/47 - pgsql_constraint_violation.pcap
2025-08-04 16:34:44,864 - INFO - 启动测试: pgsql_constraint_violation.pcap
2025-08-04 16:34:44,886 - INFO - 测试任务已启动，任务ID: 3fc0add6-5064-4200-b3e0-b400c47d1ea6
2025-08-04 16:35:02,397 - INFO - 任务完成: 3fc0add6-5064-4200-b3e0-b400c47d1ea6
2025-08-04 16:35:02,398 - INFO -   ✅ pgsql_constraint_violation.pcap: SUCCESS
2025-08-04 16:35:03,401 - INFO - 测试进度: 39/47 - pgsql_copy_fail.pcap
2025-08-04 16:35:03,402 - INFO - 启动测试: pgsql_copy_fail.pcap
2025-08-04 16:35:03,429 - INFO - 测试任务已启动，任务ID: 5853b1fc-a98a-468b-bd92-76e873e72267
2025-08-04 16:35:19,229 - INFO - 任务完成: 5853b1fc-a98a-468b-bd92-76e873e72267
2025-08-04 16:35:19,229 - INFO -   ✅ pgsql_copy_fail.pcap: SUCCESS
2025-08-04 16:35:20,231 - INFO - 测试进度: 40/47 - pgsql_error.pcap
2025-08-04 16:35:20,232 - INFO - 启动测试: pgsql_error.pcap
2025-08-04 16:35:20,278 - INFO - 测试任务已启动，任务ID: 24a23913-a0ea-4f92-a202-97b2b2c7cd43
2025-08-04 16:35:37,565 - INFO - 任务完成: 24a23913-a0ea-4f92-a202-97b2b2c7cd43
2025-08-04 16:35:37,565 - INFO -   ✅ pgsql_error.pcap: SUCCESS
2025-08-04 16:35:38,568 - INFO - 测试进度: 41/47 - pgsql_fatal_error.pcap
2025-08-04 16:35:38,569 - INFO - 启动测试: pgsql_fatal_error.pcap
2025-08-04 16:35:38,598 - INFO - 测试任务已启动，任务ID: a12e86a5-149e-406a-8a01-fde821d92f69
2025-08-04 16:35:56,010 - INFO - 任务完成: a12e86a5-149e-406a-8a01-fde821d92f69
2025-08-04 16:35:56,010 - INFO -   ✅ pgsql_fatal_error.pcap: SUCCESS
2025-08-04 16:35:57,014 - INFO - 测试进度: 42/47 - pgsql_function_call_error.pcap
2025-08-04 16:35:57,015 - INFO - 启动测试: pgsql_function_call_error.pcap
2025-08-04 16:35:57,039 - INFO - 测试任务已启动，任务ID: 8f876048-5118-4be7-b31c-14df5c38f4fd
2025-08-04 16:36:14,294 - INFO - 任务完成: 8f876048-5118-4be7-b31c-14df5c38f4fd
2025-08-04 16:36:14,295 - INFO -   ✅ pgsql_function_call_error.pcap: SUCCESS
2025-08-04 16:36:15,300 - INFO - 测试进度: 43/47 - pgsql_panic_error.pcap
2025-08-04 16:36:15,301 - INFO - 启动测试: pgsql_panic_error.pcap
2025-08-04 16:36:15,337 - INFO - 测试任务已启动，任务ID: 33c22e33-6e6e-451e-8d32-25f88ff318d3
2025-08-04 16:36:32,094 - INFO - 任务完成: 33c22e33-6e6e-451e-8d32-25f88ff318d3
2025-08-04 16:36:32,095 - INFO -   ✅ pgsql_panic_error.pcap: SUCCESS
2025-08-04 16:36:33,100 - INFO - 测试进度: 44/47 - pgsql_pipelined_error_handling.pcap
2025-08-04 16:36:33,100 - INFO - 启动测试: pgsql_pipelined_error_handling.pcap
2025-08-04 16:36:33,128 - INFO - 测试任务已启动，任务ID: 644e9628-25c5-403a-9fc0-42bbcf94280c
2025-08-04 16:36:50,069 - INFO - 任务完成: 644e9628-25c5-403a-9fc0-42bbcf94280c
2025-08-04 16:36:50,069 - INFO -   ✅ pgsql_pipelined_error_handling.pcap: SUCCESS
2025-08-04 16:36:51,074 - INFO - 测试进度: 45/47 - pgsql_syntax_error.pcap
2025-08-04 16:36:51,075 - INFO - 启动测试: pgsql_syntax_error.pcap
2025-08-04 16:36:51,095 - INFO - 测试任务已启动，任务ID: 7a92cbfb-9ae1-4680-ae28-f05effcc405c
2025-08-04 16:37:08,550 - INFO - 任务完成: 7a92cbfb-9ae1-4680-ae28-f05effcc405c
2025-08-04 16:37:08,550 - INFO -   ✅ pgsql_syntax_error.pcap: SUCCESS
2025-08-04 16:37:09,555 - INFO - 测试进度: 46/47 - pgsql_type_error.pcap
2025-08-04 16:37:09,555 - INFO - 启动测试: pgsql_type_error.pcap
2025-08-04 16:37:09,581 - INFO - 测试任务已启动，任务ID: 1cc60fa8-4e23-46aa-822a-ea5ebde3439f
2025-08-04 16:37:26,660 - INFO - 任务完成: 1cc60fa8-4e23-46aa-822a-ea5ebde3439f
2025-08-04 16:37:26,661 - INFO -   ✅ pgsql_type_error.pcap: SUCCESS
2025-08-04 16:37:27,665 - INFO - 测试进度: 47/47 - pgsql_user_not_exist.pcap
2025-08-04 16:37:27,666 - INFO - 启动测试: pgsql_user_not_exist.pcap
2025-08-04 16:37:27,709 - INFO - 测试任务已启动，任务ID: 0b6a7610-6e18-486d-9a29-440f53592a70
2025-08-04 16:37:45,215 - INFO - 任务完成: 0b6a7610-6e18-486d-9a29-440f53592a70
2025-08-04 16:37:45,215 - INFO -   ✅ pgsql_user_not_exist.pcap: SUCCESS
2025-08-04 16:37:46,219 - INFO - 目录 ../pgsql_abnormal_pcaps 测试完成
2025-08-04 16:37:46,220 - INFO - 批量测试完成
2025-08-04 16:37:46,220 - INFO - 生成测试报告: Unittest.md
2025-08-04 16:37:46,226 - INFO - 测试报告已生成: Unittest.md
2025-08-04 16:37:46,226 - INFO - 测试统计: 总数=47, 成功=44, 失败=3, 成功率=93.6%
2025-08-05 16:30:29,571 - INFO - 开始执行完整的批量测试...
2025-08-05 16:30:29,572 - INFO - 开始批量测试...
2025-08-05 16:30:29,572 - INFO - 开始扫描pcap文件...
2025-08-05 16:30:29,573 - INFO - 在 ../pgsql_pcaps 中找到 38 个pcap文件
2025-08-05 16:30:29,573 - INFO - 在 ../pgsql_abnormal_pcaps 中找到 11 个pcap文件
2025-08-05 16:30:29,573 - INFO - 总共找到 49 个pcap文件
2025-08-05 16:30:29,573 - INFO - 开始测试目录: ../pgsql_pcaps
2025-08-05 16:30:29,573 - INFO - 测试进度: 1/49 - pgsql_auth_with_copy.pcap
2025-08-05 16:30:29,574 - INFO - 启动测试: pgsql_auth_with_copy.pcap
2025-08-05 16:30:29,623 - INFO - 测试任务已启动，任务ID: d9bcf39b-4c6e-455b-baa5-33476c5db5c9
2025-08-05 16:30:40,704 - INFO - 任务完成: d9bcf39b-4c6e-455b-baa5-33476c5db5c9
2025-08-05 16:30:40,704 - INFO -   ✅ pgsql_auth_with_copy.pcap: SUCCESS
2025-08-05 16:30:41,707 - INFO - 测试进度: 2/49 - pgsql_authentication.pcap
2025-08-05 16:30:41,708 - INFO - 启动测试: pgsql_authentication.pcap
2025-08-05 16:30:41,748 - INFO - 测试任务已启动，任务ID: b05c285a-8968-4e3f-ab57-73836d462a16
2025-08-05 16:30:53,826 - INFO - 任务完成: b05c285a-8968-4e3f-ab57-73836d462a16
2025-08-05 16:30:53,826 - INFO -   ✅ pgsql_authentication.pcap: SUCCESS
2025-08-05 16:30:54,829 - INFO - 测试进度: 3/49 - pgsql_batch_operations.pcap
2025-08-05 16:30:54,829 - INFO - 启动测试: pgsql_batch_operations.pcap
2025-08-05 16:30:54,903 - INFO - 测试任务已启动，任务ID: 08e6c8f5-cdb0-4bae-ae34-65173bd527b2
2025-08-05 16:31:06,115 - INFO - 任务完成: 08e6c8f5-cdb0-4bae-ae34-65173bd527b2
2025-08-05 16:31:06,115 - INFO -   ✅ pgsql_batch_operations.pcap: SUCCESS
2025-08-05 16:31:07,119 - INFO - 测试进度: 4/49 - pgsql_binary_copy.pcap
2025-08-05 16:31:07,120 - INFO - 启动测试: pgsql_binary_copy.pcap
2025-08-05 16:31:07,169 - INFO - 测试任务已启动，任务ID: 7f353acf-4e5c-46cd-a53c-6e56e827c22b
2025-08-05 16:31:18,437 - INFO - 任务完成: 7f353acf-4e5c-46cd-a53c-6e56e827c22b
2025-08-05 16:31:18,438 - INFO -   ✅ pgsql_binary_copy.pcap: SUCCESS
2025-08-05 16:31:19,442 - INFO - 测试进度: 5/49 - pgsql_binary_extended_query.pcap
2025-08-05 16:31:19,442 - INFO - 启动测试: pgsql_binary_extended_query.pcap
2025-08-05 16:31:19,476 - INFO - 测试任务已启动，任务ID: 6fcd7ee6-5e68-41b7-ae68-430f6ce46896
2025-08-05 16:31:30,652 - INFO - 任务完成: 6fcd7ee6-5e68-41b7-ae68-430f6ce46896
2025-08-05 16:31:30,653 - INFO -   ✅ pgsql_binary_extended_query.pcap: SUCCESS
2025-08-05 16:31:31,655 - INFO - 测试进度: 6/49 - pgsql_cancel_query.pcap
2025-08-05 16:31:31,656 - INFO - 启动测试: pgsql_cancel_query.pcap
2025-08-05 16:31:31,679 - INFO - 测试任务已启动，任务ID: a6ecfa39-83f8-427b-b032-16e4b3724e02
2025-08-05 16:31:42,880 - INFO - 任务完成: a6ecfa39-83f8-427b-b032-16e4b3724e02
2025-08-05 16:31:42,880 - INFO -   ✅ pgsql_cancel_query.pcap: SUCCESS
2025-08-05 16:31:43,884 - INFO - 测试进度: 7/49 - pgsql_character_encoding.pcap
2025-08-05 16:31:43,885 - INFO - 启动测试: pgsql_character_encoding.pcap
2025-08-05 16:31:43,920 - INFO - 测试任务已启动，任务ID: fbce0d9c-a8f9-4798-b13e-135634186e70
2025-08-05 16:31:55,051 - INFO - 任务完成: fbce0d9c-a8f9-4798-b13e-135634186e70
2025-08-05 16:31:55,051 - INFO -   ✅ pgsql_character_encoding.pcap: SUCCESS
2025-08-05 16:31:56,053 - INFO - 测试进度: 8/49 - pgsql_cleartext_authentication.pcap
2025-08-05 16:31:56,054 - INFO - 启动测试: pgsql_cleartext_authentication.pcap
2025-08-05 16:31:56,130 - INFO - 测试任务已启动，任务ID: 2e8a9915-bae7-460f-ba65-c6bc0c433c95
2025-08-05 16:32:07,237 - INFO - 任务完成: 2e8a9915-bae7-460f-ba65-c6bc0c433c95
2025-08-05 16:32:07,237 - INFO -   ✅ pgsql_cleartext_authentication.pcap: SUCCESS
2025-08-05 16:32:08,242 - INFO - 测试进度: 9/49 - pgsql_close_statement_portal.pcap
2025-08-05 16:32:08,242 - INFO - 启动测试: pgsql_close_statement_portal.pcap
2025-08-05 16:32:08,297 - INFO - 测试任务已启动，任务ID: 19e10620-45a7-41b6-b18e-737289a19a22
2025-08-05 16:32:19,352 - INFO - 任务完成: 19e10620-45a7-41b6-b18e-737289a19a22
2025-08-05 16:32:19,352 - INFO -   ✅ pgsql_close_statement_portal.pcap: SUCCESS
2025-08-05 16:32:20,353 - INFO - 测试进度: 10/49 - pgsql_copy.pcap
2025-08-05 16:32:20,354 - INFO - 启动测试: pgsql_copy.pcap
2025-08-05 16:32:20,401 - INFO - 测试任务已启动，任务ID: 00650272-730f-4135-beb1-c7c8f6482a42
2025-08-05 16:32:31,483 - INFO - 任务完成: 00650272-730f-4135-beb1-c7c8f6482a42
2025-08-05 16:32:31,484 - INFO -   ✅ pgsql_copy.pcap: SUCCESS
2025-08-05 16:32:32,485 - INFO - 测试进度: 11/49 - pgsql_copy_both.pcap
2025-08-05 16:32:32,485 - INFO - 启动测试: pgsql_copy_both.pcap
2025-08-05 16:32:32,515 - INFO - 测试任务已启动，任务ID: 79ee6c2f-d4d7-490d-be17-229efcd89eba
2025-08-05 16:32:39,142 - ERROR - 任务失败: 79ee6c2f-d4d7-490d-be17-229efcd89eba, 状态: failed
2025-08-05 16:32:39,143 - INFO -   ❌ pgsql_copy_both.pcap: FAILED
2025-08-05 16:32:40,145 - INFO - 测试进度: 12/49 - pgsql_copy_from_stdin.pcap
2025-08-05 16:32:40,146 - INFO - 启动测试: pgsql_copy_from_stdin.pcap
2025-08-05 16:32:40,198 - INFO - 测试任务已启动，任务ID: 23498c25-eec8-49a1-92ab-818fb7737d9c
2025-08-05 16:32:46,553 - ERROR - 任务失败: 23498c25-eec8-49a1-92ab-818fb7737d9c, 状态: failed
2025-08-05 16:32:46,554 - INFO -   ❌ pgsql_copy_from_stdin.pcap: FAILED
2025-08-05 16:32:47,554 - INFO - 测试进度: 13/49 - pgsql_copy_to_stdout.pcap
2025-08-05 16:32:47,555 - INFO - 启动测试: pgsql_copy_to_stdout.pcap
2025-08-05 16:32:47,582 - INFO - 测试任务已启动，任务ID: 1e02095f-7371-4e95-b09f-5f59f51aa7a8
2025-08-05 16:33:03,828 - INFO - 任务完成: 1e02095f-7371-4e95-b09f-5f59f51aa7a8
2025-08-05 16:33:03,828 - INFO -   ✅ pgsql_copy_to_stdout.pcap: SUCCESS
2025-08-05 16:33:04,833 - INFO - 测试进度: 14/49 - pgsql_describe_portal.pcap
2025-08-05 16:33:04,833 - INFO - 启动测试: pgsql_describe_portal.pcap
2025-08-05 16:33:04,899 - INFO - 测试任务已启动，任务ID: 7ad84427-b1b6-4a01-b643-dfd466848281
2025-08-05 16:33:16,325 - INFO - 任务完成: 7ad84427-b1b6-4a01-b643-dfd466848281
2025-08-05 16:33:16,325 - INFO -   ✅ pgsql_describe_portal.pcap: SUCCESS
2025-08-05 16:33:17,329 - INFO - 测试进度: 15/49 - pgsql_describe_statement.pcap
2025-08-05 16:33:17,330 - INFO - 启动测试: pgsql_describe_statement.pcap
2025-08-05 16:33:17,377 - INFO - 测试任务已启动，任务ID: a7af5495-9e6c-4407-9c7c-94b56fede0e2
2025-08-05 16:33:28,492 - INFO - 任务完成: a7af5495-9e6c-4407-9c7c-94b56fede0e2
2025-08-05 16:33:28,492 - INFO -   ✅ pgsql_describe_statement.pcap: SUCCESS
2025-08-05 16:33:29,497 - INFO - 测试进度: 16/49 - pgsql_empty_data_handling.pcap
2025-08-05 16:33:29,498 - INFO - 启动测试: pgsql_empty_data_handling.pcap
2025-08-05 16:33:29,559 - INFO - 测试任务已启动，任务ID: ee62766a-83c4-463d-bc8d-4dc8b5126be7
2025-08-05 16:33:40,607 - INFO - 任务完成: ee62766a-83c4-463d-bc8d-4dc8b5126be7
2025-08-05 16:33:40,607 - INFO -   ✅ pgsql_empty_data_handling.pcap: SUCCESS
2025-08-05 16:33:41,610 - INFO - 测试进度: 17/49 - pgsql_flush.pcap
2025-08-05 16:33:41,610 - INFO - 启动测试: pgsql_flush.pcap
2025-08-05 16:33:41,648 - INFO - 测试任务已启动，任务ID: 80aa96a6-c62b-46f1-8fa6-8f159c04a629
2025-08-05 16:33:52,757 - INFO - 任务完成: 80aa96a6-c62b-46f1-8fa6-8f159c04a629
2025-08-05 16:33:52,757 - INFO -   ✅ pgsql_flush.pcap: SUCCESS
2025-08-05 16:33:53,761 - INFO - 测试进度: 18/49 - pgsql_function_call.pcap
2025-08-05 16:33:53,761 - INFO - 启动测试: pgsql_function_call.pcap
2025-08-05 16:33:53,787 - INFO - 测试任务已启动，任务ID: 366c771d-0966-4028-9edf-5fad8c6bd9e1
2025-08-05 16:34:04,860 - INFO - 任务完成: 366c771d-0966-4028-9edf-5fad8c6bd9e1
2025-08-05 16:34:04,861 - INFO -   ✅ pgsql_function_call.pcap: SUCCESS
2025-08-05 16:34:05,866 - INFO - 测试进度: 19/49 - pgsql_large_result.pcap
2025-08-05 16:34:05,867 - INFO - 启动测试: pgsql_large_result.pcap
2025-08-05 16:34:05,924 - INFO - 测试任务已启动，任务ID: 13639d87-1022-40dc-a2c8-081b56852149
2025-08-05 16:34:17,219 - INFO - 任务完成: 13639d87-1022-40dc-a2c8-081b56852149
2025-08-05 16:34:17,220 - INFO -   ✅ pgsql_large_result.pcap: SUCCESS
2025-08-05 16:34:18,224 - INFO - 测试进度: 20/49 - pgsql_max_message_length.pcap
2025-08-05 16:34:18,225 - INFO - 启动测试: pgsql_max_message_length.pcap
2025-08-05 16:34:18,422 - INFO - 测试任务已启动，任务ID: 027513c3-c21e-4cf5-a0d8-d803084fd611
2025-08-05 16:34:29,339 - INFO - 任务完成: 027513c3-c21e-4cf5-a0d8-d803084fd611
2025-08-05 16:34:29,340 - INFO -   ✅ pgsql_max_message_length.pcap: SUCCESS
2025-08-05 16:34:30,343 - INFO - 测试进度: 21/49 - pgsql_max_parameters.pcap
2025-08-05 16:34:30,343 - INFO - 启动测试: pgsql_max_parameters.pcap
2025-08-05 16:34:30,370 - INFO - 测试任务已启动，任务ID: 278de3fd-c07c-43c2-809b-445c7c9695a7
2025-08-05 16:34:41,456 - INFO - 任务完成: 278de3fd-c07c-43c2-809b-445c7c9695a7
2025-08-05 16:34:41,456 - INFO -   ✅ pgsql_max_parameters.pcap: SUCCESS
2025-08-05 16:34:42,484 - INFO - 测试进度: 22/49 - pgsql_max_string_length.pcap
2025-08-05 16:34:42,484 - INFO - 启动测试: pgsql_max_string_length.pcap
2025-08-05 16:34:42,533 - INFO - 测试任务已启动，任务ID: 35b198dd-194d-4d6a-9881-a388e9f4f9f1
2025-08-05 16:34:53,659 - INFO - 任务完成: 35b198dd-194d-4d6a-9881-a388e9f4f9f1
2025-08-05 16:34:53,659 - INFO -   ✅ pgsql_max_string_length.pcap: SUCCESS
2025-08-05 16:34:54,662 - INFO - 测试进度: 23/49 - pgsql_multi_parameter.pcap
2025-08-05 16:34:54,663 - INFO - 启动测试: pgsql_multi_parameter.pcap
2025-08-05 16:34:54,685 - INFO - 测试任务已启动，任务ID: aa813716-f64d-4f82-b70f-539f84550c42
2025-08-05 16:35:05,763 - INFO - 任务完成: aa813716-f64d-4f82-b70f-539f84550c42
2025-08-05 16:35:05,763 - INFO -   ✅ pgsql_multi_parameter.pcap: SUCCESS
2025-08-05 16:35:06,767 - INFO - 测试进度: 24/49 - pgsql_multi_query.pcap
2025-08-05 16:35:06,768 - INFO - 启动测试: pgsql_multi_query.pcap
2025-08-05 16:35:06,811 - INFO - 测试任务已启动，任务ID: 582c5e6f-1105-4729-b290-4bc68241dae5
2025-08-05 16:35:18,256 - INFO - 任务完成: 582c5e6f-1105-4729-b290-4bc68241dae5
2025-08-05 16:35:18,256 - INFO -   ✅ pgsql_multi_query.pcap: SUCCESS
2025-08-05 16:35:19,257 - INFO - 测试进度: 25/49 - pgsql_multiple_data_types.pcap
2025-08-05 16:35:19,258 - INFO - 启动测试: pgsql_multiple_data_types.pcap
2025-08-05 16:35:19,291 - INFO - 测试任务已启动，任务ID: dd894be5-4104-4631-afd6-7d860b04e6ec
2025-08-05 16:35:30,371 - INFO - 任务完成: dd894be5-4104-4631-afd6-7d860b04e6ec
2025-08-05 16:35:30,371 - INFO -   ✅ pgsql_multiple_data_types.pcap: SUCCESS
2025-08-05 16:35:31,375 - INFO - 测试进度: 26/49 - pgsql_nodata.pcap
2025-08-05 16:35:31,376 - INFO - 启动测试: pgsql_nodata.pcap
2025-08-05 16:35:31,412 - INFO - 测试任务已启动，任务ID: 84ff6e35-e9e6-43a9-8be7-ca19b9879b0e
2025-08-05 16:35:42,642 - INFO - 任务完成: 84ff6e35-e9e6-43a9-8be7-ca19b9879b0e
2025-08-05 16:35:42,643 - INFO -   ✅ pgsql_nodata.pcap: SUCCESS
2025-08-05 16:35:43,648 - INFO - 测试进度: 27/49 - pgsql_notice_response.pcap
2025-08-05 16:35:43,648 - INFO - 启动测试: pgsql_notice_response.pcap
2025-08-05 16:35:43,714 - INFO - 测试任务已启动，任务ID: 004334dc-1183-406b-b640-74f94156a538
2025-08-05 16:35:54,838 - INFO - 任务完成: 004334dc-1183-406b-b640-74f94156a538
2025-08-05 16:35:54,838 - INFO -   ✅ pgsql_notice_response.pcap: SUCCESS
2025-08-05 16:35:55,841 - INFO - 测试进度: 28/49 - pgsql_notification.pcap
2025-08-05 16:35:55,842 - INFO - 启动测试: pgsql_notification.pcap
2025-08-05 16:35:55,919 - INFO - 测试任务已启动，任务ID: 1320121a-8f09-4f2c-9a92-13a243a61e6c
2025-08-05 16:36:07,008 - INFO - 任务完成: 1320121a-8f09-4f2c-9a92-13a243a61e6c
2025-08-05 16:36:07,009 - INFO -   ✅ pgsql_notification.pcap: SUCCESS
2025-08-05 16:36:08,012 - INFO - 测试进度: 29/49 - pgsql_pipelined_queries.pcap
2025-08-05 16:36:08,013 - INFO - 启动测试: pgsql_pipelined_queries.pcap
2025-08-05 16:36:08,034 - INFO - 测试任务已启动，任务ID: 453da38f-10d0-45f5-ab3c-bee870f525f9
2025-08-05 16:36:19,078 - INFO - 任务完成: 453da38f-10d0-45f5-ab3c-bee870f525f9
2025-08-05 16:36:19,079 - INFO -   ✅ pgsql_pipelined_queries.pcap: SUCCESS
2025-08-05 16:36:20,081 - INFO - 测试进度: 30/49 - pgsql_prepared_statement.pcap
2025-08-05 16:36:20,081 - INFO - 启动测试: pgsql_prepared_statement.pcap
2025-08-05 16:36:20,099 - INFO - 测试任务已启动，任务ID: d0c25668-9522-4e03-aa7a-92dc38939aac
2025-08-05 16:36:31,175 - INFO - 任务完成: d0c25668-9522-4e03-aa7a-92dc38939aac
2025-08-05 16:36:31,175 - INFO -   ✅ pgsql_prepared_statement.pcap: SUCCESS
2025-08-05 16:36:32,179 - INFO - 测试进度: 31/49 - pgsql_replication_feedback.pcap
2025-08-05 16:36:32,179 - INFO - 启动测试: pgsql_replication_feedback.pcap
2025-08-05 16:36:32,197 - INFO - 测试任务已启动，任务ID: 044f94b8-e960-4169-994e-eb5db5ae0e04
2025-08-05 16:36:43,492 - INFO - 任务完成: 044f94b8-e960-4169-994e-eb5db5ae0e04
2025-08-05 16:36:43,493 - INFO -   ❌ pgsql_replication_feedback.pcap: NO_JSON_EVENTS
2025-08-05 16:36:44,498 - INFO - 测试进度: 32/49 - pgsql_sasl_authentication.pcap
2025-08-05 16:36:44,499 - INFO - 启动测试: pgsql_sasl_authentication.pcap
2025-08-05 16:36:44,542 - INFO - 测试任务已启动，任务ID: b9b828d1-4395-4b01-98cd-e2b713a6b036
2025-08-05 16:36:55,822 - INFO - 任务完成: b9b828d1-4395-4b01-98cd-e2b713a6b036
2025-08-05 16:36:55,823 - INFO -   ✅ pgsql_sasl_authentication.pcap: SUCCESS
2025-08-05 16:36:56,823 - INFO - 测试进度: 33/49 - pgsql_simple_query.pcap
2025-08-05 16:36:56,824 - INFO - 启动测试: pgsql_simple_query.pcap
2025-08-05 16:36:56,851 - INFO - 测试任务已启动，任务ID: 6b6f285b-1935-4d29-a5ba-abc8e50ab1fc
2025-08-05 16:37:07,887 - INFO - 任务完成: 6b6f285b-1935-4d29-a5ba-abc8e50ab1fc
2025-08-05 16:37:07,887 - INFO -   ✅ pgsql_simple_query.pcap: SUCCESS
2025-08-05 16:37:08,889 - INFO - 测试进度: 34/49 - pgsql_ssl_request_not_supported.pcap
2025-08-05 16:37:08,890 - INFO - 启动测试: pgsql_ssl_request_not_supported.pcap
2025-08-05 16:37:08,923 - INFO - 测试任务已启动，任务ID: 2647cf02-5e2c-419f-901d-df5250fd3209
2025-08-05 16:37:20,013 - INFO - 任务完成: 2647cf02-5e2c-419f-901d-df5250fd3209
2025-08-05 16:37:20,014 - INFO -   ✅ pgsql_ssl_request_not_supported.pcap: SUCCESS
2025-08-05 16:37:21,016 - INFO - 测试进度: 35/49 - pgsql_start_replication.pcap
2025-08-05 16:37:21,016 - INFO - 启动测试: pgsql_start_replication.pcap
2025-08-05 16:37:21,048 - INFO - 测试任务已启动，任务ID: f3323cd3-c384-4c99-b77f-575e76032cb8
2025-08-05 16:37:32,154 - INFO - 任务完成: f3323cd3-c384-4c99-b77f-575e76032cb8
2025-08-05 16:37:32,154 - INFO -   ✅ pgsql_start_replication.pcap: SUCCESS
2025-08-05 16:37:33,155 - INFO - 测试进度: 36/49 - pgsql_sync_separated_transactions.pcap
2025-08-05 16:37:33,155 - INFO - 启动测试: pgsql_sync_separated_transactions.pcap
2025-08-05 16:37:33,176 - INFO - 测试任务已启动，任务ID: f713edc2-6ddc-4a5a-a76e-89b6bbcb55fc
2025-08-05 16:37:44,258 - INFO - 任务完成: f713edc2-6ddc-4a5a-a76e-89b6bbcb55fc
2025-08-05 16:37:44,259 - INFO -   ✅ pgsql_sync_separated_transactions.pcap: SUCCESS
2025-08-05 16:37:45,262 - INFO - 测试进度: 37/49 - pgsql_transaction.pcap
2025-08-05 16:37:45,262 - INFO - 启动测试: pgsql_transaction.pcap
2025-08-05 16:37:45,286 - INFO - 测试任务已启动，任务ID: 3886273b-5f9f-4576-b643-b93efafef726
2025-08-05 16:37:56,340 - INFO - 任务完成: 3886273b-5f9f-4576-b643-b93efafef726
2025-08-05 16:37:56,340 - INFO -   ✅ pgsql_transaction.pcap: SUCCESS
2025-08-05 16:37:57,345 - INFO - 测试进度: 38/49 - pgsql_wal_streaming.pcap
2025-08-05 16:37:57,345 - INFO - 启动测试: pgsql_wal_streaming.pcap
2025-08-05 16:37:57,359 - INFO - 测试任务已启动，任务ID: 07a0d02a-49bd-4615-9d67-ff815b244032
2025-08-05 16:38:08,423 - INFO - 任务完成: 07a0d02a-49bd-4615-9d67-ff815b244032
2025-08-05 16:38:08,424 - INFO -   ❌ pgsql_wal_streaming.pcap: NO_JSON_EVENTS
2025-08-05 16:38:09,427 - INFO - 目录 ../pgsql_pcaps 测试完成
2025-08-05 16:38:09,428 - INFO - 开始测试目录: ../pgsql_abnormal_pcaps
2025-08-05 16:38:09,428 - INFO - 测试进度: 39/49 - pgsql_auth_failure.pcap
2025-08-05 16:38:09,428 - INFO - 启动测试: pgsql_auth_failure.pcap
2025-08-05 16:38:09,446 - INFO - 测试任务已启动，任务ID: 9ba8eb6e-1a56-4bbf-bc11-a91a4ad82cc7
2025-08-05 16:38:20,507 - INFO - 任务完成: 9ba8eb6e-1a56-4bbf-bc11-a91a4ad82cc7
2025-08-05 16:38:20,508 - INFO -   ✅ pgsql_auth_failure.pcap: SUCCESS
2025-08-05 16:38:21,512 - INFO - 测试进度: 40/49 - pgsql_constraint_violation.pcap
2025-08-05 16:38:21,513 - INFO - 启动测试: pgsql_constraint_violation.pcap
2025-08-05 16:38:21,530 - INFO - 测试任务已启动，任务ID: 2f5a0fc5-1248-4af7-a1d4-a7c208e50b27
2025-08-05 16:38:32,526 - INFO - 任务完成: 2f5a0fc5-1248-4af7-a1d4-a7c208e50b27
2025-08-05 16:38:32,526 - INFO -   ✅ pgsql_constraint_violation.pcap: SUCCESS
2025-08-05 16:38:33,530 - INFO - 测试进度: 41/49 - pgsql_copy_fail.pcap
2025-08-05 16:38:33,530 - INFO - 启动测试: pgsql_copy_fail.pcap
2025-08-05 16:38:33,556 - INFO - 测试任务已启动，任务ID: 9f7d6a33-8378-4078-a584-9fb6d0a7bbb6
2025-08-05 16:38:44,775 - INFO - 任务完成: 9f7d6a33-8378-4078-a584-9fb6d0a7bbb6
2025-08-05 16:38:44,776 - INFO -   ✅ pgsql_copy_fail.pcap: SUCCESS
2025-08-05 16:38:45,780 - INFO - 测试进度: 42/49 - pgsql_error.pcap
2025-08-05 16:38:45,781 - INFO - 启动测试: pgsql_error.pcap
2025-08-05 16:38:45,797 - INFO - 测试任务已启动，任务ID: 52c41452-b61f-4be0-bc07-e27af1734318
2025-08-05 16:39:01,880 - INFO - 任务完成: 52c41452-b61f-4be0-bc07-e27af1734318
2025-08-05 16:39:01,880 - INFO -   ✅ pgsql_error.pcap: SUCCESS
2025-08-05 16:39:02,882 - INFO - 测试进度: 43/49 - pgsql_fatal_error.pcap
2025-08-05 16:39:02,882 - INFO - 启动测试: pgsql_fatal_error.pcap
2025-08-05 16:39:02,900 - INFO - 测试任务已启动，任务ID: e4490b65-b1b7-4c34-8a11-e8afe06b6b78
2025-08-05 16:39:14,071 - INFO - 任务完成: e4490b65-b1b7-4c34-8a11-e8afe06b6b78
2025-08-05 16:39:14,071 - INFO -   ✅ pgsql_fatal_error.pcap: SUCCESS
2025-08-05 16:39:15,077 - INFO - 测试进度: 44/49 - pgsql_function_call_error.pcap
2025-08-05 16:39:15,079 - INFO - 启动测试: pgsql_function_call_error.pcap
2025-08-05 16:39:15,096 - INFO - 测试任务已启动，任务ID: b6dbad18-923b-4616-a3d0-e13782b4cc58
2025-08-05 16:39:26,290 - INFO - 任务完成: b6dbad18-923b-4616-a3d0-e13782b4cc58
2025-08-05 16:39:26,291 - INFO -   ✅ pgsql_function_call_error.pcap: SUCCESS
2025-08-05 16:39:27,293 - INFO - 测试进度: 45/49 - pgsql_panic_error.pcap
2025-08-05 16:39:27,293 - INFO - 启动测试: pgsql_panic_error.pcap
2025-08-05 16:39:27,312 - INFO - 测试任务已启动，任务ID: 5731dabe-e80c-472b-a901-1051fd7f9b2e
2025-08-05 16:39:38,488 - INFO - 任务完成: 5731dabe-e80c-472b-a901-1051fd7f9b2e
2025-08-05 16:39:38,488 - INFO -   ✅ pgsql_panic_error.pcap: SUCCESS
2025-08-05 16:39:39,493 - INFO - 测试进度: 46/49 - pgsql_pipelined_error_handling.pcap
2025-08-05 16:39:39,493 - INFO - 启动测试: pgsql_pipelined_error_handling.pcap
2025-08-05 16:39:39,513 - INFO - 测试任务已启动，任务ID: b010e17b-8ca7-42f0-a46b-d261d42b6969
2025-08-05 16:39:50,652 - INFO - 任务完成: b010e17b-8ca7-42f0-a46b-d261d42b6969
2025-08-05 16:39:50,653 - INFO -   ✅ pgsql_pipelined_error_handling.pcap: SUCCESS
2025-08-05 16:39:51,659 - INFO - 测试进度: 47/49 - pgsql_syntax_error.pcap
2025-08-05 16:39:51,659 - INFO - 启动测试: pgsql_syntax_error.pcap
2025-08-05 16:39:51,679 - INFO - 测试任务已启动，任务ID: 6d98c178-a8b4-4b2b-bf66-439ef3bf7edc
2025-08-05 16:40:07,755 - INFO - 任务完成: 6d98c178-a8b4-4b2b-bf66-439ef3bf7edc
2025-08-05 16:40:07,755 - INFO -   ✅ pgsql_syntax_error.pcap: SUCCESS
2025-08-05 16:40:08,760 - INFO - 测试进度: 48/49 - pgsql_type_error.pcap
2025-08-05 16:40:08,760 - INFO - 启动测试: pgsql_type_error.pcap
2025-08-05 16:40:08,791 - INFO - 测试任务已启动，任务ID: 1201e133-4c30-43a8-b13d-1e745ecd99fb
2025-08-05 16:40:20,154 - INFO - 任务完成: 1201e133-4c30-43a8-b13d-1e745ecd99fb
2025-08-05 16:40:20,155 - INFO -   ✅ pgsql_type_error.pcap: SUCCESS
2025-08-05 16:40:21,156 - INFO - 测试进度: 49/49 - pgsql_user_not_exist.pcap
2025-08-05 16:40:21,157 - INFO - 启动测试: pgsql_user_not_exist.pcap
2025-08-05 16:40:21,176 - INFO - 测试任务已启动，任务ID: 5794a032-11f4-4061-b11d-58d1aca62584
2025-08-05 16:40:32,232 - INFO - 任务完成: 5794a032-11f4-4061-b11d-58d1aca62584
2025-08-05 16:40:32,232 - INFO -   ✅ pgsql_user_not_exist.pcap: SUCCESS
2025-08-05 16:40:33,237 - INFO - 目录 ../pgsql_abnormal_pcaps 测试完成
2025-08-05 16:40:33,237 - INFO - 批量测试完成
2025-08-05 16:40:33,237 - INFO - 生成测试报告: report/Unittest2.md
2025-08-05 16:40:33,239 - INFO - 测试报告已生成: report/Unittest2.md
2025-08-05 16:40:33,239 - INFO - 测试统计: 总数=49, 成功=45, 失败=4, 成功率=91.8%
2025-08-05 16:51:25,502 - INFO - 测试单个文件: pgsql_authentication.pcap
2025-08-05 16:51:25,503 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,504 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,504 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,504 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,504 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,504 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,504 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,504 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,505 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,505 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,505 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,505 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,505 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,505 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,505 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,505 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,505 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,505 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,505 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,506 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,506 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,506 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,506 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,506 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,506 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,506 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,506 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,506 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,506 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,507 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,507 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,507 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,507 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,507 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,508 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,508 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,509 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,511 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,511 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,512 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,512 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,512 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,512 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,512 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,512 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,513 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,513 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,513 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,513 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,513 - WARNING - 解析预期事件JSON失败: Expecting value: line 1 column 15 (char 14)
2025-08-05 16:51:25,513 - INFO - 成功加载 50 个文件的预期事件
2025-08-05 16:51:25,514 - INFO - 启动测试: pgsql_authentication.pcap
2025-08-05 16:51:25,542 - INFO - 测试任务已启动，任务ID: 0c4f5f7e-4eb6-4a13-a4e5-52feb6117a8b
2025-08-05 16:51:36,666 - INFO - 任务完成: 0c4f5f7e-4eb6-4a13-a4e5-52feb6117a8b
2025-08-05 16:53:00,861 - INFO - 测试单个文件: pgsql_authentication.pcap
2025-08-05 16:53:00,864 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 208 (char 207), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-05 16:53:00,865 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 229 (char 228), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-05 16:53:00,865 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 246 (char 245), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-05 16:53:00,866 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 245 (char 244), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-05 16:53:00,866 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 227 (char 226), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-05 16:53:00,867 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 319 (char 318), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-05 16:53:00,867 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 223 (char 222), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-05 16:53:00,869 - INFO - 成功加载 50 个文件的预期事件
2025-08-05 16:53:00,869 - INFO - 启动测试: pgsql_authentication.pcap
2025-08-05 16:53:00,886 - INFO - 测试任务已启动，任务ID: 35fea8d0-4bc1-448c-b0b3-ba5b7ffb074a
2025-08-05 16:53:17,010 - INFO - 任务完成: 35fea8d0-4bc1-448c-b0b3-ba5b7ffb074a
2025-08-05 16:53:30,631 - INFO - 开始执行完整的批量测试...
2025-08-05 16:53:30,631 - INFO - 开始批量测试...
2025-08-05 16:53:30,631 - INFO - 开始扫描pcap文件...
2025-08-05 16:53:30,632 - INFO - 在 ../pgsql_pcaps 中找到 38 个pcap文件
2025-08-05 16:53:30,632 - INFO - 在 ../pgsql_abnormal_pcaps 中找到 11 个pcap文件
2025-08-05 16:53:30,632 - INFO - 总共找到 49 个pcap文件
2025-08-05 16:53:30,632 - INFO - 加载预期Kafka事件...
2025-08-05 16:53:30,634 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 208 (char 207), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-05 16:53:30,634 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 229 (char 228), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-05 16:53:30,634 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 246 (char 245), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-05 16:53:30,635 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 245 (char 244), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-05 16:53:30,635 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 227 (char 226), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-05 16:53:30,635 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 319 (char 318), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-05 16:53:30,636 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 223 (char 222), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-05 16:53:30,637 - INFO - 成功加载 50 个文件的预期事件
2025-08-05 16:53:30,637 - INFO - 开始测试目录: ../pgsql_pcaps
2025-08-05 16:53:30,637 - INFO - 测试进度: 1/49 - pgsql_auth_with_copy.pcap
2025-08-05 16:53:30,637 - INFO - 启动测试: pgsql_auth_with_copy.pcap
2025-08-05 16:53:30,660 - INFO - 测试任务已启动，任务ID: 058e5005-e7a3-4f84-99b0-cd9c1f4db492
2025-08-05 16:53:41,802 - INFO - 任务完成: 058e5005-e7a3-4f84-99b0-cd9c1f4db492
2025-08-05 16:53:41,803 - INFO -   ✅ pgsql_auth_with_copy.pcap: SUCCESS
2025-08-05 16:53:42,808 - INFO - 测试进度: 2/49 - pgsql_authentication.pcap
2025-08-05 16:53:42,808 - INFO - 启动测试: pgsql_authentication.pcap
2025-08-05 16:53:42,830 - INFO - 测试任务已启动，任务ID: 01855cb7-2610-42a6-88c8-b1449fa70f36
2025-08-05 16:53:53,974 - INFO - 任务完成: 01855cb7-2610-42a6-88c8-b1449fa70f36
2025-08-05 16:53:53,974 - INFO -   ✅ pgsql_authentication.pcap: SUCCESS
2025-08-05 16:53:54,977 - INFO - 测试进度: 3/49 - pgsql_batch_operations.pcap
2025-08-05 16:53:54,978 - INFO - 启动测试: pgsql_batch_operations.pcap
2025-08-05 16:53:55,000 - INFO - 测试任务已启动，任务ID: 939e4326-4330-4b3b-bca4-f684cf79970f
2025-08-05 16:54:06,074 - INFO - 任务完成: 939e4326-4330-4b3b-bca4-f684cf79970f
2025-08-05 16:54:06,075 - INFO -   ✅ pgsql_batch_operations.pcap: SUCCESS
2025-08-05 16:54:07,079 - INFO - 测试进度: 4/49 - pgsql_binary_copy.pcap
2025-08-05 16:54:07,080 - INFO - 启动测试: pgsql_binary_copy.pcap
2025-08-05 16:54:07,097 - INFO - 测试任务已启动，任务ID: 35f391a8-0bc5-4893-94dc-e0d866b6eb3c
2025-08-05 16:54:23,240 - INFO - 任务完成: 35f391a8-0bc5-4893-94dc-e0d866b6eb3c
2025-08-05 16:54:23,241 - INFO -   ✅ pgsql_binary_copy.pcap: SUCCESS
2025-08-05 16:54:24,246 - INFO - 测试进度: 5/49 - pgsql_binary_extended_query.pcap
2025-08-05 16:54:24,247 - INFO - 启动测试: pgsql_binary_extended_query.pcap
2025-08-05 16:54:24,271 - INFO - 测试任务已启动，任务ID: 31c53952-3b09-4f78-aca5-a5c2acae60f3
2025-08-05 16:54:35,376 - INFO - 任务完成: 31c53952-3b09-4f78-aca5-a5c2acae60f3
2025-08-05 16:54:35,377 - INFO -   ✅ pgsql_binary_extended_query.pcap: SUCCESS
2025-08-05 16:54:36,379 - INFO - 测试进度: 6/49 - pgsql_cancel_query.pcap
2025-08-05 16:54:36,380 - INFO - 启动测试: pgsql_cancel_query.pcap
2025-08-05 16:54:36,403 - INFO - 测试任务已启动，任务ID: a148e8f3-3bca-4d44-8b6d-a5c046e3f24b
2025-08-05 16:54:47,486 - INFO - 任务完成: a148e8f3-3bca-4d44-8b6d-a5c046e3f24b
2025-08-05 16:54:47,486 - INFO -   ✅ pgsql_cancel_query.pcap: SUCCESS
2025-08-05 16:54:48,492 - INFO - 测试进度: 7/49 - pgsql_character_encoding.pcap
2025-08-05 16:54:48,493 - INFO - 启动测试: pgsql_character_encoding.pcap
2025-08-05 16:54:48,510 - INFO - 测试任务已启动，任务ID: b63e5cd8-4cb2-4eb3-9712-e4ed8760ddf3
2025-08-05 16:54:59,711 - INFO - 任务完成: b63e5cd8-4cb2-4eb3-9712-e4ed8760ddf3
2025-08-05 16:54:59,711 - INFO -   ✅ pgsql_character_encoding.pcap: SUCCESS
2025-08-05 16:55:00,715 - INFO - 测试进度: 8/49 - pgsql_cleartext_authentication.pcap
2025-08-05 16:55:00,715 - INFO - 启动测试: pgsql_cleartext_authentication.pcap
2025-08-05 16:55:00,779 - INFO - 测试任务已启动，任务ID: 85b56078-cda9-4cff-bd14-ca8c941d967e
2025-08-05 16:55:11,981 - INFO - 任务完成: 85b56078-cda9-4cff-bd14-ca8c941d967e
2025-08-05 16:55:11,982 - INFO -   ✅ pgsql_cleartext_authentication.pcap: SUCCESS
2025-08-05 16:55:12,985 - INFO - 测试进度: 9/49 - pgsql_close_statement_portal.pcap
2025-08-05 16:55:12,985 - INFO - 启动测试: pgsql_close_statement_portal.pcap
2025-08-05 16:55:14,022 - INFO - 测试任务已启动，任务ID: f29fe6f5-bb6d-4448-8ed6-188b6e5047b0
2025-08-05 16:55:25,095 - INFO - 任务完成: f29fe6f5-bb6d-4448-8ed6-188b6e5047b0
2025-08-05 16:55:25,096 - INFO -   ✅ pgsql_close_statement_portal.pcap: SUCCESS
2025-08-05 16:55:26,101 - INFO - 测试进度: 10/49 - pgsql_copy.pcap
2025-08-05 16:55:26,115 - INFO - 启动测试: pgsql_copy.pcap
2025-08-05 16:55:26,134 - INFO - 测试任务已启动，任务ID: 553474c8-8b9a-41a7-aec2-c47d24b99cd4
2025-08-05 16:55:37,173 - INFO - 任务完成: 553474c8-8b9a-41a7-aec2-c47d24b99cd4
2025-08-05 16:55:37,174 - INFO -   ✅ pgsql_copy.pcap: SUCCESS
2025-08-05 16:55:38,179 - INFO - 测试进度: 11/49 - pgsql_copy_both.pcap
2025-08-05 16:55:38,180 - INFO - 启动测试: pgsql_copy_both.pcap
2025-08-05 16:55:38,198 - INFO - 测试任务已启动，任务ID: 5814dfb8-cfee-4d1e-be9a-c5a57fef1286
2025-08-05 16:55:50,297 - INFO - 任务完成: 5814dfb8-cfee-4d1e-be9a-c5a57fef1286
2025-08-05 16:55:50,298 - INFO -   ✅ pgsql_copy_both.pcap: SUCCESS
2025-08-05 16:55:51,302 - INFO - 测试进度: 12/49 - pgsql_copy_from_stdin.pcap
2025-08-05 16:55:51,303 - INFO - 启动测试: pgsql_copy_from_stdin.pcap
2025-08-05 16:55:51,321 - INFO - 测试任务已启动，任务ID: f7077a89-e8c6-49d3-93a3-4871c0030ca4
2025-08-05 16:56:02,345 - INFO - 任务完成: f7077a89-e8c6-49d3-93a3-4871c0030ca4
2025-08-05 16:56:02,346 - INFO -   ✅ pgsql_copy_from_stdin.pcap: SUCCESS
2025-08-05 16:56:03,348 - INFO - 测试进度: 13/49 - pgsql_copy_to_stdout.pcap
2025-08-05 16:56:03,349 - INFO - 启动测试: pgsql_copy_to_stdout.pcap
2025-08-05 16:56:03,364 - INFO - 测试任务已启动，任务ID: e0e2b37c-dab9-4991-ba6a-c68bad0d7afb
2025-08-05 16:56:14,379 - INFO - 任务完成: e0e2b37c-dab9-4991-ba6a-c68bad0d7afb
2025-08-05 16:56:14,380 - INFO -   ✅ pgsql_copy_to_stdout.pcap: SUCCESS
2025-08-05 16:56:15,385 - INFO - 测试进度: 14/49 - pgsql_describe_portal.pcap
2025-08-05 16:56:15,385 - INFO - 启动测试: pgsql_describe_portal.pcap
2025-08-05 16:56:15,403 - INFO - 测试任务已启动，任务ID: d29d82b1-2929-460d-8fa8-a12f0282ddb5
2025-08-05 16:56:26,442 - INFO - 任务完成: d29d82b1-2929-460d-8fa8-a12f0282ddb5
2025-08-05 16:56:26,443 - INFO -   ✅ pgsql_describe_portal.pcap: SUCCESS
2025-08-05 16:56:27,445 - INFO - 测试进度: 15/49 - pgsql_describe_statement.pcap
2025-08-05 16:56:27,446 - INFO - 启动测试: pgsql_describe_statement.pcap
2025-08-05 16:56:27,465 - INFO - 测试任务已启动，任务ID: c97ec7d3-06d4-4b33-95cc-55880fc8d1fe
2025-08-05 16:56:43,577 - INFO - 任务完成: c97ec7d3-06d4-4b33-95cc-55880fc8d1fe
2025-08-05 16:56:43,578 - INFO -   ✅ pgsql_describe_statement.pcap: SUCCESS
2025-08-05 16:56:44,581 - INFO - 测试进度: 16/49 - pgsql_empty_data_handling.pcap
2025-08-05 16:56:44,582 - INFO - 启动测试: pgsql_empty_data_handling.pcap
2025-08-05 16:56:44,631 - INFO - 测试任务已启动，任务ID: 57bc3389-57ce-4430-b88a-b4f4c00ebe0a
2025-08-05 16:56:55,788 - INFO - 任务完成: 57bc3389-57ce-4430-b88a-b4f4c00ebe0a
2025-08-05 16:56:55,788 - INFO -   ✅ pgsql_empty_data_handling.pcap: SUCCESS
2025-08-05 16:56:56,793 - INFO - 测试进度: 17/49 - pgsql_flush.pcap
2025-08-05 16:56:56,793 - INFO - 启动测试: pgsql_flush.pcap
2025-08-05 16:56:56,813 - INFO - 测试任务已启动，任务ID: e63486a0-6762-45b3-968b-860e7a69f720
2025-08-05 16:57:07,847 - INFO - 任务完成: e63486a0-6762-45b3-968b-860e7a69f720
2025-08-05 16:57:07,848 - INFO -   ✅ pgsql_flush.pcap: SUCCESS
2025-08-05 16:57:08,849 - INFO - 测试进度: 18/49 - pgsql_function_call.pcap
2025-08-05 16:57:08,849 - INFO - 启动测试: pgsql_function_call.pcap
2025-08-05 16:57:08,900 - INFO - 测试任务已启动，任务ID: 597e5775-7b3c-4647-8879-94ba4ee1b3ec
2025-08-05 16:57:19,898 - INFO - 任务完成: 597e5775-7b3c-4647-8879-94ba4ee1b3ec
2025-08-05 16:57:19,899 - INFO -   ✅ pgsql_function_call.pcap: SUCCESS
2025-08-05 16:57:20,903 - INFO - 测试进度: 19/49 - pgsql_large_result.pcap
2025-08-05 16:57:20,903 - INFO - 启动测试: pgsql_large_result.pcap
2025-08-05 16:57:20,917 - INFO - 测试任务已启动，任务ID: ad710414-b244-4701-9ba5-e4d3098627d4
2025-08-05 16:57:32,047 - INFO - 任务完成: ad710414-b244-4701-9ba5-e4d3098627d4
2025-08-05 16:57:32,048 - INFO -   ✅ pgsql_large_result.pcap: SUCCESS
2025-08-05 16:57:33,049 - INFO - 测试进度: 20/49 - pgsql_max_message_length.pcap
2025-08-05 16:57:33,049 - INFO - 启动测试: pgsql_max_message_length.pcap
2025-08-05 16:57:33,062 - INFO - 测试任务已启动，任务ID: cc663405-ce72-44d1-a484-53425d25a025
2025-08-05 16:57:44,245 - INFO - 任务完成: cc663405-ce72-44d1-a484-53425d25a025
2025-08-05 16:57:44,246 - INFO -   ✅ pgsql_max_message_length.pcap: SUCCESS
2025-08-05 16:57:45,269 - INFO - 测试进度: 21/49 - pgsql_max_parameters.pcap
2025-08-05 16:57:45,269 - INFO - 启动测试: pgsql_max_parameters.pcap
2025-08-05 16:57:45,311 - INFO - 测试任务已启动，任务ID: d8fb90b8-6f3f-443e-97b8-b0218a36492b
2025-08-05 16:57:56,408 - INFO - 任务完成: d8fb90b8-6f3f-443e-97b8-b0218a36492b
2025-08-05 16:57:56,409 - INFO -   ✅ pgsql_max_parameters.pcap: SUCCESS
2025-08-05 16:57:57,414 - INFO - 测试进度: 22/49 - pgsql_max_string_length.pcap
2025-08-05 16:57:57,414 - INFO - 启动测试: pgsql_max_string_length.pcap
2025-08-05 16:57:57,430 - INFO - 测试任务已启动，任务ID: b15185a9-2e80-4989-a07a-ff809444fa07
2025-08-05 16:58:08,518 - INFO - 任务完成: b15185a9-2e80-4989-a07a-ff809444fa07
2025-08-05 16:58:08,519 - INFO -   ✅ pgsql_max_string_length.pcap: SUCCESS
2025-08-05 16:58:09,523 - INFO - 测试进度: 23/49 - pgsql_multi_parameter.pcap
2025-08-05 16:58:09,523 - INFO - 启动测试: pgsql_multi_parameter.pcap
2025-08-05 16:58:09,541 - INFO - 测试任务已启动，任务ID: 7f54c566-eb78-463a-aa41-1fef22fb1896
2025-08-05 16:58:20,779 - INFO - 任务完成: 7f54c566-eb78-463a-aa41-1fef22fb1896
2025-08-05 16:58:20,779 - INFO -   ✅ pgsql_multi_parameter.pcap: SUCCESS
2025-08-05 16:58:21,782 - INFO - 测试进度: 24/49 - pgsql_multi_query.pcap
2025-08-05 16:58:21,782 - INFO - 启动测试: pgsql_multi_query.pcap
2025-08-05 16:58:21,799 - INFO - 测试任务已启动，任务ID: aad057dd-1d95-442e-b0b8-1cf4c1c1db7b
2025-08-05 16:58:32,904 - INFO - 任务完成: aad057dd-1d95-442e-b0b8-1cf4c1c1db7b
2025-08-05 16:58:32,904 - INFO -   ✅ pgsql_multi_query.pcap: SUCCESS
2025-08-05 16:58:33,905 - INFO - 测试进度: 25/49 - pgsql_multiple_data_types.pcap
2025-08-05 16:58:33,905 - INFO - 启动测试: pgsql_multiple_data_types.pcap
2025-08-05 16:58:33,940 - INFO - 测试任务已启动，任务ID: b3566023-0a10-4a44-a70e-85d46af61d7b
2025-08-05 16:58:45,027 - INFO - 任务完成: b3566023-0a10-4a44-a70e-85d46af61d7b
2025-08-05 16:58:45,028 - INFO -   ✅ pgsql_multiple_data_types.pcap: SUCCESS
2025-08-05 16:58:46,032 - INFO - 测试进度: 26/49 - pgsql_nodata.pcap
2025-08-05 16:58:46,032 - INFO - 启动测试: pgsql_nodata.pcap
2025-08-05 16:58:46,047 - INFO - 测试任务已启动，任务ID: 472b09f4-8cf0-41d8-8b60-1c21cf364215
2025-08-05 16:58:57,070 - INFO - 任务完成: 472b09f4-8cf0-41d8-8b60-1c21cf364215
2025-08-05 16:58:57,071 - INFO -   ✅ pgsql_nodata.pcap: SUCCESS
2025-08-05 16:58:58,076 - INFO - 测试进度: 27/49 - pgsql_notice_response.pcap
2025-08-05 16:58:58,076 - INFO - 启动测试: pgsql_notice_response.pcap
2025-08-05 16:58:58,090 - INFO - 测试任务已启动，任务ID: 9edf5bec-15f4-446d-bb24-b8181d01b11f
2025-08-05 16:59:09,115 - INFO - 任务完成: 9edf5bec-15f4-446d-bb24-b8181d01b11f
2025-08-05 16:59:09,117 - INFO -   ✅ pgsql_notice_response.pcap: SUCCESS
2025-08-05 16:59:10,121 - INFO - 测试进度: 28/49 - pgsql_notification.pcap
2025-08-05 16:59:10,121 - INFO - 启动测试: pgsql_notification.pcap
2025-08-05 16:59:10,134 - INFO - 测试任务已启动，任务ID: 3d25e62a-2818-4843-8cb6-d4ae8f3f5e1c
2025-08-05 16:59:26,234 - INFO - 任务完成: 3d25e62a-2818-4843-8cb6-d4ae8f3f5e1c
2025-08-05 16:59:26,236 - INFO -   ✅ pgsql_notification.pcap: SUCCESS
2025-08-05 16:59:27,241 - INFO - 测试进度: 29/49 - pgsql_pipelined_queries.pcap
2025-08-05 16:59:27,241 - INFO - 启动测试: pgsql_pipelined_queries.pcap
2025-08-05 16:59:27,254 - INFO - 测试任务已启动，任务ID: d74b1125-ce84-4066-bbeb-6318f614a448
2025-08-05 16:59:38,604 - INFO - 任务完成: d74b1125-ce84-4066-bbeb-6318f614a448
2025-08-05 16:59:38,604 - INFO -   ✅ pgsql_pipelined_queries.pcap: SUCCESS
2025-08-05 16:59:39,608 - INFO - 测试进度: 30/49 - pgsql_prepared_statement.pcap
2025-08-05 16:59:39,608 - INFO - 启动测试: pgsql_prepared_statement.pcap
2025-08-05 16:59:39,635 - INFO - 测试任务已启动，任务ID: 5fbf679e-700b-4864-b78a-c62cc41a34a9
2025-08-05 16:59:50,661 - INFO - 任务完成: 5fbf679e-700b-4864-b78a-c62cc41a34a9
2025-08-05 16:59:50,661 - INFO -   ✅ pgsql_prepared_statement.pcap: SUCCESS
2025-08-05 16:59:51,666 - INFO - 测试进度: 31/49 - pgsql_replication_feedback.pcap
2025-08-05 16:59:51,666 - INFO - 启动测试: pgsql_replication_feedback.pcap
2025-08-05 16:59:51,684 - INFO - 测试任务已启动，任务ID: fc15a44a-1b06-4f08-8860-d53e900b435b
2025-08-05 17:00:07,781 - INFO - 任务完成: fc15a44a-1b06-4f08-8860-d53e900b435b
2025-08-05 17:00:07,781 - INFO -   ❌ pgsql_replication_feedback.pcap: NO_JSON_EVENTS
2025-08-05 17:00:08,787 - INFO - 测试进度: 32/49 - pgsql_sasl_authentication.pcap
2025-08-05 17:00:08,787 - INFO - 启动测试: pgsql_sasl_authentication.pcap
2025-08-05 17:00:08,803 - INFO - 测试任务已启动，任务ID: 228ee03f-2320-4b11-a5cd-06aae75ec5fa
2025-08-05 17:00:19,975 - INFO - 任务完成: 228ee03f-2320-4b11-a5cd-06aae75ec5fa
2025-08-05 17:00:19,975 - INFO -   ✅ pgsql_sasl_authentication.pcap: SUCCESS
2025-08-05 17:00:20,980 - INFO - 测试进度: 33/49 - pgsql_simple_query.pcap
2025-08-05 17:00:20,981 - INFO - 启动测试: pgsql_simple_query.pcap
2025-08-05 17:00:20,999 - INFO - 测试任务已启动，任务ID: 83e925d9-3467-48af-b755-b11808df183d
2025-08-05 17:00:32,055 - INFO - 任务完成: 83e925d9-3467-48af-b755-b11808df183d
2025-08-05 17:00:32,056 - INFO -   ✅ pgsql_simple_query.pcap: SUCCESS
2025-08-05 17:00:33,058 - INFO - 测试进度: 34/49 - pgsql_ssl_request_not_supported.pcap
2025-08-05 17:00:33,059 - INFO - 启动测试: pgsql_ssl_request_not_supported.pcap
2025-08-05 17:00:33,072 - INFO - 测试任务已启动，任务ID: 32c75165-d504-4fe7-b080-58407866de4e
2025-08-05 17:00:44,138 - INFO - 任务完成: 32c75165-d504-4fe7-b080-58407866de4e
2025-08-05 17:00:44,140 - INFO -   ✅ pgsql_ssl_request_not_supported.pcap: SUCCESS
2025-08-05 17:00:45,145 - INFO - 测试进度: 35/49 - pgsql_start_replication.pcap
2025-08-05 17:00:45,146 - INFO - 启动测试: pgsql_start_replication.pcap
2025-08-05 17:00:45,161 - INFO - 测试任务已启动，任务ID: c7215e0d-afa7-4386-901d-53cd30bd59ed
2025-08-05 17:01:01,259 - INFO - 任务完成: c7215e0d-afa7-4386-901d-53cd30bd59ed
2025-08-05 17:01:01,259 - INFO -   ✅ pgsql_start_replication.pcap: SUCCESS
2025-08-05 17:01:02,261 - INFO - 测试进度: 36/49 - pgsql_sync_separated_transactions.pcap
2025-08-05 17:01:02,261 - INFO - 启动测试: pgsql_sync_separated_transactions.pcap
2025-08-05 17:01:02,278 - INFO - 测试任务已启动，任务ID: be696958-3814-49b0-b25c-f53fc934b43e
2025-08-05 17:01:13,629 - INFO - 任务完成: be696958-3814-49b0-b25c-f53fc934b43e
2025-08-05 17:01:13,629 - INFO -   ✅ pgsql_sync_separated_transactions.pcap: SUCCESS
2025-08-05 17:01:14,634 - INFO - 测试进度: 37/49 - pgsql_transaction.pcap
2025-08-05 17:01:14,635 - INFO - 启动测试: pgsql_transaction.pcap
2025-08-05 17:01:14,655 - INFO - 测试任务已启动，任务ID: c004c705-6729-4f63-959a-79aa248ba172
2025-08-05 17:01:25,919 - INFO - 任务完成: c004c705-6729-4f63-959a-79aa248ba172
2025-08-05 17:01:25,919 - INFO -   ✅ pgsql_transaction.pcap: SUCCESS
2025-08-05 17:01:26,924 - INFO - 测试进度: 38/49 - pgsql_wal_streaming.pcap
2025-08-05 17:01:26,925 - INFO - 启动测试: pgsql_wal_streaming.pcap
2025-08-05 17:01:26,941 - INFO - 测试任务已启动，任务ID: 01348b92-1dc9-489b-b29e-928397b8784d
2025-08-05 17:01:38,007 - INFO - 任务完成: 01348b92-1dc9-489b-b29e-928397b8784d
2025-08-05 17:01:38,007 - INFO -   ❌ pgsql_wal_streaming.pcap: NO_JSON_EVENTS
2025-08-05 17:01:39,011 - INFO - 目录 ../pgsql_pcaps 测试完成
2025-08-05 17:01:39,011 - INFO - 开始测试目录: ../pgsql_abnormal_pcaps
2025-08-05 17:01:39,011 - INFO - 测试进度: 39/49 - pgsql_auth_failure.pcap
2025-08-05 17:01:39,011 - INFO - 启动测试: pgsql_auth_failure.pcap
2025-08-05 17:01:39,026 - INFO - 测试任务已启动，任务ID: aa7982ef-aec1-40ec-a1c3-690229f372f1
2025-08-05 17:01:50,083 - INFO - 任务完成: aa7982ef-aec1-40ec-a1c3-690229f372f1
2025-08-05 17:01:50,084 - INFO -   ✅ pgsql_auth_failure.pcap: SUCCESS
2025-08-05 17:01:51,087 - INFO - 测试进度: 40/49 - pgsql_constraint_violation.pcap
2025-08-05 17:01:51,087 - INFO - 启动测试: pgsql_constraint_violation.pcap
2025-08-05 17:01:51,099 - INFO - 测试任务已启动，任务ID: a51df59d-8457-4360-b1af-12a5bc3ef130
2025-08-05 17:02:02,080 - INFO - 任务完成: a51df59d-8457-4360-b1af-12a5bc3ef130
2025-08-05 17:02:02,080 - INFO -   ✅ pgsql_constraint_violation.pcap: SUCCESS
2025-08-05 17:02:03,085 - INFO - 测试进度: 41/49 - pgsql_copy_fail.pcap
2025-08-05 17:02:03,085 - INFO - 启动测试: pgsql_copy_fail.pcap
2025-08-05 17:02:03,101 - INFO - 测试任务已启动，任务ID: 6db117f5-72a7-4579-ae9e-04ecfc5a6a91
2025-08-05 17:02:14,142 - INFO - 任务完成: 6db117f5-72a7-4579-ae9e-04ecfc5a6a91
2025-08-05 17:02:14,143 - INFO -   ✅ pgsql_copy_fail.pcap: SUCCESS
2025-08-05 17:02:15,148 - INFO - 测试进度: 42/49 - pgsql_error.pcap
2025-08-05 17:02:15,148 - INFO - 启动测试: pgsql_error.pcap
2025-08-05 17:02:15,164 - INFO - 测试任务已启动，任务ID: 4c8d7988-8477-4e62-b9fa-cdfa413e8364
2025-08-05 17:02:26,158 - INFO - 任务完成: 4c8d7988-8477-4e62-b9fa-cdfa413e8364
2025-08-05 17:02:26,158 - INFO -   ✅ pgsql_error.pcap: SUCCESS
2025-08-05 17:02:27,163 - INFO - 测试进度: 43/49 - pgsql_fatal_error.pcap
2025-08-05 17:02:27,163 - INFO - 启动测试: pgsql_fatal_error.pcap
2025-08-05 17:02:27,197 - INFO - 测试任务已启动，任务ID: 7a05ead8-1d26-439b-866b-d1a22cc680a2
2025-08-05 17:02:38,218 - INFO - 任务完成: 7a05ead8-1d26-439b-866b-d1a22cc680a2
2025-08-05 17:02:38,218 - INFO -   ✅ pgsql_fatal_error.pcap: SUCCESS
2025-08-05 17:02:39,223 - INFO - 测试进度: 44/49 - pgsql_function_call_error.pcap
2025-08-05 17:02:39,223 - INFO - 启动测试: pgsql_function_call_error.pcap
2025-08-05 17:02:39,241 - INFO - 测试任务已启动，任务ID: ba223acf-4aea-49d3-a4b3-e23b15f1a47b
2025-08-05 17:02:55,297 - INFO - 任务完成: ba223acf-4aea-49d3-a4b3-e23b15f1a47b
2025-08-05 17:02:55,297 - INFO -   ✅ pgsql_function_call_error.pcap: SUCCESS
2025-08-05 17:02:56,300 - INFO - 测试进度: 45/49 - pgsql_panic_error.pcap
2025-08-05 17:02:56,301 - INFO - 启动测试: pgsql_panic_error.pcap
2025-08-05 17:02:56,326 - INFO - 测试任务已启动，任务ID: a25ffc43-4173-478f-aaea-c466c4bb70b4
2025-08-05 17:03:07,337 - INFO - 任务完成: a25ffc43-4173-478f-aaea-c466c4bb70b4
2025-08-05 17:03:07,338 - INFO -   ✅ pgsql_panic_error.pcap: SUCCESS
2025-08-05 17:03:08,339 - INFO - 测试进度: 46/49 - pgsql_pipelined_error_handling.pcap
2025-08-05 17:03:08,339 - INFO - 启动测试: pgsql_pipelined_error_handling.pcap
2025-08-05 17:03:08,359 - INFO - 测试任务已启动，任务ID: 581d9222-bd2b-411b-b7f5-f40ef51685be
2025-08-05 17:03:19,469 - INFO - 任务完成: 581d9222-bd2b-411b-b7f5-f40ef51685be
2025-08-05 17:03:19,470 - INFO -   ✅ pgsql_pipelined_error_handling.pcap: SUCCESS
2025-08-05 17:03:20,471 - INFO - 测试进度: 47/49 - pgsql_syntax_error.pcap
2025-08-05 17:03:20,471 - INFO - 启动测试: pgsql_syntax_error.pcap
2025-08-05 17:03:20,489 - INFO - 测试任务已启动，任务ID: 48a24683-18a3-4510-b675-c09c70d94439
2025-08-05 17:03:31,652 - INFO - 任务完成: 48a24683-18a3-4510-b675-c09c70d94439
2025-08-05 17:03:31,652 - INFO -   ✅ pgsql_syntax_error.pcap: SUCCESS
2025-08-05 17:03:32,657 - INFO - 测试进度: 48/49 - pgsql_type_error.pcap
2025-08-05 17:03:32,657 - INFO - 启动测试: pgsql_type_error.pcap
2025-08-05 17:03:32,690 - INFO - 测试任务已启动，任务ID: e7f664ca-8e70-4f2f-ab08-63663b24cfde
2025-08-05 17:03:43,899 - INFO - 任务完成: e7f664ca-8e70-4f2f-ab08-63663b24cfde
2025-08-05 17:03:43,899 - INFO -   ✅ pgsql_type_error.pcap: SUCCESS
2025-08-05 17:03:44,903 - INFO - 测试进度: 49/49 - pgsql_user_not_exist.pcap
2025-08-05 17:03:44,904 - INFO - 启动测试: pgsql_user_not_exist.pcap
2025-08-05 17:03:44,917 - INFO - 测试任务已启动，任务ID: 329fba19-32b2-4818-89e6-578722deaecf
2025-08-05 17:04:01,103 - INFO - 任务完成: 329fba19-32b2-4818-89e6-578722deaecf
2025-08-05 17:04:01,103 - INFO -   ✅ pgsql_user_not_exist.pcap: SUCCESS
2025-08-05 17:04:02,108 - INFO - 目录 ../pgsql_abnormal_pcaps 测试完成
2025-08-05 17:04:02,109 - INFO - 批量测试完成
2025-08-05 17:04:02,109 - INFO - 生成测试报告: report/Unittest2_with_kafka_comparison.md
2025-08-05 17:04:02,110 - INFO - 测试报告已生成: report/Unittest2_with_kafka_comparison.md
2025-08-05 17:04:02,111 - INFO - 测试统计: 总数=49, 成功=47, 失败=2, 成功率=95.9%
2025-08-06 14:16:21,559 - INFO - 执行文件扫描...
2025-08-06 14:16:21,560 - INFO - 开始扫描pcap文件...
2025-08-06 14:16:21,561 - INFO - 在 ../pgsql_pcaps 中找到 38 个pcap文件
2025-08-06 14:16:21,561 - INFO - 在 ../pgsql_abnormal_pcaps 中找到 11 个pcap文件
2025-08-06 14:16:21,561 - INFO - 总共找到 49 个pcap文件
2025-08-06 14:16:49,879 - INFO - 执行文件扫描...
2025-08-06 14:16:49,880 - INFO - 开始扫描丢包测试pcap文件...
2025-08-06 14:16:49,880 - INFO - 在 ../pgsql_lost_pcaps 中找到 21 个丢包测试pcap文件
2025-08-06 14:17:02,310 - INFO - 测试单个文件: pgsql_auth_ok_lost.pcap
2025-08-06 14:17:02,312 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 208 (char 207), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-06 14:17:02,313 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 229 (char 228), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-06 14:17:02,313 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 246 (char 245), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-06 14:17:02,313 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 245 (char 244), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-06 14:17:02,313 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 227 (char 226), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-06 14:17:02,314 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 319 (char 318), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-06 14:17:02,314 - WARNING - 解析预期事件JSON失败: Extra data: line 1 column 223 (char 222), JSON内容: {"meta":{"tm":"timestamp","type":"PostgreSQL"},"net":{"src_ip":"************","dst_ip":"************...
2025-08-06 14:17:02,315 - INFO - 成功加载 71 个文件的预期事件
2025-08-06 14:17:02,316 - INFO - 启动测试: pgsql_auth_ok_lost.pcap
2025-08-06 14:17:02,333 - ERROR - 启动测试请求失败: 502 Server Error: Bad Gateway for url: http://**************:8000/api/v1/test/pcap-replay
2025-08-06 14:18:08,083 - INFO - 测试单个文件: pgsql_auth_ok_lost.pcap
2025-08-06 14:18:08,084 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"startup_message_lost"}, "net": {"src_ip":"...
2025-08-06 14:18:08,084 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"auth_request_lost"}, "net": {"src_ip":"192...
2025-08-06 14:18:08,084 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"password_response_lost"}, "net": {"src_ip"...
2025-08-06 14:18:08,084 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"auth_ok_lost"}, "net": {"src_ip":"192.168....
2025-08-06 14:18:08,084 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"backend_key_lost"}, "net": {"src_ip":"192....
2025-08-06 14:18:08,085 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"ready_for_query_lost"}, "net": {"src_ip":"...
2025-08-06 14:18:08,085 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"query_message_lost"}, "net": {"src_ip":"19...
2025-08-06 14:18:08,085 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"parse_message_lost"}, "net": {"src_ip":"19...
2025-08-06 14:18:08,085 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"bind_message_lost"}, "net": {"src_ip":"192...
2025-08-06 14:18:08,085 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"execute_message_lost"}, "net": {"src_ip":"...
2025-08-06 14:18:08,086 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"sync_message_lost"}, "net": {"src_ip":"192...
2025-08-06 14:18:08,086 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"row_description_lost"}, "net": {"src_ip":"...
2025-08-06 14:18:08,086 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"data_row_lost"}, "net": {"src_ip":"192.168...
2025-08-06 14:18:08,086 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"large_query_segment_lost"}, "net": {"src_i...
2025-08-06 14:18:08,086 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"ssl_request_lost"}, "net": {"src_ip":"192....
2025-08-06 14:18:08,086 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"copy_data_lost"}, "net": {"src_ip":"192.16...
2025-08-06 14:18:08,086 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"cancel_request_lost"}, "net": {"src_ip":"1...
2025-08-06 14:18:08,086 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"terminate_lost"}, "net": {"src_ip":"192.16...
2025-08-06 14:18:08,086 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"error_response_lost"}, "net": {"src_ip":"1...
2025-08-06 14:18:08,086 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"transaction_begin_lost"}, "net": {"src_ip"...
2025-08-06 14:18:08,086 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"notification_lost"}, "net": {"src_ip":"192...
2025-08-06 14:18:08,086 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","server_version":"14.9"}, "net": {"src_ip":"192.168...
2025-08-06 14:18:08,087 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","server_version":"14.9"}, "net": {"src_ip":"192.168...
2025-08-06 14:18:08,087 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","server_version":"14.9"}, "net": {"src_ip":"192.168...
2025-08-06 14:18:08,087 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,087 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,087 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,087 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,087 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,087 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,087 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,088 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,088 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,088 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,088 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,088 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,088 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,088 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,088 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,088 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,088 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,088 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,089 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,089 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,089 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,089 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,089 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,089 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,089 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,090 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,091 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,091 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,091 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,091 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,091 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,092 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,092 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,092 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,092 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,092 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,092 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,092 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,092 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,092 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,093 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,093 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,093 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,093 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,093 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,093 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,093 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:08,095 - INFO - 成功加载 71 个文件的预期事件
2025-08-06 14:18:08,096 - INFO - 启动测试: pgsql_auth_ok_lost.pcap
2025-08-06 14:18:08,127 - ERROR - 启动测试请求失败: 502 Server Error: Bad Gateway for url: http://**************:8000/api/v1/test/pcap-replay
2025-08-06 14:18:36,795 - INFO - 测试单个文件: pgsql_auth_ok_lost.pcap
2025-08-06 14:18:36,796 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"startup_message_lost"}, "net": {"src_ip":"...
2025-08-06 14:18:36,796 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"auth_request_lost"}, "net": {"src_ip":"192...
2025-08-06 14:18:36,797 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"password_response_lost"}, "net": {"src_ip"...
2025-08-06 14:18:36,797 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"auth_ok_lost"}, "net": {"src_ip":"192.168....
2025-08-06 14:18:36,797 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"backend_key_lost"}, "net": {"src_ip":"192....
2025-08-06 14:18:36,797 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"ready_for_query_lost"}, "net": {"src_ip":"...
2025-08-06 14:18:36,797 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"query_message_lost"}, "net": {"src_ip":"19...
2025-08-06 14:18:36,797 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"parse_message_lost"}, "net": {"src_ip":"19...
2025-08-06 14:18:36,797 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"bind_message_lost"}, "net": {"src_ip":"192...
2025-08-06 14:18:36,797 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"execute_message_lost"}, "net": {"src_ip":"...
2025-08-06 14:18:36,797 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"sync_message_lost"}, "net": {"src_ip":"192...
2025-08-06 14:18:36,797 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"row_description_lost"}, "net": {"src_ip":"...
2025-08-06 14:18:36,798 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"data_row_lost"}, "net": {"src_ip":"192.168...
2025-08-06 14:18:36,798 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"large_query_segment_lost"}, "net": {"src_i...
2025-08-06 14:18:36,798 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"ssl_request_lost"}, "net": {"src_ip":"192....
2025-08-06 14:18:36,798 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"copy_data_lost"}, "net": {"src_ip":"192.16...
2025-08-06 14:18:36,798 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"cancel_request_lost"}, "net": {"src_ip":"1...
2025-08-06 14:18:36,798 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"terminate_lost"}, "net": {"src_ip":"192.16...
2025-08-06 14:18:36,799 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"error_response_lost"}, "net": {"src_ip":"1...
2025-08-06 14:18:36,799 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"transaction_begin_lost"}, "net": {"src_ip"...
2025-08-06 14:18:36,799 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","error":"notification_lost"}, "net": {"src_ip":"192...
2025-08-06 14:18:36,799 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","server_version":"14.9"}, "net": {"src_ip":"192.168...
2025-08-06 14:18:36,800 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","server_version":"14.9"}, "net": {"src_ip":"192.168...
2025-08-06 14:18:36,800 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL","server_version":"14.9"}, "net": {"src_ip":"192.168...
2025-08-06 14:18:36,800 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,801 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,801 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,802 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,802 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,802 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,802 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,802 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,803 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,803 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,803 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,803 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,803 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,804 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,804 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,804 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,804 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,804 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,804 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,805 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,805 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,805 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,805 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,806 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,806 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,807 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,807 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,807 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,808 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,808 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,809 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,809 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,809 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,809 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,809 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,810 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,810 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,810 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,810 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,810 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,810 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,811 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,811 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,811 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,811 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,811 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,812 - WARNING - 解析预期事件JSON失败: Expecting ',' delimiter: line 1 column 18 (char 17), JSON内容: {"meta": {"tm":""timestamp"","type":"PostgreSQL"}, "net": {"src_ip":"************","dst_ip":"192.168...
2025-08-06 14:18:36,814 - INFO - 成功加载 71 个文件的预期事件
2025-08-06 14:18:36,814 - INFO - 启动测试: pgsql_auth_ok_lost.pcap
2025-08-06 14:18:36,829 - ERROR - 启动测试请求失败: 502 Server Error: Bad Gateway for url: http://**************:8000/api/v1/test/pcap-replay
2025-08-06 14:20:59,439 - INFO - 执行文件扫描...
2025-08-06 14:20:59,439 - INFO - 开始扫描丢包测试pcap文件...
2025-08-06 14:20:59,439 - INFO - 在 ../pgsql_lost_pcaps 中找到 21 个丢包测试pcap文件
