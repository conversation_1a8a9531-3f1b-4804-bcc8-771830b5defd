#!/usr/bin/env python3
"""
PostgreSQL协议测试自动化框架
用于批量测试pcap文件的协议解析能力并生成详细测试报告
"""

import os
import sys
import time
import json
import glob
import requests
import re
from datetime import datetime
from typing import List, Dict, Tuple, Optional
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pgsql_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PostgreSQLTestFramework:
    """PostgreSQL协议测试自动化框架"""
    
    def __init__(self, base_url: str = "http://**************:8000"):
        """
        初始化测试框架
        
        Args:
            base_url: gwhw_mcp服务的基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.test_results = []
        self.session = requests.Session()
        self.session.timeout = 30
        
        # 测试目录配置
        self.test_directories = [
            "../pgsql_pcaps",
            "../pgsql_abnormal_pcaps"
        ]

        # 丢包测试目录配置
        self.lost_test_directory = "../pgsql_lost_pcaps"

        # 预期事件文档路径
        self.expected_events_doc = "../AI_DOC/PostgreSQL_Protocol_Test_Cases.md"
        
    def scan_pcap_files(self) -> Dict[str, List[str]]:
        """
        扫描所有测试目录，获取pcap文件列表
        
        Returns:
            按目录分组的pcap文件字典
        """
        logger.info("开始扫描pcap文件...")
        pcap_files = {}
        
        for directory in self.test_directories:
            if not os.path.exists(directory):
                logger.warning(f"目录不存在: {directory}")
                continue
                
            files = glob.glob(os.path.join(directory, "*.pcap"))
            files.sort()
            
            if files:
                pcap_files[directory] = [os.path.basename(f) for f in files]
                logger.info(f"在 {directory} 中找到 {len(files)} 个pcap文件")
            else:
                logger.warning(f"在 {directory} 中未找到pcap文件")
        
        total_files = sum(len(files) for files in pcap_files.values())
        logger.info(f"总共找到 {total_files} 个pcap文件")
        
        return pcap_files

    def load_expected_events(self) -> Dict[str, List[Dict]]:
        """
        从文档中加载预期的Kafka事件

        Returns:
            按文件名分组的预期事件字典
        """
        expected_events = {}

        try:
            if not os.path.exists(self.expected_events_doc):
                logger.warning(f"预期事件文档不存在: {self.expected_events_doc}")
                return expected_events

            with open(self.expected_events_doc, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析文档中的预期事件
            # 查找生成文件和对应的预期Kafka事件
            file_pattern = r'- \*\*生成文件\*\*: `([^`]+)`'
            event_pattern = r'- \*\*预期Kafka事件\*\*:\s*```json\s*(\{.*?\})\s*```'

            file_matches = re.finditer(file_pattern, content)
            event_matches = re.finditer(event_pattern, content, re.DOTALL)

            file_list = [match.group(1) for match in file_matches]
            event_list = []

            for match in event_matches:
                try:
                    event_json = match.group(1).strip()
                    # 清理JSON字符串中的换行和多余空格
                    event_json = re.sub(r'\s+', ' ', event_json)

                    # 处理JSON中的占位符
                    event_json = event_json.replace('timestamp', '"timestamp"')

                    # 尝试解析JSON
                    event_obj = json.loads(event_json)
                    event_list.append(event_obj)
                except json.JSONDecodeError as e:
                    logger.warning(f"解析预期事件JSON失败: {e}, JSON内容: {event_json[:100]}...")
                    # 即使解析失败，也保存原始字符串用于后续分析
                    event_list.append({"_raw_json": event_json, "_parse_error": str(e)})
                except Exception as e:
                    logger.warning(f"处理预期事件时发生异常: {e}")
                    event_list.append(None)

            # 将文件名和事件配对
            for i, filename in enumerate(file_list):
                if i < len(event_list) and event_list[i] is not None:
                    # 检查是否是解析失败的事件
                    if isinstance(event_list[i], dict) and "_parse_error" in event_list[i]:
                        # 对于解析失败的事件，创建一个简化的预期结构用于对比
                        raw_json = event_list[i].get("_raw_json", "")
                        simplified_event = self._extract_key_fields_from_raw_json(raw_json)
                        expected_events[filename] = [simplified_event]
                    else:
                        expected_events[filename] = [event_list[i]]
                else:
                    expected_events[filename] = []

            logger.info(f"成功加载 {len(expected_events)} 个文件的预期事件")

        except Exception as e:
            logger.error(f"加载预期事件失败: {e}")

        return expected_events

    def _extract_key_fields_from_raw_json(self, raw_json: str) -> Dict:
        """
        从原始JSON字符串中提取关键字段用于对比

        Args:
            raw_json: 原始JSON字符串

        Returns:
            包含关键字段的字典
        """
        simplified = {
            "req": {},
            "rsp": {},
            "net": {},
            "_is_simplified": True
        }

        try:
            # 提取cmd_type
            cmd_type_match = re.search(r'"cmd_type":\s*"([^"]+)"', raw_json)
            if cmd_type_match:
                simplified["req"]["cmd_type"] = cmd_type_match.group(1)

            # 提取status
            status_match = re.search(r'"status":\s*(\d+)', raw_json)
            if status_match:
                simplified["rsp"]["status"] = int(status_match.group(1))

            # 提取row_count
            row_count_match = re.search(r'"row_count":\s*(\d+)', raw_json)
            if row_count_match:
                simplified["rsp"]["row_count"] = int(row_count_match.group(1))

            # 提取sql
            sql_match = re.search(r'"sql":\s*"([^"]+)"', raw_json)
            if sql_match:
                simplified["req"]["sql"] = sql_match.group(1)

            # 提取网络信息
            src_ip_match = re.search(r'"src_ip":\s*"([^"]+)"', raw_json)
            if src_ip_match:
                simplified["net"]["src_ip"] = src_ip_match.group(1)

            dst_ip_match = re.search(r'"dst_ip":\s*"([^"]+)"', raw_json)
            if dst_ip_match:
                simplified["net"]["dst_ip"] = dst_ip_match.group(1)

            dst_port_match = re.search(r'"dst_port":\s*(\d+)', raw_json)
            if dst_port_match:
                simplified["net"]["dst_port"] = int(dst_port_match.group(1))

        except Exception as e:
            logger.warning(f"提取关键字段时发生异常: {e}")

        return simplified

    def compare_kafka_events(self, pcap_file_name: str, actual_events: List[Dict],
                           expected_events: Dict[str, List[Dict]]) -> Dict:
        """
        对比实际Kafka事件与预期事件

        Args:
            pcap_file_name: pcap文件名
            actual_events: 实际的JSON事件列表
            expected_events: 预期事件字典

        Returns:
            对比结果字典
        """
        comparison_result = {
            "file_name": pcap_file_name,
            "has_expected": pcap_file_name in expected_events,
            "actual_event_count": len(actual_events) if actual_events else 0,
            "expected_event_count": 0,
            "events_match": False,
            "field_comparisons": [],
            "summary": "",
            "details": []
        }

        try:
            if pcap_file_name not in expected_events:
                comparison_result["summary"] = "无预期事件定义"
                return comparison_result

            expected_list = expected_events[pcap_file_name]
            comparison_result["expected_event_count"] = len(expected_list)

            if not actual_events:
                comparison_result["summary"] = "实际事件为空"
                return comparison_result

            if not expected_list:
                comparison_result["summary"] = "预期事件为空"
                return comparison_result

            # 对比事件数量
            if len(actual_events) != len(expected_list):
                comparison_result["summary"] = f"事件数量不匹配: 实际{len(actual_events)}, 预期{len(expected_list)}"

            # 逐个对比事件
            for i, (actual, expected) in enumerate(zip(actual_events, expected_list)):
                event_comparison = self._compare_single_event(actual, expected, i)
                comparison_result["field_comparisons"].append(event_comparison)

            # 计算总体匹配度
            total_fields = sum(comp["total_fields"] for comp in comparison_result["field_comparisons"])
            matched_fields = sum(comp["matched_fields"] for comp in comparison_result["field_comparisons"])

            if total_fields > 0:
                match_rate = matched_fields / total_fields
                comparison_result["events_match"] = match_rate >= 0.8  # 80%匹配度认为成功
                comparison_result["match_rate"] = match_rate

                if comparison_result["events_match"]:
                    comparison_result["summary"] = f"事件匹配成功 ({match_rate:.1%})"
                else:
                    comparison_result["summary"] = f"事件匹配度较低 ({match_rate:.1%})"
            else:
                comparison_result["summary"] = "无法进行字段对比"

        except Exception as e:
            comparison_result["summary"] = f"对比过程异常: {str(e)}"
            logger.error(f"对比Kafka事件时发生异常: {e}")

        return comparison_result

    def _compare_single_event(self, actual: Dict, expected: Dict, event_index: int) -> Dict:
        """
        对比单个事件的详细字段

        Args:
            actual: 实际事件
            expected: 预期事件
            event_index: 事件索引

        Returns:
            单个事件对比结果
        """
        event_result = {
            "event_index": event_index,
            "matched_fields": 0,
            "total_fields": 0,
            "field_details": {},
            "critical_fields_match": True
        }

        # 关键字段列表
        critical_fields = [
            ("req", "cmd_type"),
            ("rsp", "status"),
            ("req", "sql"),
            ("rsp", "row_count")
        ]

        # 对比关键字段
        for field_path in critical_fields:
            actual_value = self._get_nested_value(actual, field_path)
            expected_value = self._get_nested_value(expected, field_path)

            field_name = ".".join(field_path)
            event_result["total_fields"] += 1

            if actual_value == expected_value:
                event_result["matched_fields"] += 1
                event_result["field_details"][field_name] = {
                    "status": "MATCH",
                    "actual": actual_value,
                    "expected": expected_value
                }
            else:
                event_result["field_details"][field_name] = {
                    "status": "MISMATCH",
                    "actual": actual_value,
                    "expected": expected_value
                }
                # cmd_type和status是最关键的字段
                if field_path in [("req", "cmd_type"), ("rsp", "status")]:
                    event_result["critical_fields_match"] = False

        # 对比网络信息
        net_fields = [("net", "src_ip"), ("net", "dst_ip"), ("net", "dst_port")]
        for field_path in net_fields:
            actual_value = self._get_nested_value(actual, field_path)
            expected_value = self._get_nested_value(expected, field_path)

            field_name = ".".join(field_path)
            event_result["total_fields"] += 1

            if actual_value == expected_value:
                event_result["matched_fields"] += 1
                event_result["field_details"][field_name] = {
                    "status": "MATCH",
                    "actual": actual_value,
                    "expected": expected_value
                }
            else:
                event_result["field_details"][field_name] = {
                    "status": "MISMATCH",
                    "actual": actual_value,
                    "expected": expected_value
                }

        return event_result

    def _get_nested_value(self, data: Dict, field_path: Tuple[str, ...]) -> any:
        """
        获取嵌套字典中的值

        Args:
            data: 数据字典
            field_path: 字段路径元组

        Returns:
            字段值，如果不存在返回None
        """
        try:
            current = data
            for key in field_path:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return None
            return current
        except Exception:
            return None
    
    def start_pcap_replay_test(self, pcap_file_name: str, protocol_name: str = "postgre", 
                              eth_name: str = "lo") -> Optional[str]:
        """
        启动pcap回放测试
        
        Args:
            pcap_file_name: pcap文件名（不包含路径）
            protocol_name: 协议名称，默认为"postgre"
            eth_name: 网络接口名称，默认为"lo"
            
        Returns:
            任务ID，如果失败返回None
        """
        url = f"{self.base_url}/api/v1/test/pcap-replay"
        
        payload = {
            "pcap_file_name": pcap_file_name,
            "protocol_name": protocol_name,
            "eth_name": eth_name
        }
        
        try:
            logger.info(f"启动测试: {pcap_file_name}")
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            task_id = result.get("task_id")
            
            if task_id:
                logger.info(f"测试任务已启动，任务ID: {task_id}")
                return task_id
            else:
                logger.error(f"启动测试失败，未获取到任务ID: {result}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"启动测试请求失败: {e}")
            return None
        except Exception as e:
            logger.error(f"启动测试时发生未知错误: {e}")
            return None
    
    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """
        查询任务状态和结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态信息，如果失败返回None
        """
        url = f"{self.base_url}/api/v1/tasks/{task_id}"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"查询任务状态失败: {e}")
            return None
        except Exception as e:
            logger.error(f"查询任务状态时发生未知错误: {e}")
            return None
    
    def wait_for_task_completion(self, task_id: str, max_wait_time: int = 300, 
                                check_interval: int = 5) -> Optional[Dict]:
        """
        等待任务完成
        
        Args:
            task_id: 任务ID
            max_wait_time: 最大等待时间（秒）
            check_interval: 检查间隔（秒）
            
        Returns:
            最终任务状态，如果超时或失败返回None
        """
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            task_status = self.get_task_status(task_id)
            
            if not task_status:
                logger.error(f"无法获取任务状态: {task_id}")
                return None
            
            status = task_status.get("status", "").lower()
            
            if status in ["completed", "success", "finished"]:
                logger.info(f"任务完成: {task_id}")
                return task_status
            elif status in ["failed", "error"]:
                logger.error(f"任务失败: {task_id}, 状态: {status}")
                return task_status
            elif status in ["running", "in_progress", "processing"]:
                logger.debug(f"任务进行中: {task_id}, 状态: {status}")
            else:
                logger.debug(f"任务状态: {task_id}, 状态: {status}")
            
            time.sleep(check_interval)
        
        logger.error(f"任务等待超时: {task_id}")
        return None
    
    def test_single_pcap(self, pcap_file_name: str, expected_events: Dict[str, List[Dict]] = None) -> Dict:
        """
        测试单个pcap文件

        Args:
            pcap_file_name: pcap文件名
            expected_events: 预期事件字典（可选）

        Returns:
            测试结果字典
        """
        test_result = {
            "file_name": pcap_file_name,
            "status": "UNKNOWN",
            "task_id": None,
            "json_events": None,
            "raw_json_events": None,
            "kafka_event_comparison": None,
            "error_message": None,
            "test_time": datetime.now().isoformat(),
            "duration": 0
        }
        
        start_time = time.time()
        
        try:
            # 启动测试
            task_id = self.start_pcap_replay_test(pcap_file_name)
            if not task_id:
                test_result["status"] = "FAILED"
                test_result["error_message"] = "无法启动测试任务"
                return test_result
            
            test_result["task_id"] = task_id
            
            # 等待任务完成
            final_status = self.wait_for_task_completion(task_id)
            if not final_status:
                test_result["status"] = "TIMEOUT"
                test_result["error_message"] = "任务等待超时"
                return test_result
            
            # 分析结果
            status = final_status.get("status", "").lower()
            message = final_status.get("message", "")
            result = final_status.get("result", {})

            if status in ["completed", "success", "finished"]:
                # 检查是否有json_events
                if result and "json_events" in result:
                    json_events = result.get("json_events", [])
                    if json_events and len(json_events) > 0:
                        test_result["status"] = "SUCCESS"
                        test_result["json_events"] = self.extract_json_events_from_result(result)
                        test_result["raw_json_events"] = json_events
                        test_result["hw_log"] = result.get("hw_log", "")
                        test_result["hw_err"] = result.get("hw_err", "")

                        # 进行Kafka事件对比（如果提供了预期事件）
                        if expected_events:
                            try:
                                comparison_result = self.compare_kafka_events(
                                    pcap_file_name, json_events, expected_events
                                )
                                test_result["kafka_event_comparison"] = comparison_result
                                logger.debug(f"Kafka事件对比完成: {pcap_file_name} - {comparison_result['summary']}")
                            except Exception as e:
                                logger.warning(f"Kafka事件对比失败 {pcap_file_name}: {e}")
                                test_result["kafka_event_comparison"] = {
                                    "summary": f"对比异常: {str(e)}",
                                    "events_match": False
                                }
                    else:
                        test_result["status"] = "NO_JSON_EVENTS"
                        test_result["error_message"] = "任务完成但json_events为空"
                else:
                    test_result["status"] = "NO_JSON_EVENTS"
                    test_result["error_message"] = "任务完成但未找到json_events字段"
            else:
                test_result["status"] = "FAILED"
                test_result["error_message"] = f"任务状态: {status}, 消息: {message}"
            
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["error_message"] = f"测试过程中发生异常: {str(e)}"
            logger.error(f"测试 {pcap_file_name} 时发生异常: {e}")
        
        finally:
            test_result["duration"] = time.time() - start_time
        
        return test_result
    
    def extract_json_events(self, message: str) -> Optional[str]:
        """
        从任务消息中提取json事件内容
        
        Args:
            message: 任务消息
            
        Returns:
            json事件内容摘要
        """
        try:
            # 尝试解析消息中的JSON内容
            if isinstance(message, str):
                # 查找JSON格式的内容
                import re
                json_pattern = r'\{.*\}'
                matches = re.findall(json_pattern, message, re.DOTALL)
                
                if matches:
                    # 返回第一个JSON匹配的摘要
                    json_str = matches[0]
                    if len(json_str) > 200:
                        return json_str[:200] + "..."
                    return json_str
                else:
                    # 如果没有找到JSON，返回消息摘要
                    if len(message) > 100:
                        return message[:100] + "..."
                    return message
            
            return str(message)
            
        except Exception as e:
            logger.warning(f"提取JSON事件时发生错误: {e}")
            return str(message)[:100] if message else None

    def extract_json_events_from_result(self, result: Dict) -> str:
        """
        从任务结果中提取json事件内容摘要

        Args:
            result: 任务结果字典

        Returns:
            json事件内容摘要
        """
        try:
            json_events = result.get("json_events", [])
            if not json_events:
                return "无JSON事件"

            # 统计信息
            event_count = len(json_events)
            summary = f"包含{event_count}个JSON事件"

            # 提取第一个事件的关键信息
            if event_count > 0:
                first_event = json_events[0]
                req = first_event.get("req", {})
                rsp = first_event.get("rsp", {})

                cmd_type = req.get("cmd_type", "unknown")
                sql = req.get("sql", "")
                status = rsp.get("status", "unknown")
                row_count = rsp.get("row_count", 0)

                summary += f" - 命令类型: {cmd_type}"
                if sql:
                    sql_preview = sql[:50] + "..." if len(sql) > 50 else sql
                    summary += f", SQL: {sql_preview}"
                summary += f", 状态: {status}, 行数: {row_count}"

            return summary

        except Exception as e:
            logger.warning(f"提取JSON事件摘要时发生错误: {e}")
            return f"JSON事件解析错误: {str(e)}"

    def run_batch_tests(self) -> Dict[str, List[Dict]]:
        """
        运行批量测试

        Returns:
            按目录分组的测试结果
        """
        logger.info("开始批量测试...")

        # 扫描pcap文件
        pcap_files = self.scan_pcap_files()

        if not pcap_files:
            logger.error("未找到任何pcap文件")
            return {}

        # 加载预期事件
        logger.info("加载预期Kafka事件...")
        expected_events = self.load_expected_events()

        batch_results = {}
        total_files = sum(len(files) for files in pcap_files.values())
        current_file = 0

        for directory, files in pcap_files.items():
            logger.info(f"开始测试目录: {directory}")
            directory_results = []

            for pcap_file in files:
                current_file += 1
                logger.info(f"测试进度: {current_file}/{total_files} - {pcap_file}")

                # 测试单个文件
                result = self.test_single_pcap(pcap_file, expected_events)
                directory_results.append(result)

                # 记录结果
                status_symbol = "✅" if result["status"] == "SUCCESS" else "❌"
                logger.info(f"  {status_symbol} {pcap_file}: {result['status']}")

                # 添加短暂延迟，避免过于频繁的请求
                time.sleep(1)

            batch_results[directory] = directory_results
            logger.info(f"目录 {directory} 测试完成")

        self.test_results = batch_results
        logger.info("批量测试完成")

        return batch_results

    def run_specific_tests(self, file_list: List[str]) -> Dict[str, List[Dict]]:
        """
        运行指定文件列表的测试

        Args:
            file_list: 要测试的pcap文件名列表

        Returns:
            测试结果字典
        """
        logger.info(f"开始测试指定的{len(file_list)}个文件...")

        # 加载预期事件
        expected_events = self.load_expected_events()

        batch_results = {"specific_tests": []}

        for i, pcap_file in enumerate(file_list, 1):
            logger.info(f"测试进度: {i}/{len(file_list)} - {pcap_file}")

            # 测试单个文件
            result = self.test_single_pcap(pcap_file, expected_events)
            batch_results["specific_tests"].append(result)

            # 记录结果
            status_symbol = "✅" if result["status"] == "SUCCESS" else "❌"
            logger.info(f"  {status_symbol} {pcap_file}: {result['status']}")

            # 添加短暂延迟，避免过于频繁的请求
            time.sleep(1)

        self.test_results = batch_results
        logger.info("指定文件测试完成")

        return batch_results

    def scan_lost_pcap_files(self) -> List[str]:
        """
        扫描丢包测试目录，获取pcap文件列表

        Returns:
            丢包测试pcap文件列表
        """
        logger.info("开始扫描丢包测试pcap文件...")

        if not os.path.exists(self.lost_test_directory):
            logger.warning(f"丢包测试目录不存在: {self.lost_test_directory}")
            return []

        files = glob.glob(os.path.join(self.lost_test_directory, "*.pcap"))
        files.sort()

        pcap_files = [os.path.basename(f) for f in files]

        if pcap_files:
            logger.info(f"在 {self.lost_test_directory} 中找到 {len(pcap_files)} 个丢包测试pcap文件")
        else:
            logger.warning(f"在 {self.lost_test_directory} 中未找到pcap文件")

        return pcap_files

    def run_lost_packet_tests(self) -> Dict[str, List[Dict]]:
        """
        运行丢包场景测试

        Returns:
            丢包测试结果字典
        """
        logger.info("开始丢包场景测试...")

        # 扫描丢包测试pcap文件
        pcap_files = self.scan_lost_pcap_files()

        if not pcap_files:
            logger.error("未找到任何丢包测试pcap文件")
            return {}

        # 加载预期事件（包含丢包场景的预期事件）
        logger.info("加载丢包场景预期Kafka事件...")
        expected_events = self.load_expected_events()

        lost_test_results = {"lost_packet_tests": []}

        for i, pcap_file in enumerate(pcap_files, 1):
            logger.info(f"丢包测试进度: {i}/{len(pcap_files)} - {pcap_file}")

            # 测试单个文件
            result = self.test_single_pcap(pcap_file, expected_events)
            lost_test_results["lost_packet_tests"].append(result)

            # 记录结果
            status_symbol = "✅" if result["status"] == "SUCCESS" else "❌"
            logger.info(f"  {status_symbol} {pcap_file}: {result['status']}")

            # 添加短暂延迟，避免过于频繁的请求
            time.sleep(1)

        logger.info("丢包场景测试完成")
        return lost_test_results

    def generate_test_report(self, output_file: str = "Unittest.md") -> None:
        """
        生成测试报告

        Args:
            output_file: 输出文件名
        """
        logger.info(f"生成测试报告: {output_file}")

        if not self.test_results:
            logger.error("没有测试结果可生成报告")
            return

        # 计算统计信息
        total_files = 0
        success_files = 0
        failed_files = 0

        for directory_results in self.test_results.values():
            for result in directory_results:
                total_files += 1
                if result["status"] == "SUCCESS":
                    success_files += 1
                else:
                    failed_files += 1

        success_rate = (success_files / total_files * 100) if total_files > 0 else 0

        # 生成报告内容
        report_content = self._generate_report_content(total_files, success_files, failed_files, success_rate)

        # 写入文件
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)

            logger.info(f"测试报告已生成: {output_file}")
            logger.info(f"测试统计: 总数={total_files}, 成功={success_files}, 失败={failed_files}, 成功率={success_rate:.1f}%")

        except Exception as e:
            logger.error(f"生成测试报告失败: {e}")

    def generate_lost_packet_test_report(self, lost_test_results: Dict[str, List[Dict]],
                                       output_file: str = "report/Unittest_lost.md") -> None:
        """
        生成丢包测试专用报告

        Args:
            lost_test_results: 丢包测试结果字典
            output_file: 输出文件名
        """
        logger.info(f"生成丢包测试报告: {output_file}")

        if not lost_test_results or "lost_packet_tests" not in lost_test_results:
            logger.error("没有丢包测试结果可生成报告")
            return

        results = lost_test_results["lost_packet_tests"]

        # 计算统计信息
        total_files = len(results)
        success_files = sum(1 for r in results if r["status"] == "SUCCESS")
        failed_files = total_files - success_files
        success_rate = (success_files / total_files * 100) if total_files > 0 else 0

        # 生成报告内容
        report_content = self._generate_lost_packet_report_content(
            results, total_files, success_files, failed_files, success_rate
        )

        # 确保报告目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # 写入文件
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)

            logger.info(f"丢包测试报告已生成: {output_file}")
            logger.info(f"丢包测试统计: 总数={total_files}, 成功={success_files}, 失败={failed_files}, 成功率={success_rate:.1f}%")

        except Exception as e:
            logger.error(f"生成丢包测试报告失败: {e}")

    def _generate_lost_packet_report_content(self, results: List[Dict], total_files: int,
                                           success_files: int, failed_files: int,
                                           success_rate: float) -> str:
        """
        生成丢包测试报告内容

        Args:
            results: 测试结果列表
            total_files: 总文件数
            success_files: 成功文件数
            failed_files: 失败文件数
            success_rate: 成功率

        Returns:
            报告内容字符串
        """
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        content = f"""# PostgreSQL协议丢包测试自动化报告

## 测试概览

- **测试时间**: {current_time}
- **测试目标**: 验证PostgreSQL协议在TCP网络丢包异常情况下的解析能力和异常检测
- **测试范围**: pgsql_lost_pcaps 目录下的所有丢包场景pcap文件
- **测试特点**: 模拟真实网络环境中的数据包丢失情况，验证协议解析器的鲁棒性

## 丢包测试统计

| 指标 | 数值 |
|------|------|
| 总测试文件数 | {total_files} |
| 成功解析文件数 | {success_files} |
| 失败文件数 | {failed_files} |
| 成功率 | {success_rate:.1f}% |

## 丢包场景分类分析

"""

        # 按场景类型分组分析
        auth_scenarios = []
        query_scenarios = []
        response_scenarios = []
        advanced_scenarios = []
        connection_scenarios = []

        for result in results:
            file_name = result["file_name"]
            if any(x in file_name for x in ["startup", "auth", "password", "backend_key", "ready_for_query"]):
                auth_scenarios.append(result)
            elif any(x in file_name for x in ["query", "parse", "bind", "execute", "sync"]):
                query_scenarios.append(result)
            elif any(x in file_name for x in ["row_description", "data_row"]):
                response_scenarios.append(result)
            elif any(x in file_name for x in ["large_query", "ssl_request", "copy_data"]):
                advanced_scenarios.append(result)
            elif any(x in file_name for x in ["cancel", "terminate", "error", "transaction", "notification"]):
                connection_scenarios.append(result)

        # 生成各类场景的分析
        scenario_groups = [
            ("认证阶段丢包场景", auth_scenarios),
            ("查询执行阶段丢包场景", query_scenarios),
            ("响应数据阶段丢包场景", response_scenarios),
            ("大数据传输和高级功能丢包场景", advanced_scenarios),
            ("连接管理和错误处理丢包场景", connection_scenarios)
        ]

        for group_name, group_results in scenario_groups:
            if group_results:
                group_success = sum(1 for r in group_results if r["status"] == "SUCCESS")
                group_total = len(group_results)
                group_rate = (group_success / group_total * 100) if group_total > 0 else 0

                content += f"### {group_name}\n\n"
                content += f"**场景统计**: {group_success}/{group_total} 成功 ({group_rate:.1f}%)\n\n"

                # 详细结果表格
                content += "| 文件名 | 状态 | 丢包类型 | JSON事件 | Kafka事件对比 | 错误信息 | 耗时(秒) |\n"
                content += "|--------|------|----------|----------|--------------|----------|----------|\n"

                for result in group_results:
                    status_symbol = "✅" if result["status"] == "SUCCESS" else "❌"
                    file_name = result["file_name"]
                    status = result["status"]
                    json_events = result.get("json_events", "无") or "无"
                    error_msg = result.get("error_message", "无") or "无"
                    duration = f"{result.get('duration', 0):.1f}"

                    # 提取丢包类型
                    loss_type = self._extract_loss_type_from_filename(file_name)

                    # Kafka事件对比结果
                    kafka_comparison = result.get("kafka_event_comparison")
                    if kafka_comparison:
                        if kafka_comparison.get("events_match", False):
                            kafka_status = f"✅ {kafka_comparison.get('summary', '匹配')}"
                        else:
                            kafka_status = f"⚠️ {kafka_comparison.get('summary', '不匹配')}"
                    else:
                        kafka_status = "无对比"

                    # 截断过长的内容
                    if len(json_events) > 30:
                        json_events = json_events[:30] + "..."
                    if len(error_msg) > 30:
                        error_msg = error_msg[:30] + "..."
                    if len(kafka_status) > 30:
                        kafka_status = kafka_status[:30] + "..."

                    content += f"| {file_name} | {status_symbol} {status} | {loss_type} | {json_events} | {kafka_status} | {error_msg} | {duration} |\n"

                content += "\n"

        # 完成报告内容
        content = self._complete_lost_packet_report_content(
            content, results, failed_files, success_rate
        )

        return content

    def _extract_loss_type_from_filename(self, filename: str) -> str:
        """
        从文件名中提取丢包类型

        Args:
            filename: pcap文件名

        Returns:
            丢包类型描述
        """
        loss_type_map = {
            "startup_message": "启动消息丢失",
            "auth_request": "认证请求丢失",
            "password_response": "密码响应丢失",
            "auth_ok": "认证确认丢失",
            "backend_key": "后端密钥丢失",
            "ready_for_query": "就绪状态丢失",
            "query_message": "查询消息丢失",
            "parse_message": "解析消息丢失",
            "bind_message": "绑定消息丢失",
            "execute_message": "执行消息丢失",
            "sync_message": "同步消息丢失",
            "row_description": "行描述丢失",
            "data_row": "数据行丢失",
            "large_query_segment": "大查询分段丢失",
            "ssl_request": "SSL请求丢失",
            "copy_data": "COPY数据丢失",
            "cancel_request": "取消请求丢失",
            "terminate": "终止消息丢失",
            "error_response": "错误响应丢失",
            "transaction_begin": "事务开始响应丢失",
            "notification": "通知消息丢失"
        }

        for key, description in loss_type_map.items():
            if key in filename:
                return description

        return "未知丢包类型"

    def _complete_lost_packet_report_content(self, content: str, results: List[Dict],
                                           failed_files: int, success_rate: float) -> str:
        """
        完成丢包测试报告的剩余部分

        Args:
            content: 已有的报告内容
            results: 测试结果列表
            total_files: 总文件数
            success_files: 成功文件数
            failed_files: 失败文件数
            success_rate: 成功率

        Returns:
            完整的报告内容
        """
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 添加Kafka事件对比分析
        content += "## 丢包场景Kafka事件对比分析\n\n"

        # 统计对比结果
        total_with_expected = 0
        successful_matches = 0
        comparison_details = []

        for result in results:
            kafka_comparison = result.get("kafka_event_comparison")
            if kafka_comparison and kafka_comparison.get("has_expected", False):
                total_with_expected += 1
                if kafka_comparison.get("events_match", False):
                    successful_matches += 1
                comparison_details.append({
                    "file_name": result["file_name"],
                    "comparison": kafka_comparison
                })

        if total_with_expected > 0:
            match_rate = successful_matches / total_with_expected * 100
            content += f"**对比统计**: {successful_matches}/{total_with_expected} 个丢包场景的Kafka事件与预期匹配 ({match_rate:.1f}%)\n\n"

            # 详细对比结果
            content += "### 详细对比结果\n\n"
            content += "| 文件名 | 事件数量对比 | 匹配度 | 关键字段状态 | 详细说明 |\n"
            content += "|--------|------------|--------|------------|----------|\n"

            for detail in comparison_details:
                file_name = detail["file_name"]
                comp = detail["comparison"]

                actual_count = comp.get("actual_event_count", 0)
                expected_count = comp.get("expected_event_count", 0)
                event_count_str = f"{actual_count}/{expected_count}"

                match_rate = comp.get("match_rate", 0) * 100 if comp.get("match_rate") else 0
                match_rate_str = f"{match_rate:.1f}%"

                events_match = comp.get("events_match", False)
                match_symbol = "✅" if events_match else "❌"

                summary = comp.get("summary", "无说明")
                if len(summary) > 25:
                    summary = summary[:25] + "..."

                # 检查关键字段匹配情况
                field_comparisons = comp.get("field_comparisons", [])
                critical_match = "✅" if all(fc.get("critical_fields_match", True) for fc in field_comparisons) else "❌"

                content += f"| {file_name} | {event_count_str} | {match_symbol} {match_rate_str} | {critical_match} | {summary} |\n"

            content += "\n"
        else:
            content += "**说明**: 当前丢包测试中没有找到预期事件定义，无法进行对比分析。\n\n"

        # 添加失败场景分析
        failed_results = [r for r in results if r["status"] != "SUCCESS"]

        if failed_results:
            content += "## 失败场景分析\n\n"

            # 按失败类型分组
            failure_types = {}
            for result in failed_results:
                failure_type = result["status"]
                if failure_type not in failure_types:
                    failure_types[failure_type] = []
                failure_types[failure_type].append(result)

            for failure_type, failures in failure_types.items():
                content += f"### {failure_type} ({len(failures)}个场景)\n\n"

                for result in failures:
                    loss_type = self._extract_loss_type_from_filename(result['file_name'])
                    content += f"- **{result['file_name']}** ({loss_type}): {result.get('error_message', '未知错误')}\n"

                content += "\n"

        # 添加丢包测试总结和建议
        content += "## 丢包测试总结\n\n"

        if success_rate >= 90:
            content += "🎉 **丢包测试结果优秀**: 大部分丢包场景都能被正确识别和处理。\n\n"
        elif success_rate >= 70:
            content += "✅ **丢包测试结果良好**: 多数丢包场景能被识别，但仍有改进空间。\n\n"
        else:
            content += "⚠️ **丢包测试结果需要改进**: 较多丢包场景未能正确处理，需要优化异常检测能力。\n\n"

        content += "### 丢包测试特点分析\n\n"
        content += "1. **认证阶段丢包**: 影响连接建立，应能检测到认证流程中断\n"
        content += "2. **查询阶段丢包**: 影响SQL执行，应能检测到请求不完整\n"
        content += "3. **响应阶段丢包**: 影响结果返回，应能检测到数据缺失\n"
        content += "4. **连接管理丢包**: 影响连接状态，应能检测到管理消息丢失\n\n"

        content += "### 改进建议\n\n"

        if failed_files > 0:
            content += "1. **异常检测增强**: 改进协议解析器的丢包检测能力\n"
            content += "2. **超时机制优化**: 完善网络超时和重传检测逻辑\n"
            content += "3. **状态管理改进**: 增强协议状态机的异常状态处理\n"

        content += "4. **监控告警**: 建立丢包场景的实时监控和告警机制\n"
        content += "5. **网络诊断**: 集成网络质量诊断和丢包率统计功能\n"
        content += "6. **恢复策略**: 实现丢包场景下的自动恢复和重试机制\n\n"

        content += f"---\n*丢包测试报告生成时间: {current_time}*\n"

        return content

    def _generate_report_content(self, total_files: int, success_files: int,
                               failed_files: int, success_rate: float) -> str:
        """
        生成报告内容

        Args:
            total_files: 总文件数
            success_files: 成功文件数
            failed_files: 失败文件数
            success_rate: 成功率

        Returns:
            报告内容字符串
        """
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        content = f"""# PostgreSQL协议测试自动化报告

## 测试概览

- **测试时间**: {current_time}
- **测试目标**: 验证PostgreSQL协议pcap文件的解析能力和json事件生成
- **测试范围**: pgsql_pcaps 和 pgsql_abnormal_pcaps 目录下的所有pcap文件

## 测试统计

| 指标 | 数值 |
|------|------|
| 总测试文件数 | {total_files} |
| 成功解析文件数 | {success_files} |
| 失败文件数 | {failed_files} |
| 成功率 | {success_rate:.1f}% |

## 详细测试结果

"""

        # 按目录分组显示结果
        for directory, results in self.test_results.items():
            content += f"### {directory}\n\n"

            directory_success = sum(1 for r in results if r["status"] == "SUCCESS")
            directory_total = len(results)
            directory_rate = (directory_success / directory_total * 100) if directory_total > 0 else 0

            content += f"**目录统计**: {directory_success}/{directory_total} 成功 ({directory_rate:.1f}%)\n\n"

            # 测试结果表格
            content += "| 文件名 | 状态 | JSON事件 | Kafka事件对比 | 错误信息 | 耗时(秒) |\n"
            content += "|--------|------|----------|--------------|----------|----------|\n"

            for result in results:
                status_symbol = "✅" if result["status"] == "SUCCESS" else "❌"
                file_name = result["file_name"]
                status = result["status"]
                json_events = result.get("json_events", "无") or "无"
                error_msg = result.get("error_message", "无") or "无"
                duration = f"{result.get('duration', 0):.1f}"

                # Kafka事件对比结果
                kafka_comparison = result.get("kafka_event_comparison")
                if kafka_comparison:
                    if kafka_comparison.get("events_match", False):
                        kafka_status = f"✅ {kafka_comparison.get('summary', '匹配')}"
                    else:
                        kafka_status = f"⚠️ {kafka_comparison.get('summary', '不匹配')}"
                else:
                    kafka_status = "无对比"

                # 截断过长的内容
                if len(json_events) > 40:
                    json_events = json_events[:40] + "..."
                if len(error_msg) > 40:
                    error_msg = error_msg[:40] + "..."
                if len(kafka_status) > 40:
                    kafka_status = kafka_status[:40] + "..."

                content += f"| {file_name} | {status_symbol} {status} | {json_events} | {kafka_status} | {error_msg} | {duration} |\n"

            content += "\n"

        # 添加Kafka事件对比汇总
        content += "## Kafka事件对比分析\n\n"

        # 统计对比结果
        total_with_expected = 0
        successful_matches = 0
        comparison_details = []

        for directory_results in self.test_results.values():
            for result in directory_results:
                kafka_comparison = result.get("kafka_event_comparison")
                if kafka_comparison and kafka_comparison.get("has_expected", False):
                    total_with_expected += 1
                    if kafka_comparison.get("events_match", False):
                        successful_matches += 1
                    comparison_details.append({
                        "file_name": result["file_name"],
                        "comparison": kafka_comparison
                    })

        if total_with_expected > 0:
            match_rate = successful_matches / total_with_expected * 100
            content += f"**对比统计**: {successful_matches}/{total_with_expected} 个文件的Kafka事件与预期匹配 ({match_rate:.1f}%)\n\n"

            # 详细对比结果
            content += "### 详细对比结果\n\n"
            content += "| 文件名 | 事件数量对比 | 匹配度 | 关键字段状态 | 详细说明 |\n"
            content += "|--------|------------|--------|------------|----------|\n"

            for detail in comparison_details:
                file_name = detail["file_name"]
                comp = detail["comparison"]

                actual_count = comp.get("actual_event_count", 0)
                expected_count = comp.get("expected_event_count", 0)
                event_count_str = f"{actual_count}/{expected_count}"

                match_rate = comp.get("match_rate", 0) * 100 if comp.get("match_rate") else 0
                match_rate_str = f"{match_rate:.1f}%"

                events_match = comp.get("events_match", False)
                match_symbol = "✅" if events_match else "❌"

                summary = comp.get("summary", "无说明")
                if len(summary) > 30:
                    summary = summary[:30] + "..."

                # 检查关键字段匹配情况
                field_comparisons = comp.get("field_comparisons", [])
                critical_match = "✅" if all(fc.get("critical_fields_match", True) for fc in field_comparisons) else "❌"

                content += f"| {file_name} | {event_count_str} | {match_symbol} {match_rate_str} | {critical_match} | {summary} |\n"

            content += "\n"
        else:
            content += "**说明**: 当前测试中没有找到预期事件定义，无法进行对比分析。\n\n"

        # 添加失败文件汇总
        failed_results = []
        for directory_results in self.test_results.values():
            for result in directory_results:
                if result["status"] != "SUCCESS":
                    failed_results.append(result)

        if failed_results:
            content += "## 失败文件分析\n\n"

            # 按失败类型分组
            failure_types = {}
            for result in failed_results:
                failure_type = result["status"]
                if failure_type not in failure_types:
                    failure_types[failure_type] = []
                failure_types[failure_type].append(result)

            for failure_type, failures in failure_types.items():
                content += f"### {failure_type} ({len(failures)}个文件)\n\n"

                for result in failures:
                    content += f"- **{result['file_name']}**: {result.get('error_message', '未知错误')}\n"

                content += "\n"

        # 添加建议和总结
        content += "## 测试总结\n\n"

        if success_rate >= 90:
            content += "🎉 **测试结果优秀**: 大部分pcap文件都能正确解析并生成json事件。\n\n"
        elif success_rate >= 70:
            content += "✅ **测试结果良好**: 多数pcap文件能正确解析，但仍有改进空间。\n\n"
        else:
            content += "⚠️ **测试结果需要改进**: 较多pcap文件解析失败，需要进一步优化。\n\n"

        content += "### 改进建议\n\n"

        if failed_files > 0:
            content += "1. **失败文件分析**: 重点分析失败文件的协议格式和内容特征\n"
            content += "2. **协议解析优化**: 根据失败模式优化PostgreSQL协议解析器\n"
            content += "3. **错误处理增强**: 改进异常情况的处理和错误信息提供\n"

        content += "4. **持续监控**: 定期运行自动化测试，确保协议解析质量\n"
        content += "5. **测试扩展**: 考虑添加更多边界情况和异常场景的测试用例\n\n"

        content += f"---\n*报告生成时间: {current_time}*\n"

        return content


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="PostgreSQL协议测试自动化框架")
    parser.add_argument("--base-url", default="http://**************:8000",
                       help="gwhw_mcp服务的基础URL (默认: http://**************:8000)")
    parser.add_argument("--output", default="Unittest.md",
                       help="测试报告输出文件名 (默认: Unittest.md)")
    parser.add_argument("--single-file",
                       help="测试单个pcap文件")
    parser.add_argument("--no-json-files", action="store_true",
                       help="重新测试标记为'无JSON事件文件分析'的10个文件")
    parser.add_argument("--scan-only", action="store_true",
                       help="仅扫描文件，不执行测试")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="详细输出")
    parser.add_argument("--lost-packet-test", action="store_true",
                       help="运行丢包场景测试")

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 创建测试框架实例
    framework = PostgreSQLTestFramework(base_url=args.base_url)

    try:
        if args.scan_only:
            # 仅扫描文件
            logger.info("执行文件扫描...")

            if args.lost_packet_test:
                # 扫描丢包测试文件
                lost_pcap_files = framework.scan_lost_pcap_files()

                print("\n" + "="*60)
                print("PostgreSQL协议丢包测试文件扫描结果")
                print("="*60)

                if lost_pcap_files:
                    print(f"\n📁 {framework.lost_test_directory}: {len(lost_pcap_files)} 个丢包测试文件")
                    for file in lost_pcap_files:
                        loss_type = framework._extract_loss_type_from_filename(file)
                        print(f"  - {file} ({loss_type})")
                    print(f"\n📊 总计: {len(lost_pcap_files)} 个丢包测试pcap文件")
                else:
                    print("\n⚠️ 未找到任何丢包测试文件")
            else:
                # 扫描常规测试文件
                pcap_files = framework.scan_pcap_files()

                print("\n" + "="*60)
                print("PostgreSQL协议测试文件扫描结果")
                print("="*60)

                total_files = 0
                for directory, files in pcap_files.items():
                    print(f"\n📁 {directory}: {len(files)} 个文件")
                    for file in files:
                        print(f"  - {file}")
                    total_files += len(files)

                print(f"\n📊 总计: {total_files} 个pcap文件")

        elif args.single_file:
            # 测试单个文件
            logger.info(f"测试单个文件: {args.single_file}")
            expected_events = framework.load_expected_events()
            result = framework.test_single_pcap(args.single_file, expected_events)

            print("\n" + "="*60)
            print("单文件测试结果")
            print("="*60)
            print(f"文件名: {result['file_name']}")
            print(f"状态: {result['status']}")
            print(f"任务ID: {result.get('task_id', '无')}")
            print(f"耗时: {result.get('duration', 0):.1f}秒")

            if result.get('json_events'):
                print(f"JSON事件: {result['json_events']}")

            if result.get('kafka_event_comparison'):
                comp = result['kafka_event_comparison']
                print(f"Kafka事件对比: {comp.get('summary', '无')}")
                if comp.get('events_match'):
                    print(f"  ✅ 事件匹配成功 (匹配度: {comp.get('match_rate', 0)*100:.1f}%)")
                else:
                    print(f"  ❌ 事件匹配失败")

            if result.get('error_message'):
                print(f"错误信息: {result['error_message']}")

        elif args.no_json_files:
            # 测试标记为"无JSON事件文件分析"的10个文件
            no_json_files = [
                "pgsql_max_message_length.pcap",
                "pgsql_max_parameters.pcap",
                "pgsql_max_string_length.pcap",
                "pgsql_empty_data_handling.pcap",
                "pgsql_start_replication.pcap",
                "pgsql_wal_streaming.pcap",
                "pgsql_replication_feedback.pcap",
                "pgsql_ssl_request_supported.pcap",
                "pgsql_describe_statement.pcap",
                "pgsql_copy_to_stdout.pcap",
                "pgsql_close_statement_portal.pcap",
                "pgsql_nodata.pcap"
            ]

            logger.info(f"重新测试{len(no_json_files)}个无JSON事件文件...")

            print("\n" + "="*60)
            print("重新测试无JSON事件文件")
            print("="*60)
            print(f"服务地址: {args.base_url}")
            print(f"测试文件数: {len(no_json_files)}")
            print("="*60)

            # 运行指定文件测试
            results = framework.run_specific_tests(no_json_files)

            if results:
                # 生成专门的测试报告
                output_file = "no_json_events_retest.md"
                framework.generate_test_report(output_file)

                # 显示详细统计
                test_results = results["specific_tests"]
                success_files = sum(1 for result in test_results if result["status"] == "SUCCESS")
                no_json_files_count = sum(1 for result in test_results if result["status"] == "NO_JSON_EVENTS")
                failed_files = len(test_results) - success_files - no_json_files_count

                print("\n" + "="*60)
                print("重新测试结果统计")
                print("="*60)
                print(f"总测试文件: {len(test_results)}")
                print(f"成功生成JSON事件: {success_files}")
                print(f"仍无JSON事件: {no_json_files_count}")
                print(f"测试失败: {failed_files}")
                print(f"JSON事件生成率: {(success_files/len(test_results)*100):.1f}%")
                print(f"详细报告: {output_file}")
                print("="*60)

                # 显示每个文件的详细结果
                print("\n详细测试结果:")
                for result in test_results:
                    status_symbol = "✅" if result["status"] == "SUCCESS" else ("⚠️" if result["status"] == "NO_JSON_EVENTS" else "❌")
                    print(f"{status_symbol} {result['file_name']}: {result['status']}")
                    if result.get('json_events'):
                        print(f"   JSON事件: {result['json_events']}")
                    if result.get('error_message'):
                        print(f"   错误信息: {result['error_message']}")
                    print(f"   耗时: {result.get('duration', 0):.1f}秒")
                    print()
            else:
                logger.error("重新测试失败，未获得任何结果")
                sys.exit(1)

        elif args.lost_packet_test:
            # 运行丢包场景测试
            logger.info("开始执行丢包场景测试...")

            print("\n" + "="*60)
            print("PostgreSQL协议丢包测试自动化系统")
            print("="*60)
            print(f"服务地址: {args.base_url}")
            print(f"测试目录: pgsql_lost_pcaps")
            print("="*60)

            # 运行丢包测试
            lost_results = framework.run_lost_packet_tests()

            if lost_results:
                # 生成丢包测试报告
                framework.generate_lost_packet_test_report(lost_results)

                # 显示简要统计
                test_results = lost_results["lost_packet_tests"]
                success_files = sum(1 for result in test_results if result["status"] == "SUCCESS")
                total_files = len(test_results)

                print("\n" + "="*60)
                print("丢包测试完成统计")
                print("="*60)
                print(f"总测试文件: {total_files}")
                print(f"成功解析: {success_files}")
                print(f"失败文件: {total_files - success_files}")
                print(f"成功率: {(success_files/total_files*100):.1f}%")
                print(f"详细报告: report/Unittest_lost.md")
                print("="*60)

                # 显示每个场景的详细结果
                print("\n丢包场景测试结果:")
                for result in test_results:
                    status_symbol = "✅" if result["status"] == "SUCCESS" else "❌"
                    loss_type = framework._extract_loss_type_from_filename(result['file_name'])
                    print(f"{status_symbol} {result['file_name']} ({loss_type}): {result['status']}")
                    if result.get('json_events'):
                        print(f"   JSON事件: {result['json_events']}")
                    if result.get('error_message'):
                        print(f"   错误信息: {result['error_message']}")
                    print(f"   耗时: {result.get('duration', 0):.1f}秒")
                    print()

            else:
                logger.error("丢包测试失败，未获得任何结果")
                sys.exit(1)

        else:
            # 运行完整的批量测试
            logger.info("开始执行完整的批量测试...")

            print("\n" + "="*60)
            print("PostgreSQL协议测试自动化系统")
            print("="*60)
            print(f"服务地址: {args.base_url}")
            print(f"报告文件: {args.output}")
            print("="*60)

            # 运行批量测试
            results = framework.run_batch_tests()

            if results:
                # 生成测试报告
                framework.generate_test_report(args.output)

                # 显示简要统计
                total_files = sum(len(files) for files in results.values())
                success_files = sum(1 for directory_results in results.values()
                                  for result in directory_results
                                  if result["status"] == "SUCCESS")

                print("\n" + "="*60)
                print("测试完成统计")
                print("="*60)
                print(f"总测试文件: {total_files}")
                print(f"成功解析: {success_files}")
                print(f"失败文件: {total_files - success_files}")
                print(f"成功率: {(success_files/total_files*100):.1f}%")
                print(f"详细报告: {args.output}")
                print("="*60)

            else:
                logger.error("批量测试失败，未获得任何结果")
                sys.exit(1)

    except KeyboardInterrupt:
        logger.info("用户中断测试")
        sys.exit(0)
    except Exception as e:
        logger.error(f"测试过程中发生未知错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
