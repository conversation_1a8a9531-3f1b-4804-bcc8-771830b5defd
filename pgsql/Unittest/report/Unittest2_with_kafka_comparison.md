# PostgreSQL协议测试自动化报告

## 测试概览

- **测试时间**: 2025-08-05 17:04:02
- **测试目标**: 验证PostgreSQL协议pcap文件的解析能力和json事件生成
- **测试范围**: pgsql_pcaps 和 pgsql_abnormal_pcaps 目录下的所有pcap文件

## 测试统计

| 指标 | 数值 |
|------|------|
| 总测试文件数 | 49 |
| 成功解析文件数 | 47 |
| 失败文件数 | 2 |
| 成功率 | 95.9% |

## 详细测试结果

### ../pgsql_pcaps

**目录统计**: 36/38 成功 (94.7%)

| 文件名 | 状态 | JSON事件 | Kafka事件对比 | 错误信息 | 耗时(秒) |
|--------|------|----------|--------------|----------|----------|
| pgsql_auth_with_copy.pcap | ✅ SUCCESS | 包含2个JSON事件 - 命令类型: login, 状态: 0, 行数: 0 | ⚠️ 事件匹配度较低 (57.1%) | 无 | 11.2 |
| pgsql_authentication.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: login, 状态: 0, 行数: 0 | ✅ 事件匹配成功 (85.7%) | 无 | 11.2 |
| pgsql_batch_operations.pcap | ✅ SUCCESS | 包含5个JSON事件 - 命令类型: BEGIN, SQL: BEGIN;, 状... | ⚠️ 事件匹配度较低 (71.4%) | 无 | 11.1 |
| pgsql_binary_copy.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: COPY, SQL: COPY binar... | ✅ 事件匹配成功 (85.7%) | 无 | 16.2 |
| pgsql_binary_extended_query.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT i... | ✅ 事件匹配成功 (85.7%) | 无 | 11.1 |
| pgsql_cancel_query.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT *... | ⚠️ 事件匹配度较低 (71.4%) | 无 | 11.1 |
| pgsql_character_encoding.pcap | ✅ SUCCESS | 包含2个JSON事件 - 命令类型: SELECT, SQL: SELECT '... | ⚠️ 事件匹配度较低 (57.1%) | 无 | 11.2 |
| pgsql_cleartext_authentication.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: login, 状态: 0, 行数: 0 | ✅ 事件匹配成功 (85.7%) | 无 | 11.3 |
| pgsql_close_statement_portal.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT c... | ✅ 事件匹配成功 (85.7%) | 无 | 12.1 |
| pgsql_copy.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: COPY, SQL: COPY users... | ⚠️ 无预期事件定义 | 无 | 11.1 |
| pgsql_copy_both.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: login, 状态: 0, 行数: 0 | ⚠️ 事件匹配度较低 (71.4%) | 无 | 12.1 |
| pgsql_copy_from_stdin.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: COPY, SQL: COPY emplo... | ✅ 事件匹配成功 (85.7%) | 无 | 11.0 |
| pgsql_copy_to_stdout.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: COPY, SQL: COPY users... | ✅ 事件匹配成功 (85.7%) | 无 | 11.0 |
| pgsql_describe_portal.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT *... | ⚠️ 事件匹配度较低 (71.4%) | 无 | 11.1 |
| pgsql_describe_statement.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT i... | ⚠️ 事件匹配度较低 (71.4%) | 无 | 16.1 |
| pgsql_empty_data_handling.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT N... | ✅ 事件匹配成功 (85.7%) | 无 | 11.2 |
| pgsql_flush.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT v... | ✅ 事件匹配成功 (85.7%) | 无 | 11.1 |
| pgsql_function_call.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT O... | ⚠️ 事件匹配度较低 (71.4%) | 无 | 11.0 |
| pgsql_large_result.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT *... | ✅ 事件匹配成功 (85.7%) | 无 | 11.1 |
| pgsql_max_message_length.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT '... | ✅ 事件匹配成功 (85.7%) | 无 | 11.2 |
| pgsql_max_parameters.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: INSERT, SQL: INSERT I... | ⚠️ 事件匹配度较低 (71.4%) | 无 | 11.1 |
| pgsql_max_string_length.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT '... | ✅ 事件匹配成功 (85.7%) | 无 | 11.1 |
| pgsql_multi_parameter.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: INSERT, SQL: INSERT I... | ⚠️ 事件匹配度较低 (57.1%) | 无 | 11.3 |
| pgsql_multi_query.pcap | ✅ SUCCESS | 包含2个JSON事件 - 命令类型: SELECT, SQL: SELECT *... | ⚠️ 事件匹配度较低 (71.4%) | 无 | 11.1 |
| pgsql_multiple_data_types.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT 4... | ✅ 事件匹配成功 (85.7%) | 无 | 11.1 |
| pgsql_nodata.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: CREATE, SQL: CREATE T... | ⚠️ 事件匹配度较低 (71.4%) | 无 | 11.0 |
| pgsql_notice_response.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: CREATE, SQL: CREATE T... | ⚠️ 事件匹配度较低 (71.4%) | 无 | 11.0 |
| pgsql_notification.pcap | ✅ SUCCESS | 包含2个JSON事件 - 命令类型: LISTEN, SQL: LISTEN c... | ✅ 事件匹配成功 (85.7%) | 无 | 16.1 |
| pgsql_pipelined_queries.pcap | ✅ SUCCESS | 包含3个JSON事件 - 命令类型: SELECT, SQL: SELECT 1... | ✅ 事件匹配成功 (100.0%) | 无 | 11.4 |
| pgsql_prepared_statement.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT *... | ✅ 事件匹配成功 (100.0%) | 无 | 11.1 |
| pgsql_replication_feedback.pcap | ❌ NO_JSON_EVENTS | 无 | 无对比 | 任务完成但json_events为空 | 16.1 |
| pgsql_sasl_authentication.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: login, 状态: 0, 行数: 0 | ✅ 事件匹配成功 (85.7%) | 无 | 11.2 |
| pgsql_simple_query.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT *... | ✅ 事件匹配成功 (100.0%) | 无 | 11.1 |
| pgsql_ssl_request_not_supported.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: login, 状态: 0, 行数: 0 | ⚠️ 事件匹配度较低 (57.1%) | 无 | 11.1 |
| pgsql_start_replication.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: login, 状态: 0, 行数: 0 | ⚠️ 事件匹配度较低 (71.4%) | 无 | 16.1 |
| pgsql_sync_separated_transactions.pcap | ✅ SUCCESS | 包含3个JSON事件 - 命令类型: BEGIN, SQL: BEGIN;, 状... | ⚠️ 事件匹配度较低 (71.4%) | 无 | 11.4 |
| pgsql_transaction.pcap | ✅ SUCCESS | 包含3个JSON事件 - 命令类型: BEGIN, SQL: BEGIN;, 状... | ✅ 事件匹配成功 (85.7%) | 无 | 11.3 |
| pgsql_wal_streaming.pcap | ❌ NO_JSON_EVENTS | 无 | 无对比 | 任务完成但json_events为空 | 11.1 |

### ../pgsql_abnormal_pcaps

**目录统计**: 11/11 成功 (100.0%)

| 文件名 | 状态 | JSON事件 | Kafka事件对比 | 错误信息 | 耗时(秒) |
|--------|------|----------|--------------|----------|----------|
| pgsql_auth_failure.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: login, 状态: 28, 行数: 1 | ⚠️ 事件匹配度较低 (71.4%) | 无 | 11.1 |
| pgsql_constraint_violation.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: INSERT, SQL: INSERT I... | ⚠️ 事件匹配度较低 (57.1%) | 无 | 11.0 |
| pgsql_copy_fail.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: login, 状态: 22, 行数: 1 | ⚠️ 事件匹配度较低 (57.1%) | 无 | 11.1 |
| pgsql_error.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT *... | ✅ 事件匹配成功 (85.7%) | 无 | 11.0 |
| pgsql_fatal_error.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT *... | ⚠️ 事件匹配度较低 (57.1%) | 无 | 11.1 |
| pgsql_function_call_error.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT O... | ⚠️ 事件匹配度较低 (42.9%) | 无 | 16.1 |
| pgsql_panic_error.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT p... | ⚠️ 事件匹配度较低 (57.1%) | 无 | 11.0 |
| pgsql_pipelined_error_handling.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT 1... | ⚠️ 事件匹配度较低 (57.1%) | 无 | 11.1 |
| pgsql_syntax_error.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT n... | ⚠️ 事件匹配度较低 (71.4%) | 无 | 11.2 |
| pgsql_type_error.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: SELECT, SQL: SELECT '... | ⚠️ 事件匹配度较低 (57.1%) | 无 | 11.2 |
| pgsql_user_not_exist.pcap | ✅ SUCCESS | 包含1个JSON事件 - 命令类型: login, 状态: 28000, 行数:... | ⚠️ 事件匹配度较低 (57.1%) | 无 | 16.2 |

## Kafka事件对比分析

**对比统计**: 20/46 个文件的Kafka事件与预期匹配 (43.5%)

### 详细对比结果

| 文件名 | 事件数量对比 | 匹配度 | 关键字段状态 | 详细说明 |
|--------|------------|--------|------------|----------|
| pgsql_auth_with_copy.pcap | 2/1 | ❌ 57.1% | ❌ | 事件匹配度较低 (57.1%) |
| pgsql_authentication.pcap | 1/1 | ✅ 85.7% | ✅ | 事件匹配成功 (85.7%) |
| pgsql_batch_operations.pcap | 5/1 | ❌ 71.4% | ❌ | 事件匹配度较低 (71.4%) |
| pgsql_binary_copy.pcap | 1/1 | ✅ 85.7% | ✅ | 事件匹配成功 (85.7%) |
| pgsql_binary_extended_query.pcap | 1/1 | ✅ 85.7% | ✅ | 事件匹配成功 (85.7%) |
| pgsql_cancel_query.pcap | 1/1 | ❌ 71.4% | ❌ | 事件匹配度较低 (71.4%) |
| pgsql_character_encoding.pcap | 2/1 | ❌ 57.1% | ❌ | 事件匹配度较低 (57.1%) |
| pgsql_cleartext_authentication.pcap | 1/1 | ✅ 85.7% | ✅ | 事件匹配成功 (85.7%) |
| pgsql_close_statement_portal.pcap | 1/1 | ✅ 85.7% | ✅ | 事件匹配成功 (85.7%) |
| pgsql_copy_both.pcap | 1/1 | ❌ 71.4% | ❌ | 事件匹配度较低 (71.4%) |
| pgsql_copy_from_stdin.pcap | 1/1 | ✅ 85.7% | ✅ | 事件匹配成功 (85.7%) |
| pgsql_copy_to_stdout.pcap | 1/1 | ✅ 85.7% | ✅ | 事件匹配成功 (85.7%) |
| pgsql_describe_portal.pcap | 1/1 | ❌ 71.4% | ❌ | 事件匹配度较低 (71.4%) |
| pgsql_describe_statement.pcap | 1/1 | ❌ 71.4% | ✅ | 事件匹配度较低 (71.4%) |
| pgsql_empty_data_handling.pcap | 1/1 | ✅ 85.7% | ✅ | 事件匹配成功 (85.7%) |
| pgsql_flush.pcap | 1/1 | ✅ 85.7% | ✅ | 事件匹配成功 (85.7%) |
| pgsql_function_call.pcap | 1/1 | ❌ 71.4% | ❌ | 事件匹配度较低 (71.4%) |
| pgsql_large_result.pcap | 1/1 | ✅ 85.7% | ✅ | 事件匹配成功 (85.7%) |
| pgsql_max_message_length.pcap | 1/1 | ✅ 85.7% | ✅ | 事件匹配成功 (85.7%) |
| pgsql_max_parameters.pcap | 1/1 | ❌ 71.4% | ❌ | 事件匹配度较低 (71.4%) |
| pgsql_max_string_length.pcap | 1/1 | ✅ 85.7% | ✅ | 事件匹配成功 (85.7%) |
| pgsql_multi_parameter.pcap | 1/1 | ❌ 57.1% | ❌ | 事件匹配度较低 (57.1%) |
| pgsql_multi_query.pcap | 2/1 | ❌ 71.4% | ✅ | 事件匹配度较低 (71.4%) |
| pgsql_multiple_data_types.pcap | 1/1 | ✅ 85.7% | ✅ | 事件匹配成功 (85.7%) |
| pgsql_nodata.pcap | 1/1 | ❌ 71.4% | ✅ | 事件匹配度较低 (71.4%) |
| pgsql_notice_response.pcap | 1/1 | ❌ 71.4% | ✅ | 事件匹配度较低 (71.4%) |
| pgsql_notification.pcap | 2/1 | ✅ 85.7% | ✅ | 事件匹配成功 (85.7%) |
| pgsql_pipelined_queries.pcap | 3/1 | ✅ 100.0% | ✅ | 事件匹配成功 (100.0%) |
| pgsql_prepared_statement.pcap | 1/1 | ✅ 100.0% | ✅ | 事件匹配成功 (100.0%) |
| pgsql_sasl_authentication.pcap | 1/1 | ✅ 85.7% | ✅ | 事件匹配成功 (85.7%) |
| pgsql_simple_query.pcap | 1/1 | ✅ 100.0% | ✅ | 事件匹配成功 (100.0%) |
| pgsql_ssl_request_not_supported.pcap | 1/1 | ❌ 57.1% | ❌ | 事件匹配度较低 (57.1%) |
| pgsql_start_replication.pcap | 1/1 | ❌ 71.4% | ❌ | 事件匹配度较低 (71.4%) |
| pgsql_sync_separated_transactions.pcap | 3/1 | ❌ 71.4% | ❌ | 事件匹配度较低 (71.4%) |
| pgsql_transaction.pcap | 3/1 | ✅ 85.7% | ✅ | 事件匹配成功 (85.7%) |
| pgsql_auth_failure.pcap | 1/1 | ❌ 71.4% | ✅ | 事件匹配度较低 (71.4%) |
| pgsql_constraint_violation.pcap | 1/1 | ❌ 57.1% | ❌ | 事件匹配度较低 (57.1%) |
| pgsql_copy_fail.pcap | 1/1 | ❌ 57.1% | ❌ | 事件匹配度较低 (57.1%) |
| pgsql_error.pcap | 1/1 | ✅ 85.7% | ✅ | 事件匹配成功 (85.7%) |
| pgsql_fatal_error.pcap | 1/1 | ❌ 57.1% | ❌ | 事件匹配度较低 (57.1%) |
| pgsql_function_call_error.pcap | 1/1 | ❌ 42.9% | ❌ | 事件匹配度较低 (42.9%) |
| pgsql_panic_error.pcap | 1/1 | ❌ 57.1% | ❌ | 事件匹配度较低 (57.1%) |
| pgsql_pipelined_error_handling.pcap | 1/1 | ❌ 57.1% | ❌ | 事件匹配度较低 (57.1%) |
| pgsql_syntax_error.pcap | 1/1 | ❌ 71.4% | ❌ | 事件匹配度较低 (71.4%) |
| pgsql_type_error.pcap | 1/1 | ❌ 57.1% | ❌ | 事件匹配度较低 (57.1%) |
| pgsql_user_not_exist.pcap | 1/1 | ❌ 57.1% | ❌ | 事件匹配度较低 (57.1%) |

## 失败文件分析

### NO_JSON_EVENTS (2个文件)

- **pgsql_replication_feedback.pcap**: 任务完成但json_events为空
- **pgsql_wal_streaming.pcap**: 任务完成但json_events为空

## 测试总结

🎉 **测试结果优秀**: 大部分pcap文件都能正确解析并生成json事件。

### 改进建议

1. **失败文件分析**: 重点分析失败文件的协议格式和内容特征
2. **协议解析优化**: 根据失败模式优化PostgreSQL协议解析器
3. **错误处理增强**: 改进异常情况的处理和错误信息提供
4. **持续监控**: 定期运行自动化测试，确保协议解析质量
5. **测试扩展**: 考虑添加更多边界情况和异常场景的测试用例

---
*报告生成时间: 2025-08-05 17:04:02*
